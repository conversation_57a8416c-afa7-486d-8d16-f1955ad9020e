package actors.menu
{
   import actors.SoundManager;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import util.GameUtility;
   
   public class GameHelp extends MovieClip
   {
      
      public var btnBack:SimpleButton;
      
      public function GameHelp()
      {
         super();
         this.addEventListener(Event.ADDED_TO_STAGE,this.added,false,0,true);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.removed,false,0,true);
      }
      
      private function added(param1:*) : void
      {
         this.btnBack.addEventListener(MouseEvent.CLICK,this.backHandler);
         if(ThreeKingdoms._instance._gameQuality)
         {
            ThreeKingdoms._instance._gameQuality.visible = false;
         }
      }
      
      private function removed(param1:*) : void
      {
         this.btnBack.removeEventListener(MouseEvent.CLICK,this.backHandler);
      }
      
      private function backHandler(param1:*) : void
      {
         SoundManager.play("select");
         GameUtility.clearDisplayList(this);
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         if(ThreeKingdoms._instance._gameQuality)
         {
            ThreeKingdoms._instance._gameQuality.visible = true;
         }
      }
   }
}

