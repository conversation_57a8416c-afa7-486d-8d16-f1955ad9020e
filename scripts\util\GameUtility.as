package util
{
   import actors.roles.Role1;
   import actors.roles.Role2;
   import actors.roles.Role3;
   import flash.display.*;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.net.LocalConnection;
   import flash.utils.*;
   
   public class GameUtility
   {
      
      private static var _byte:ByteArray;
      
      public function GameUtility()
      {
         super();
      }
      
      public static function getClass(param1:String) : Class
      {
         return getDefinitionByName(param1) as Class;
      }
      
      public static function getObject(param1:String) : *
      {
         var _loc2_:Class = getDefinitionByName(param1) as Class;
         return new _loc2_();
      }
      
      public static function getLibraryObjectFromSWF(param1:String, param2:String) : *
      {
         var _loc3_:* = ThreeKingdoms._instance._loader.get("assets/" + param1);
         var _loc4_:Class = _loc3_.getDefinitionByName(param2) as Class;
         return new _loc4_();
      }
      
      public static function getLibraryObjectFromSWFByID(param1:String, param2:String) : *
      {
         var _loc3_:* = ThreeKingdoms._instance._loader.get(param1);
         var _loc4_:Class = _loc3_.getDefinitionByName(param2) as Class;
         return new _loc4_();
      }
      
      public static function getRoleInfo(param1:String, param2:uint) : *
      {
         var _loc3_:Class = getDefinitionByName(param1) as Class;
         return new _loc3_(param2);
      }
      
      public static function getObjectOfEnemy(param1:String, param2:Number = 0, param3:Number = 0) : *
      {
         var _loc4_:Class = getDefinitionByName(param1) as Class;
         return new _loc4_(param2,param3);
      }
      
      public static function getImageObject(param1:String) : Bitmap
      {
         var _loc2_:Class = getDefinitionByName(param1) as Class;
         var _loc3_:* = new _loc2_();
         return new Bitmap(_loc3_);
      }
      
      public static function getDistance(param1:DisplayObject, param2:DisplayObject) : Number
      {
         var _loc3_:Number = Math.abs(param1.x - param2.x);
         var _loc4_:Number = Math.abs(param1.y - param2.y);
         return Math.sqrt(_loc3_ * _loc3_ + _loc4_ * _loc4_);
      }
      
      public static function getNextPoint(param1:DisplayObject, param2:DisplayObject) : Point
      {
         var _loc3_:Number = param2.x - param1.x;
         var _loc4_:Number = param2.y - param1.y;
         var _loc5_:Number = Math.sqrt(_loc3_ * _loc3_ + _loc4_ * _loc4_);
         return new Point(_loc3_ / _loc5_,_loc4_ / _loc5_);
      }
      
      public static function getRandomNumber(param1:Number) : Boolean
      {
         var _loc2_:Number = param1 / 100;
         if(Math.random() < _loc2_)
         {
            return true;
         }
         return false;
      }
      
      public static function clone(param1:Object) : *
      {
         _byte = new ByteArray();
         _byte.writeObject(param1);
         _byte.position = 0;
         return _byte.readObject();
      }
      
      public static function setNumber(param1:Number) : Number
      {
         return param1 * 2 - 5;
      }
      
      public static function getNumber(param1:Number) : Number
      {
         return param1 * 0.5 + 5;
      }
      
      public static function clear() : void
      {
         if(_byte)
         {
            _byte.clear();
            _byte = null;
         }
      }
      
      public static function flipHorizontal(param1:DisplayObject, param2:int) : void
      {
         var _loc3_:Matrix = param1.transform.matrix;
         var _loc4_:* = _loc3_.a;
         _loc3_.a = param2 == 1 ? Math.abs(_loc4_) : -Math.abs(_loc4_);
         param1.transform.matrix = _loc3_;
      }
      
      public static function stopAllChildren(param1:DisplayObjectContainer) : void
      {
         var _loc3_:MovieClip = null;
         var _loc2_:int = 0;
         while(_loc2_ < param1.numChildren)
         {
            if(param1.getChildAt(_loc2_) is MovieClip)
            {
               _loc3_ = param1.getChildAt(_loc2_) as MovieClip;
               if(_loc3_.name != "mcLevelUp")
               {
                  _loc3_.stop();
                  stopAllChildren(_loc3_);
               }
            }
            _loc2_++;
         }
      }
      
      public static function startAllChildren(param1:DisplayObjectContainer) : void
      {
         var _loc3_:MovieClip = null;
         var _loc2_:int = 0;
         while(_loc2_ < param1.numChildren)
         {
            if(param1.getChildAt(_loc2_) is MovieClip)
            {
               _loc3_ = param1.getChildAt(_loc2_) as MovieClip;
               if(_loc3_.name != "mcLevelUp")
               {
                  _loc3_.play();
                  startAllChildren(_loc3_);
               }
            }
            _loc2_++;
         }
      }
      
      public static function center(param1:DisplayObject, param2:Stage) : *
      {
         param1.x = (param2.stageWidth - param1.width) / 2;
         param1.y = (param2.stageHeight - param1.height) / 2;
      }
      
      public static function clearDisplayList(param1:DisplayObjectContainer) : void
      {
         var _loc2_:DisplayObject = null;
         var _loc3_:Number = param1.numChildren - 1;
         while(_loc3_ >= 0)
         {
            _loc2_ = param1.getChildAt(_loc3_);
            if(_loc2_)
            {
               if(!(_loc2_ is Role1 || _loc2_ is Role2 || _loc2_ is Role3))
               {
                  if(_loc2_.visible == false)
                  {
                     if(_loc2_ is DisplayObjectContainer)
                     {
                        if(_loc2_ is Sprite)
                        {
                           Sprite(_loc2_).graphics.clear();
                        }
                        clearDisplayList(DisplayObjectContainer(_loc2_));
                     }
                  }
                  param1.removeChild(_loc2_);
               }
            }
            _loc3_--;
         }
      }
      
      public static function GC() : *
      {
         var _loc1_:LocalConnection = null;
         var _loc2_:LocalConnection = null;
         try
         {
            _loc1_ = new LocalConnection();
            _loc2_ = new LocalConnection();
            _loc1_.connect("ThreeKingdoms_4399_20111104");
            _loc2_.connect("ThreeKingdoms_4399_20111104");
         }
         catch(e:Error)
         {
         }
      }
   }
}

