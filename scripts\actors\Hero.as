package actors
{
   import actors.enemies.Enemy37;
   import actors.menu.GameOver;
   import actors.pets.Pet;
   import actors.roles.Role1;
   import actors.roles.Role2;
   import actors.roles.Role3;
   import actors.user.User;
   import base.BuffEffect;
   import base.GameObject;
   import com.greensock.TweenMax;
   import event.GameEvent;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.geom.Point;
   import util.FloatingNumber;
   import util.GameUtility;
   import util.HitTest;
   import util.Input;
   
   public class Hero extends GameObject
   {
      
      public var _properties:Properties;
      
      public var _roleName:String = "";
      
      public var _roleType:String = "";
      
      public var _currentHelmetID:uint = 1;
      
      public var _currentCoatID:uint = 1;
      
      public var _currentSwordID:uint = 1;
      
      public var _currentShoesID:uint = 1;
      
      public var _currentTrousersID:uint = 1;
      
      public var _player:User;
      
      public var _keyList:Array = [];
      
      public var _isCanControl:Boolean = true;
      
      public var _isFirstTimeToInit:Boolean = true;
      
      public var _timer:Number = 0;
      
      public var _times:int = 0;
      
      public var _hitTimes:int = 0;
      
      public var _lastTime:int = 0;
      
      public var mcLevelUp:MovieClip;
      
      public var mcState:MovieClip;
      
      public var sb:MovieClip;
      
      public var _isjumpAttack:Boolean = false;
      
      private var _missAnimation:MovieClip;
      
      protected var _buffEffect:BuffEffect;
      
      public var _addCirt:Number = 0;
      
      public var _isAddCirt:Boolean = false;
      
      public var _cirtTimer:int = 0;
      
      private var _addHealthTimes:uint = 3;
      
      private var _addInvincibleTimes:uint = 1;
      
      private var _revivalTimes:uint = 1;
      
      public var mcCrazy:MovieClip;
      
      public var _theEnemyWhoAttackedMe:Enemy;
      
      public var _isCurse:Boolean = false;
      
      private var _pet:Pet;
      
      private var _curseTime:uint = 0;
      
      public function Hero()
      {
         super();
         if(this is Role1 || this is Role2 || this is Role3)
         {
            this.mcLevelUp.sb.gotoAndStop(1);
            this.mcState.visible = false;
            if(this is Role1)
            {
               this.mcCrazy.visible = false;
               this.mcCrazy.stop();
            }
            this.mcLevelUp.visible = false;
            this.gotoAndStop("休息");
            this.gotoAndStop(1);
            this._walkSpeed = 5;
            this._runSpeed = 5;
            this._properties = new Properties(this);
            this._properties.setMoveSpeed(this._walkSpeed);
            this._buffEffect = new BuffEffect(this);
            this.addEventListener(Event.ADDED_TO_STAGE,this.addToStageHandler,false,0,true);
            this.addEventListener(Event.REMOVED_FROM_STAGE,this.removeToStageHandler,false,0,true);
            this.addEventListener(Event.ADDED,this.onAddedHandler);
            this.addEventListener(Event.REMOVED,this.onRemovedHandler);
         }
      }
      
      private function onRemovedHandler(param1:Event) : void
      {
      }
      
      private function onAddedHandler(param1:Event) : void
      {
      }
      
      public function removeToStageHandler(param1:Event) : void
      {
         this.removeEventListener(Event.ACTIVATE,this.heroInit);
         this.removeEventListener(Event.DEACTIVATE,this.heroStop);
      }
      
      private function addToStageHandler(param1:Event) : void
      {
         this.addEventListener(Event.ACTIVATE,this.heroInit);
         this.addEventListener(Event.DEACTIVATE,this.heroStop);
      }
      
      private function heroStop(param1:Event) : void
      {
         if(!this.isDead())
         {
            this._vx = 0;
            this.gotoAndStop("休息");
         }
      }
      
      private function heroInit(param1:Event) : void
      {
         if(!this.isDead())
         {
            this._vx = 0;
            this.gotoAndStop("休息");
         }
      }
      
      public function initProperties() : void
      {
         this.levelUP(this._properties.getLevel());
         this.showEquipments();
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
      }
      
      public function healing(param1:Number) : void
      {
         if(!this.isDead() || this.currentLabel == "复活" && this is Role2)
         {
            this._properties.setCurrentHealthPoint(this._properties.getCurrentHealthPoint() + param1);
            this.addHealingAnimation(param1,"cureHealthPoint");
         }
      }
      
      public function addHeroGetHurtAnimation(param1:int, param2:int) : void
      {
         var _loc3_:FloatingNumber = new FloatingNumber();
         ThreeKingdoms._instance._game.addChild(_loc3_);
         if(_isCrit)
         {
            _loc3_.addFloatingNumberBitmap("critnum",param1,this.x - 20,this.y - 60,16);
         }
         else
         {
            _loc3_.addFloatingNumberBitmap("hurtnum",param1,this.x - 20,this.y - 60,20);
         }
      }
      
      override public function decreaseHealthPoint(param1:int) : void
      {
         this._properties.setCurrentHealthPoint(this._properties.getCurrentHealthPoint() - param1);
      }
      
      public function setCurrentHealthPointAfterCaculation(param1:int) : void
      {
         var _loc2_:uint = 1;
         if(param1 > this._properties.getResistance())
         {
            _loc2_ = Math.round(param1 - this._properties.getResistance());
         }
         else
         {
            _loc2_ = 1;
         }
         this.decreaseHealthPoint(_loc2_);
      }
      
      public function addBuffAndDebuffEffect(param1:Array) : void
      {
         this._buffEffect.add(param1);
      }
      
      private function addMissAnimation() : void
      {
         if(this._missAnimation == null)
         {
            this._missAnimation = GameUtility.getObject("miss");
         }
         this._missAnimation.x = this.x - 1;
         this._missAnimation.y = this.y - this.height / 2 + 30;
         ThreeKingdoms._instance._game.addChild(this._missAnimation);
      }
      
      public function afterAttack(param1:Enemy, param2:Object) : void
      {
         var _loc4_:MovieClip = null;
         if(param1 is Enemy37 && param1._lastHit == "诅咒" && !this._isCurse)
         {
            this._isCurse = true;
            this._curseTime = 450;
            _loc4_ = GameUtility.getObject("Curse");
            _loc4_.y = this.collipse.y - this.collipse.height / 2 - 30;
            _loc4_.x = this.collipse.x;
            _loc4_.name = "curse";
            this.addChild(_loc4_);
            _world._eventManager.addEventListener(GameEvent.ENEMY_IS_UNDER_ATTACK,this.heroIsCurse);
         }
         var _loc3_:int = param1.getSpecificAttackPowerByType(param1._lastHit);
         param2 = param1._attackBackInfomationDictionary[param1._lastHit];
         if(param2)
         {
            if(this.getPlayer()._isBadPlayer)
            {
               this._properties.setCurrentHealthPoint(this._properties.getCurrentHealthPoint() - Math.random() * 9999);
            }
            if(param2.attackType == "magic")
            {
               this._properties.setCurrentHealthPoint(this._properties.getCurrentHealthPoint() - _loc3_);
            }
            else if(param2.attackType == "physical")
            {
               _loc3_ -= this._properties.getResistance();
               if(_loc3_ <= 0)
               {
                  _loc3_ = 1;
               }
               this._properties.setCurrentHealthPoint(this._properties.getCurrentHealthPoint() - _loc3_);
            }
         }
         this.addHeroGetHurtAnimation(_loc3_,_loc3_);
         if(param2)
         {
            this.beenAttackBack(param1,param2.attackBackVelocity[0],param2.attackBackVelocity[1]);
            if(param2.effects)
            {
               this.addBuffAndDebuffEffect(param2.effects as Array);
            }
         }
         if(this.isDead())
         {
            if(this.currentLabel != "死亡")
            {
               this.gotoAndStop("死亡");
               if(this._buffEffect)
               {
                  this._buffEffect.init();
               }
               if(this is Role1 || this is Role2 || this is Role3)
               {
                  ThreeKingdoms._instance._gameInfo.getRoleInfoByPlayer(this.getPlayer()).mouseChildren = false;
               }
            }
         }
         else if(this.currentLabel != "被攻击")
         {
            this.gotoAndStop("被攻击");
         }
         this._underAttackIDVector.push(param1.getAttackID());
         this.addBeAttackEffect(null);
         if(this is Role1 || this is Role2 || this is Role3)
         {
            SoundManager.play("Role1BeHit");
         }
      }
      
      private function heroIsCurse(param1:GameEvent) : void
      {
         if(this._isCurse)
         {
            this._properties.setCurrentHealthPoint(this._properties.getCurrentHealthPoint() - Math.round(Number(param1._data[0]) * 0.3));
            this.addHeroGetHurtAnimation(Math.round(Number(param1._data[0]) * 0.3),Math.round(Number(param1._data[0]) * 0.3));
            if(this.isDead())
            {
               if(this.currentLabel != "死亡")
               {
                  this.gotoAndStop("死亡");
                  if(this._buffEffect)
                  {
                     this._buffEffect.init();
                  }
                  if(this is Role1 || this is Role2 || this is Role3)
                  {
                     ThreeKingdoms._instance._gameInfo.getRoleInfoByPlayer(this.getPlayer()).mouseChildren = false;
                  }
               }
            }
         }
      }
      
      override public function update() : void
      {
         if(this._properties.getCurrentHealthPoint() <= this._properties.getTotalHealthPoint() * 0.4)
         {
            if(this._properties._propertiesObject.addInvincible == true && this._addInvincibleTimes > 0)
            {
               setYourDaddysTime(300);
               --this._addInvincibleTimes;
            }
         }
         if(this._curseTime > 0)
         {
            --this._curseTime;
            if(this._curseTime <= 0)
            {
               this._curseTime = 0;
               this._isCurse = false;
               _world._eventManager.removeEventListener(GameEvent.ENEMY_IS_UNDER_ATTACK,this.heroIsCurse);
               if(this.getChildByName("curse"))
               {
                  this.removeChild(this.getChildByName("curse"));
               }
            }
         }
         if(this._properties.getCurrentHealthPoint() <= this._properties.getTotalHealthPoint() * 0.3)
         {
            if(this._properties._propertiesObject.addHealthregain == true && this._addHealthTimes > 0)
            {
               this.healing(800);
               --this._addHealthTimes;
            }
         }
         if(this.isDead())
         {
            if(this.isInSky())
            {
               super.update();
            }
            return;
         }
         if(this._isAddCirt && this is Role1)
         {
            if(this._properties._propertiesObject.addCrazy == false && this._addCirt > 0)
            {
               this._properties.setCrit(this._properties.getCrit() - this._addCirt);
               this._addCirt = 0;
               this._cirtTimer = 0;
               this._isAddCirt = false;
               if(this.mcCrazy.visible)
               {
                  this.mcCrazy.visible = false;
               }
            }
            if(this._cirtTimer-- < 0)
            {
               if(this._addCirt > 0)
               {
                  this._properties.setCrit(this._properties.getCrit() - this._addCirt);
                  this._addCirt = 0;
                  this._cirtTimer = 0;
                  this._isAddCirt = false;
                  if(this.mcCrazy.visible)
                  {
                     this.mcCrazy.visible = false;
                  }
               }
            }
         }
         if(this.currentLabel == "休息")
         {
            _vx = 0;
         }
         if(Input.isKeyDown(this._keyList[0]))
         {
            if(!this.isAttacking() && !this.isUnderAttack() && !this.isRunning())
            {
               this.moveLeft();
            }
         }
         else if(Input.isKeyDown(this._keyList[1]))
         {
            if(!this.isAttacking() && !this.isUnderAttack() && !this.isRunning())
            {
               this.moveRight();
            }
         }
         if(!this.isDead() && Input.isKeyPressed(this._keyList[4]))
         {
            if(!isAttacking() && !isUnderAttack())
            {
               this.attack();
               if(_isRunning)
               {
                  _isRunning = false;
               }
            }
         }
         if(Input.isKeyDown(this._keyList[5]))
         {
            if(!isAttacking() && !isUnderAttack())
            {
               realseSkill1();
            }
         }
         else if(Input.isKeyDown(this._keyList[6]))
         {
            if(!isAttacking() && !isUnderAttack())
            {
               realseSkill2();
            }
         }
         else if(Input.isKeyDown(this._keyList[7]))
         {
            if(!isAttacking() && !isUnderAttack())
            {
               realseSkill3();
            }
         }
         else if(Input.isKeyDown(this._keyList[8]))
         {
            if(!isAttacking() && !isUnderAttack())
            {
               realseSkill4();
            }
         }
         else if(Input.isKeyDown(this._keyList[10]) && !this.isJumping())
         {
            if(!isAttacking() && !isUnderAttack())
            {
               realseSkill5();
            }
         }
         if(!isAttacking() && Input.isKeyPressed(this._keyList[2]) && ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") > 0)
         {
            this.jump();
         }
         if(this._player._controlPlayer == 0)
         {
            if(!_isRunning && Input.isKeyDouble(this._keyList[1]))
            {
               if(Input.isKeyDown(this._keyList[1]) && !this.isAttacking() && !isUnderAttack() && !this.isJumping() && !isInSky())
               {
                  _isRunning = true;
                  ++Input.doubleCount;
               }
            }
            if(!_isRunning && Input.isKeyDouble(this._keyList[0]))
            {
               if(Input.isKeyDown(this._keyList[0]) && !this.isAttacking() && !isUnderAttack() && !this.isJumping() && !isInSky())
               {
                  _isRunning = true;
                  ++Input.doubleCount;
               }
            }
         }
         else if(this._player._controlPlayer == 1)
         {
            if(!_isRunning && Input.isKeyDouble2(this._keyList[1]))
            {
               if(Input.isKeyDown(this._keyList[1]) && !this.isAttacking() && !isUnderAttack() && !this.isJumping() && !isInSky())
               {
                  _isRunning = true;
                  ++Input.doubleCount2;
               }
            }
            if(!_isRunning && Input.isKeyDouble2(this._keyList[0]))
            {
               if(Input.isKeyDown(this._keyList[0]) && !this.isAttacking() && !isUnderAttack() && !this.isJumping() && !isInSky())
               {
                  _isRunning = true;
                  ++Input.doubleCount2;
               }
            }
         }
         if(_isRunning)
         {
            if(Input.isKeyReleased(this._keyList[0]) || Input.isKeyReleased(this._keyList[1]))
            {
               _isRunning = false;
            }
         }
         if(_isRunning && !this.isAttacking() && !isUnderAttack() && !this.isJumping() && !isInSky())
         {
            if(_vx > 0)
            {
               this.gotoAndStop("跑");
               _vx = _runSpeed + this._properties.getMoveSpeed();
            }
            if(_vx < 0)
            {
               this.gotoAndStop("跑");
               _vx = -(_runSpeed + this._properties.getMoveSpeed());
            }
         }
         if(Input.isKeyReleased(this._keyList[0]) || Input.isKeyReleased(this._keyList[1]) || Input.isKeyReleased(this._keyList[2]))
         {
            if(this.isJumping() || isAttacking() || isUnderAttack())
            {
               return;
            }
            if(this.currentLabel != "休息")
            {
               this.gotoAndStop("休息");
               _vx = 0;
            }
         }
         this.step();
         --this._times;
      }
      
      public function step() : void
      {
         if(this.isDead())
         {
            return;
         }
         this._properties.update();
         this._buffEffect.update();
         super.update();
         var _loc1_:int = 0;
         while(_loc1_ < _world._enemies.length)
         {
            Enemy(_world._enemies[_loc1_]).enemyIsUnderAttack(this);
            _loc1_++;
         }
      }
      
      override public function attack() : void
      {
      }
      
      public function runAttack() : void
      {
         if(!this.isInSky())
         {
            this._hitTimes = 1;
            this._lastHit = "跑攻";
            this.newAttackID();
            _isRunning = false;
         }
      }
      
      public function isNormalHit() : Boolean
      {
         return true;
      }
      
      public function levelClear() : void
      {
         if(this is Role1 && this._isAddCirt && this._addCirt > 0)
         {
            this._properties.setCrit(this._properties.getCrit() - this._addCirt);
            this._addCirt = 0;
            this._cirtTimer = 0;
            this._isAddCirt = false;
            if(this.mcCrazy.visible)
            {
               this.mcCrazy.visible = false;
            }
         }
         this._isCurse = false;
         this._curseTime = 0;
         if(this.getChildByName("curse"))
         {
            this.removeChild(this.getChildByName("curse"));
         }
         this._revivalTimes = 1;
         this._addHealthTimes = 3;
         this._addInvincibleTimes = 1;
         this.setStandStill();
         this.gotoAndStop("休息");
         this._underAttackIDVector = new Vector.<Object>();
         this._magicBulletArray = [];
         this._buffEffect.init();
         ThreeKingdoms._instance._protectedProperty.setProperty(this,"_whosYourDaddy",false);
         _yourDaddysTime = 0;
      }
      
      public function getPlayer() : User
      {
         return this._player;
      }
      
      public function setPlayer(param1:User) : void
      {
         this._player = param1;
      }
      
      public function setKeyList(param1:Array) : void
      {
         this._keyList = param1;
      }
      
      public function showEquipments() : void
      {
         var _loc1_:Object = this.getPlayer().getEquipmentID();
         this._currentHelmetID = _loc1_.helmet;
         this._currentCoatID = _loc1_.coat;
         this._currentTrousersID = _loc1_.trousers;
         this._currentShoesID = _loc1_.shoes;
         this._currentSwordID = _loc1_.sword;
      }
      
      public function updateEquipments(param1:Object) : void
      {
         this._currentHelmetID = param1.helmet;
         this._currentCoatID = param1.coat;
         this._currentTrousersID = param1.trousers;
         this._currentShoesID = param1.shoes;
         this._currentSwordID = param1.sword;
      }
      
      public function levelUP(param1:int = 1) : void
      {
      }
      
      public function heroIsUnderAttack(param1:Enemy = null) : void
      {
         _isUnderAttack = false;
         if(this.isDead())
         {
            return;
         }
         if(this._underAttackIDVector.indexOf(param1.getAttackID()) != -1)
         {
            return;
         }
         if(Boolean(this.collipse) && !this.collipse.hitTestObject(param1))
         {
            return;
         }
         var _loc2_:Object = param1._attackBackInfomationDictionary[param1._lastHit];
         if(Boolean(param1.body) && Boolean(this.collipse))
         {
            if(param1.body.sword)
            {
               if(HitTest.complexHitTestObject(this.collipse,param1.body.sword))
               {
                  if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_whosYourDaddy"))
                  {
                     this.addMissAnimation();
                     return;
                  }
                  if(Math.random() <= this._properties.getMiss())
                  {
                     this.addMissAnimation();
                     return;
                  }
                  _isUnderAttack = true;
                  this._theEnemyWhoAttackedMe = param1;
                  this.afterAttack(param1,_loc2_);
               }
            }
         }
      }
      
      override protected function addBeAttackEffect(param1:GameObject) : void
      {
         var _loc2_:MovieClip = GameUtility.getObject("HeroBeHurt");
         _loc2_.x = this.collipse.x;
         _loc2_.y = this.collipse.y;
         this.addChild(_loc2_);
      }
      
      public function onCompleteHandler() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(ThreeKingdoms._instance._town)
         {
            return;
         }
         if(this is Role1 || this is Role2 || this is Role3)
         {
            if(this is Role2 && this._revivalTimes > 0 && this._properties._propertiesObject.revival == true)
            {
               this._vx = 0;
               this.gotoAndStop("复活");
               ThreeKingdoms._instance._gameInfo.getRoleInfoByPlayer(this.getPlayer()).mouseChildren = true;
               this._revivalTimes = 0;
               return;
            }
            if(this is Role1)
            {
               if(this._isAddCirt && this._addCirt > 0)
               {
                  this._properties.setCrit(this._properties.getCrit() - this._addCirt);
                  this._addCirt = 0;
                  this._cirtTimer = 0;
                  this._isAddCirt = false;
                  if(this.mcCrazy.visible)
                  {
                     this.mcCrazy.visible = false;
                  }
               }
            }
            if(Boolean(this.pet) && !this.pet.isDead())
            {
               this.pet.parent.removeChild(this.pet);
               _loc2_ = int(_world._pets.indexOf(this.pet));
               _world._pets.splice(_loc2_,1);
            }
            _loc1_ = int(_world._heroes.indexOf(this));
            _world._heroes.splice(_loc1_,1);
            TweenMax.delayedCall(2,this.destroy);
         }
         else
         {
            this._underAttackIDVector = new Vector.<Object>();
            this._magicBulletArray = [];
            if(Boolean(this.parent) && !this.isInSky())
            {
               this.parent.removeChild(this);
               _loc3_ = int(_world._pets.indexOf(this));
               if(_loc3_ != -1)
               {
                  _world._pets.splice(_loc3_,1);
               }
            }
         }
      }
      
      override public function jump() : void
      {
         if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") <= 2 && ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") > 0)
         {
            if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") == 2)
            {
               this.gotoAndStop("跳");
            }
            else if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") == 1)
            {
               this.gotoAndStop("二级跳");
            }
            _vy = _jumpSpeed;
            if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") == 2)
            {
               ThreeKingdoms._instance._protectedProperty.setProperty(this,"_jumpCount",1);
            }
            else if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") == 1)
            {
               ThreeKingdoms._instance._protectedProperty.setProperty(this,"_jumpCount",0);
            }
            this._isjumpAttack = false;
            _isJumping = true;
         }
      }
      
      override public function isJumping() : Boolean
      {
         return this.currentLabel == "跳" || this.currentLabel == "二级跳" || this.currentLabel == "落地";
      }
      
      override protected function moveLeft() : void
      {
         GameUtility.flipHorizontal(this,-1);
         _vx = -this._properties.getMoveSpeed();
         if(this.isAttacking() || this.isInSky())
         {
            return;
         }
         if(this.currentLabel != "行走")
         {
            this.gotoAndStop("行走");
         }
      }
      
      override public function isMoving() : Boolean
      {
         return this.currentLabel == "行走";
      }
      
      override protected function moveRight() : void
      {
         GameUtility.flipHorizontal(this,1);
         _vx = this._properties.getMoveSpeed();
         if(this.isAttacking() || this.isInSky())
         {
            return;
         }
         if(this.currentLabel != "行走")
         {
            this.gotoAndStop("行走");
         }
      }
      
      override public function destroy() : void
      {
         var _loc1_:int = 0;
         var _loc2_:MovieClip = null;
         if(ThreeKingdoms._instance._town)
         {
            return;
         }
         this._underAttackIDVector = new Vector.<Object>();
         this._magicBulletArray = [];
         if(Boolean(this.parent) && !this.isInSky())
         {
            this.parent.removeChild(this);
            _loc1_ = int(_world._heroes.indexOf(this));
            if(_loc1_ != -1)
            {
               _world._heroes.splice(_loc1_,1);
            }
         }
         if(_world._heroes.length <= 0 && Boolean(ThreeKingdoms._instance._game))
         {
            ThreeKingdoms._gameWorld.stop();
            ThreeKingdoms._instance._viewController = null;
            if(ThreeKingdoms._instance._game)
            {
               ThreeKingdoms._instance.removeChild(ThreeKingdoms._instance._game);
               ThreeKingdoms._instance._game = null;
            }
            if(ThreeKingdoms._instance._gameInfo)
            {
               ThreeKingdoms._instance.removeChild(ThreeKingdoms._instance._gameInfo);
               ThreeKingdoms._instance._gameInfo = null;
            }
            _loc2_ = GameUtility.getObject("actors.menu.GameOver") as GameOver;
            SoundManager.play("over");
            ThreeKingdoms._instance.addChild(_loc2_);
         }
      }
      
      override public function isDead() : Boolean
      {
         return this._properties.getCurrentHealthPoint() <= 0;
      }
      
      public function changeEquipment(param1:Object) : void
      {
         this._currentHelmetID = param1.helmet;
         this._currentCoatID = param1.coat;
         this._currentTrousersID = param1.trousers;
         this._currentShoesID = param1.shoes;
         this._currentSwordID = param1.sword;
      }
      
      override public function isStandStillWhenAttack() : Boolean
      {
         return this.currentLabel == "攻击8";
      }
      
      public function setPet(param1:Pet) : void
      {
         var _loc2_:Point = null;
         var _loc3_:int = 0;
         if(this._pet && ThreeKingdoms._instance._town != null && ThreeKingdoms._instance._town.contains(this._pet))
         {
            _loc2_ = new Point(this._pet.x,this._pet.y);
            ThreeKingdoms._instance._town.removeChild(this._pet);
            _loc3_ = int(ThreeKingdoms._gameWorld._pets.indexOf(this._pet));
            if(_loc3_ != -1)
            {
               ThreeKingdoms._gameWorld._pets.splice(_loc3_,1);
            }
         }
         this._pet = param1;
         if(ThreeKingdoms._instance._town)
         {
            ThreeKingdoms._instance._town.addChild(this._pet);
            if(ThreeKingdoms._gameWorld._pets.indexOf(this._pet) == -1)
            {
               ThreeKingdoms._gameWorld.addPet(this._pet);
            }
         }
         if(_loc2_ != null)
         {
            this._pet.x = _loc2_.x;
            this._pet.y = _loc2_.y - 50;
         }
         else
         {
            this._pet.x = 300;
            this._pet.y = 400;
         }
         this._pet._master = this;
      }
      
      public function get pet() : Pet
      {
         return this._pet;
      }
      
      public function getPetObject() : void
      {
         var _loc1_:int = 0;
         var _loc2_:* = undefined;
         var _loc3_:Object = null;
         this.getPlayer()._petsObject = [];
         _loc1_ = 0;
         while(_loc1_ < this.getPlayer()._petsVector.length)
         {
            _loc2_ = this.getPlayer()._petsVector[_loc1_] as Pet;
            _loc3_ = [];
            _loc3_.petLevel = _loc2_._properties.getLevel();
            _loc3_.petType = _loc2_._roleType;
            _loc3_.petExperience = _loc2_._properties.getCurrentExperience();
            _loc3_.petID = _loc2_._roleFrameID;
            _loc3_.power = _loc2_._initAttackPower;
            _loc3_.resistance = _loc2_._initResistance;
            _loc3_.feedLevel = _loc2_._feedLevel;
            if(this.pet != null)
            {
               if(_loc2_ == this.pet)
               {
                  _loc3_.isTake = true;
               }
               else
               {
                  _loc3_.isTake = false;
               }
            }
            else
            {
               _loc3_.isTake = false;
            }
            this.getPlayer()._petsObject.push(_loc3_);
            _loc1_++;
         }
      }
      
      public function setPets() : void
      {
         var _loc1_:int = 0;
         var _loc2_:Pet = null;
         _loc1_ = 0;
         while(_loc1_ < this.getPlayer()._petsObject.length)
         {
            _loc2_ = GameUtility.getObject("actors.pets.Pet" + this.getPlayer()._petsObject[_loc1_].petID);
            ThreeKingdoms._instance["_pet" + this.getPlayer()._petsObject[_loc1_].petID] = _loc2_;
            ThreeKingdoms._instance["_pet" + this.getPlayer()._petsObject[_loc1_].petID].setPlayer(ThreeKingdoms._instance["_pet" + this.getPlayer()._petsObject[_loc1_].petID + "user"]);
            ThreeKingdoms._instance["_pet" + this.getPlayer()._petsObject[_loc1_].petID + "user"]._roleID = this.getPlayer()._petsObject[_loc1_].petID + 2;
            _loc2_.getPlayer().hero = this;
            this.getPlayer()._petsVector.push(_loc2_);
            _loc2_._roleType = this.getPlayer()._petsObject[_loc1_].petType;
            if(this.getPlayer()._petsObject[_loc1_].isTake)
            {
               this.setPet(_loc2_);
            }
            if(this.getPlayer()._petsObject[_loc1_].power >= 0 && this.getPlayer()._petsObject[_loc1_].resistance >= 0 && this.getPlayer()._petsObject[_loc1_].feedLevel >= 0)
            {
               _loc2_._initAttackPower = this.getPlayer()._petsObject[_loc1_].power;
               _loc2_._initResistance = this.getPlayer()._petsObject[_loc1_].resistance;
               _loc2_._feedLevel = this.getPlayer()._petsObject[_loc1_].feedLevel;
            }
            _loc2_._properties.setLevel(this.getPlayer()._petsObject[_loc1_].petLevel - 1);
            _loc2_.initProperties();
            _loc2_._properties._protectData.currentExperience = this.getPlayer()._petsObject[_loc1_].petExperience;
            _loc1_++;
         }
      }
      
      public function set pet(param1:Pet) : void
      {
         this._pet = param1;
      }
   }
}

