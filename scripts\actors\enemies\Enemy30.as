package actors.enemies
{
   import actors.Enemy;
   import actors.Hero;
   import util.GameUtility;
   
   public class Enemy30 extends Enemy
   {
      
      public function Enemy30(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 1000;
         this._object._attackRange = 500;
         this._object._alertRange = 1000;
         this._goldPrice = 2000;
         this.isBoss = false;
         this._walkSpeed = 6;
         this.enemyName = "貂蝉";
         this._currentHealthPoint = 100000;
         this._totalHealthPoint = 100000;
         this._attackProbablity = 100;
         this._experience = 3000;
         this._resistance = 120;
         this._probability = 0.2;
         this._fallEquipmentsList = [{
            "id":23,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":24,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":25,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":26,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":86,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":87,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":88,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":89,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":49,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":50,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":51,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":52,
            "qualityID":[0],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,2],
            "attackInterval":4,
            "attackPower":490 + int(Math.random() * 34),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,4],
            "attackInterval":4,
            "attackPower":490 + int(Math.random() * 34),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[-2,-2],
            "attackInterval":4,
            "attackPower":490 + int(Math.random() * 34),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击4"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,4],
            "attackInterval":4,
            "attackPower":490 + int(Math.random() * 34),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(this._curAttackTarget)
         {
            if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) >= 400 && GameUtility.getDistance(this,this._curAttackTarget) <= 500)
               {
                  if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 120;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 200 && GameUtility.getDistance(this,this._curAttackTarget) < 400)
               {
                  if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 120;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 90;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 100 && GameUtility.getDistance(this,this._curAttackTarget) <= 200)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 100;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 120;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 90;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) <= 100)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 100;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     _skill2CoolDown = 120;
                     this.realseSkill2();
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     _skill3CoolDown = 90;
                     this.realseSkill3();
                  }
                  else
                  {
                     this.attack();
                  }
               }
               else
               {
                  moveTowardHero();
               }
            }
            else
            {
               moveTowardHero();
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(14);
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         newAttackID();
      }
      
      override public function realseSkill2() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(22);
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         newAttackID();
      }
      
      override public function realseSkill3() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(25);
         this._lastHit = "攻击3";
         this.gotoAndStop("攻击3");
         newAttackID();
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(23);
         this._lastHit = "攻击4";
         this.gotoAndStop("攻击4");
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4";
      }
      
      override public function afterAttack(param1:Hero = null, param2:Object = null) : void
      {
         super.afterAttack(param1,param2);
      }
   }
}

