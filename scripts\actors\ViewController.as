package actors
{
   import actors.map.LongShot;
   import actors.menu.GameComplete;
   import base.EnemyAppearPoint;
   import base.GameWorld;
   import flash.display.MovieClip;
   import game.Game;
   import util.GameUtility;
   import util.Input;
   
   public class ViewController
   {
      
      private var _lockRight:Array = [1360,2320,3280,4240,4799.8];
      
      private var _lockPoint:Array = [1360,2320,3280,4240,5180.8];
      
      private var _lockLeft:Array = [0,960,1920,2880,3830];
      
      public var _gameWorld:GameWorld;
      
      public var _game:Game;
      
      private var _stageWidth:Number;
      
      private var _longshot:LongShot;
      
      private var _ratio:Number = 0;
      
      private var _isStop:Boolean = true;
      
      private var _enemyCount:int = 0;
      
      private var _enemyTimes:int = 0;
      
      private var stagePosition:Number;
      
      private var rightEdge:Number;
      
      private var leftEdge:Number;
      
      private var stagePos:Number;
      
      private var distance:Number;
      
      private var _lockScreen:Boolean = false;
      
      private var tk:ThreeKingdoms;
      
      public var currentPoint:Array = [];
      
      private var _lock:Boolean;
      
      private var _vx:*;
      
      private var _stopNum:Number = 0;
      
      private var autoParallexScrolling:Boolean = false;
      
      private var shakeVal:Number = 0;
      
      private var canMove:Boolean;
      
      public function ViewController()
      {
         super();
         this.tk = ThreeKingdoms._instance;
         this._gameWorld = ThreeKingdoms._gameWorld;
         this._longshot = ThreeKingdoms._instance._longshot;
         this._game = ThreeKingdoms._instance._game;
         this._stageWidth = ThreeKingdoms._instance.stage.stageWidth;
         this._ratio = (this._longshot.width - 960) / (this._game.width - 960);
         if(ThreeKingdoms._instance._currentLevel > 11 && ThreeKingdoms._instance._currentLevel < 13)
         {
            this._lockRight = [960,1920,2880,4240,4799.8];
            this._lockPoint = [960,1920,2880,4240,5180.8];
            this._lockLeft = [0,560,1520,1920,3830];
         }
      }
      
      public function shake(param1:int) : void
      {
         if(this.shakeVal == 0)
         {
            this.shakeVal = param1;
         }
      }
      
      public function isLockPoint() : Boolean
      {
         return Math.abs(this._game.x - 960) >= this._lockPoint[this._stopNum];
      }
      
      public function isLockPointLeft() : Boolean
      {
         if(ThreeKingdoms._instance._currentLevel > 11 && ThreeKingdoms._instance._currentLevel < 13)
         {
            if(Math.abs(this._game.x) >= 960 && Math.abs(this._game.x) <= 1920)
            {
               return true;
            }
         }
         if(ThreeKingdoms._instance._currentLevel <= 9 || ThreeKingdoms._instance._currentLevel == 13)
         {
            if(Math.abs(this._game.x) >= 3839 && Math.abs(this._game.x) <= 4799)
            {
               return true;
            }
         }
         if(Math.abs(this._game.x) >= this._lockLeft[this._stopNum - 1] && Math.abs(this._game.x) <= this._lockPoint[this._stopNum - 2])
         {
            return true;
         }
         return false;
      }
      
      public function sendEnemy() : void
      {
         var _loc3_:int = 0;
         var _loc4_:EnemyAppearPoint = null;
         var _loc5_:EnemyAppearPoint = null;
         var _loc6_:MovieClip = null;
         var _loc1_:* = this.getCurrentScreen();
         if(this._enemyTimes < _loc1_)
         {
            this._enemyTimes = _loc1_;
            _loc3_ = 0;
            while(_loc3_ < ThreeKingdoms._gameWorld._sendEnemyPoint.length)
            {
               _loc4_ = EnemyAppearPoint(ThreeKingdoms._gameWorld._sendEnemyPoint[_loc3_]);
               if(_loc4_.stopPointIdx == this.getCurrentScreen() - 1)
               {
                  _loc4_.start();
                  this.currentPoint.push(_loc4_);
                  this._isStop = true;
                  this._lockScreen = true;
                  this._lock = true;
               }
               _loc3_++;
            }
         }
         var _loc2_:int = 0;
         while(_loc2_ < this.currentPoint.length)
         {
            _loc5_ = EnemyAppearPoint(this.currentPoint[_loc2_]);
            if(_loc5_.isOver)
            {
               this.currentPoint.splice(_loc2_,1);
            }
            _loc2_++;
         }
         if(this._gameWorld._enemies.length == 0 && this.currentPoint.length == 0 && _loc1_ < 5 && ThreeKingdoms._gameWorld._sendEnemyPoint.length != 0)
         {
            if(this._lock && this.canMove)
            {
               this._lock = false;
               _loc6_ = GameUtility.getObject("actors.map.GoGoGo");
               SoundManager.play("gogogo");
               _loc6_.x = 860;
               _loc6_.y = 300;
               _loc6_.scaleX = 1.1;
               _loc6_.scaleY = 1.1;
               this.tk._gameInfo.addChild(_loc6_);
               this.autoParallexScrolling = true;
               ++this._stopNum;
            }
            if(this._gameWorld._heroes.length == 1)
            {
               if(!this._gameWorld._heroes[0].isAttacking())
               {
                  this.canMove = true;
                  if(this._lockScreen && this.stagePosition <= this.rightEdge)
                  {
                     this._lockScreen = false;
                     this._isStop = false;
                  }
                  else if(this._lockScreen && this.stagePosition > this.rightEdge)
                  {
                     this._lockScreen = false;
                     this._isStop = false;
                  }
               }
               else
               {
                  this.canMove = false;
               }
            }
            else if(this._gameWorld._heroes.length == 2)
            {
               if(!(Boolean(this._gameWorld._heroes[0].isAttacking()) && Boolean(this._gameWorld._heroes[1].isAttacking())))
               {
                  this.canMove = true;
                  if(this._lockScreen && this.stagePos < this._stageWidth * 0.5)
                  {
                     this._lockScreen = false;
                     this._isStop = false;
                  }
                  else if(this._lockScreen && this.stagePos > this._stageWidth * 0.5)
                  {
                     this._lockScreen = false;
                     this._isStop = false;
                  }
               }
               else
               {
                  this.canMove = false;
               }
            }
         }
      }
      
      public function update() : void
      {
         var _loc1_:Boolean = false;
         var _loc2_:Boolean = false;
         var _loc3_:MovieClip = null;
         if(this.shakeVal > 0)
         {
            this._game.x += this.shakeVal;
            this.shakeVal *= -1;
         }
         else if(this.shakeVal < 0)
         {
            this._game.x += this.shakeVal;
            this.shakeVal = 0;
         }
         this.sendEnemy();
         if(this.shakeVal == 0 && ThreeKingdoms._gameWorld._heroes.length >= 1)
         {
            this.moveScreen();
            _loc1_ = !ThreeKingdoms._gameWorld._heroes[0].isInSky() && ThreeKingdoms._gameWorld._heroes[0].x >= this._game.transferDoor.x - 100 && ThreeKingdoms._gameWorld._heroes[0].x <= this._game.transferDoor.x + 50;
            _loc2_ = false;
            if(ThreeKingdoms._gameWorld._heroes.length >= 2)
            {
               _loc2_ = !ThreeKingdoms._gameWorld._heroes[1].isInSky() && ThreeKingdoms._gameWorld._heroes[1].x >= this._game.transferDoor.x - 100 && ThreeKingdoms._gameWorld._heroes[1].x <= this._game.transferDoor.x + 50;
            }
            if(this._game.transferDoor.visible && (_loc1_ || _loc2_) && (Input.isKeyPressed(38) || Input.isKeyPressed(87)))
            {
               if(this.tk._game)
               {
                  this.tk._game.detory();
                  this.tk._game = null;
               }
               if(this.tk._gameInfo)
               {
                  this.tk.removeChild(this.tk._gameInfo);
                  this.tk._gameInfo = null;
               }
               if(this.tk._longshot)
               {
                  this.tk.removeChild(this.tk._longshot);
                  this.tk._longshot = null;
               }
               ThreeKingdoms._gameWorld.stop();
               ThreeKingdoms._gameWorld.destroy();
               if(this.tk._currentMaxLevel < 9 && this.tk._currentLevel == this.tk._currentMaxLevel)
               {
                  ++this.tk._currentMaxLevel;
               }
               this.tk._memory._neeUI = false;
               this.tk._memory.setMemory();
               if(this.tk._currentLevel == 9)
               {
                  _loc3_ = GameUtility.getObject("actors.menu.GameComplete") as GameComplete;
                  this.tk.addChild(_loc3_);
               }
               else
               {
                  this.tk.initGame();
               }
            }
         }
      }
      
      public function getCurrentScreen() : Number
      {
         var _loc1_:Number = NaN;
         if(Math.abs(this._game.x) <= this._lockLeft[1])
         {
            _loc1_ = 1;
         }
         else if(Math.abs(this._game.x) > this._lockLeft[1] && Math.abs(this._game.x) <= this._lockLeft[2])
         {
            _loc1_ = 2;
         }
         else if(Math.abs(this._game.x) > this._lockLeft[2] && Math.abs(this._game.x) <= this._lockLeft[3])
         {
            _loc1_ = 3;
         }
         else if(Math.abs(this._game.x) > this._lockLeft[3] && Math.abs(this._game.x) <= this._lockLeft[4])
         {
            _loc1_ = 4;
         }
         else if(Math.abs(this._game.x) > this._lockLeft[4] && Math.abs(this._game.x) <= this._lockRight[4])
         {
            _loc1_ = 5;
         }
         return _loc1_;
      }
      
      public function moveScreen() : void
      {
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc7_:Number = NaN;
         var _loc1_:int = ThreeKingdoms._gameWorld._heroes[0] ? int(Math.abs(ThreeKingdoms._gameWorld._heroes[0]._vx)) : 0;
         var _loc2_:int = 0;
         if(ThreeKingdoms._gameWorld._heroes.length == 2)
         {
            _loc2_ = Math.abs(ThreeKingdoms._gameWorld._heroes[1]._vx);
         }
         var _loc5_:uint = this.getCurrentScreen();
         this.rightEdge = ThreeKingdoms._instance.stage.stageWidth - 400;
         this.leftEdge = 400;
         if(this.tk._gameMode == 1 && !this.tk._isGameOver)
         {
            if(this.autoParallexScrolling)
            {
               if(this.stagePosition > this.rightEdge && !this.isLockPoint())
               {
                  if(Math.max(_loc1_,_loc2_) + 10 <= 30)
                  {
                     _loc3_ = Math.max(_loc1_,_loc2_) + 10;
                  }
                  else
                  {
                     _loc3_ = 30;
                  }
                  this._game.x -= int(_loc3_);
                  _loc4_ = int(_loc3_ * this._ratio);
                  this._longshot.x -= int(_loc4_);
               }
               else
               {
                  this.autoParallexScrolling = false;
               }
            }
            this.stagePosition = this._game.x + this._gameWorld._heroes[0].x;
            if(!this.isLockPoint() && this.stagePosition > this.rightEdge)
            {
               if(!this.autoParallexScrolling)
               {
                  this._game.x -= int(this.stagePosition - this.rightEdge);
                  if(this._game.x >= -int(this._game.floor.width - this._stageWidth))
                  {
                     this._longshot.x -= int((this.stagePosition - this.rightEdge) * this._ratio);
                  }
                  else
                  {
                     this._game.x = -int(this._game.floor.width - this._stageWidth);
                     this._longshot.x = this._longshot.x;
                  }
               }
            }
            if(!this.isLockPointLeft() && this.stagePosition < this.leftEdge)
            {
               if(this._gameWorld._heroes[0].x >= this._lockLeft[_loc5_ - 1] && this._gameWorld._heroes[0].x <= this._lockRight[_loc5_ - 2])
               {
                  return;
               }
               this._longshot.x += int((this.leftEdge - this.stagePosition) * this._ratio);
               this._game.x += int(this.leftEdge - this.stagePosition);
               if(this._game.x > 0)
               {
                  this._game.x = 0;
               }
               if(this._longshot.x > 0)
               {
                  this._longshot.x = 0;
               }
            }
         }
         else if(this.tk._gameMode == 2 && !this.tk._isGameOver)
         {
            _loc6_ = this._game.x + this._gameWorld._heroes[0].x;
            if(this._gameWorld._heroes.length == 2)
            {
               _loc7_ = this._game.x + this._gameWorld._heroes[1].x;
            }
            else
            {
               _loc7_ = _loc6_;
            }
            _loc3_ = Math.max(_loc1_,_loc2_);
            if(this.autoParallexScrolling)
            {
               if(_loc6_ > this.leftEdge && _loc7_ > this.rightEdge && !this.isLockPoint())
               {
                  if(Math.max(_loc1_,_loc2_) + 10 <= 30)
                  {
                     _loc3_ = Math.max(_loc1_,_loc2_) + 10;
                  }
                  else
                  {
                     _loc3_ = 30;
                  }
                  this._game.x -= _loc3_;
                  _loc4_ = int(_loc3_ * this._ratio);
                  this._longshot.x -= int(_loc4_);
               }
               if(_loc7_ > this.leftEdge && _loc6_ > this.rightEdge && !this.isLockPoint())
               {
                  if(Math.max(_loc1_,_loc2_) + 10 <= 30)
                  {
                     _loc3_ = Math.max(_loc1_,_loc2_) + 10;
                  }
                  else
                  {
                     _loc3_ = 30;
                  }
                  this._game.x -= _loc3_;
                  _loc4_ = int(_loc3_ * this._ratio);
                  this._longshot.x -= int(_loc4_);
               }
               else
               {
                  this.autoParallexScrolling = false;
               }
            }
            if(_loc6_ > this.leftEdge && _loc7_ > this.rightEdge && !this.isLockPoint())
            {
               if(!this.autoParallexScrolling)
               {
                  this._game.x -= _loc3_;
                  if(this._game.x >= -(this._game.floor.width - this._stageWidth))
                  {
                     this._longshot.x -= _loc3_ * ((this._longshot.width - 960) / (this._game.width - 960));
                  }
                  else
                  {
                     this._game.x = -(this._game.floor.width - this._stageWidth);
                     this._longshot.x = this._longshot.x;
                  }
               }
            }
            else if(_loc7_ > this.leftEdge && _loc6_ > this.rightEdge && !this.isLockPoint())
            {
               if(!this.autoParallexScrolling)
               {
                  this._game.x -= _loc3_;
                  if(this._game.x >= -(this._game.floor.width - this._stageWidth))
                  {
                     this._longshot.x -= _loc3_ * ((this._longshot.width - 960) / (this._game.width - 960));
                  }
                  else
                  {
                     this._game.x = -(this._game.floor.width - this._stageWidth);
                     this._longshot.x = this._longshot.x;
                  }
               }
            }
            if(!this.isLockPointLeft() && _loc6_ < this.leftEdge && _loc7_ < this.rightEdge)
            {
               if(_loc6_ >= this._lockLeft[_loc5_ - 1] && _loc6_ <= this._lockRight[_loc5_ - 2] || _loc7_ >= this._lockLeft[_loc5_ - 1] && _loc7_ <= this._lockRight[_loc5_ - 2])
               {
                  return;
               }
               this._game.x += _loc3_;
               this._longshot.x += _loc3_ * ((this._longshot.width - 960) / (this._game.width - 960));
               if(this._game.x > 0)
               {
                  this._game.x = 0;
               }
               if(this._longshot.x > 0)
               {
                  this._longshot.x = 0;
               }
            }
            else if(!this.isLockPointLeft() && _loc7_ < this.leftEdge && _loc6_ < this.rightEdge)
            {
               if(_loc6_ >= this._lockLeft[_loc5_ - 1] && _loc6_ <= this._lockRight[_loc5_ - 2] || _loc7_ >= this._lockLeft[_loc5_ - 1] && _loc7_ <= this._lockRight[_loc5_ - 2])
               {
                  return;
               }
               this._game.x += _loc3_;
               this._longshot.x += _loc3_ * ((this._longshot.width - 960) / (this._game.width - 960));
               if(this._game.x > 0)
               {
                  this._game.x = 0;
               }
               if(this._longshot.x > 0)
               {
                  this._longshot.x = 0;
               }
            }
         }
      }
   }
}

