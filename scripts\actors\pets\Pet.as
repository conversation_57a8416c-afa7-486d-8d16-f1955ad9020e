package actors.pets
{
   import actors.Enemy;
   import actors.Hero;
   import actors.Properties;
   import base.BuffEffect;
   import flash.events.Event;
   import flash.geom.Point;
   import util.GameUtility;
   
   public class Pet extends Hero
   {
      
      private var tk:ThreeKingdoms;
      
      public var _attackProbablity:Number = 50;
      
      public var _master:Hero;
      
      public var _currentAttakTarget:Enemy;
      
      public var _lastAttackTarget:Enemy;
      
      public var _skill1CoolDown:uint = 0;
      
      public var _skill2CoolDown:uint = 0;
      
      public var _roleFrameID:uint = 1;
      
      public var _description:String;
      
      public var _isFirstAddToStage:Boolean = true;
      
      public var _initAttackPower:int;
      
      public var _initResistance:int;
      
      public var _feedLevel:int = 0;
      
      public function Pet()
      {
         super();
         this.tk = ThreeKingdoms._instance;
         this.gotoAndStop("休息");
         this._walkSpeed = 4;
         this._runSpeed = 5;
         this._properties = new Properties(this);
         this._properties.setMoveSpeed(this._walkSpeed);
         _buffEffect = new BuffEffect(this);
         this._object._maxPatrolView = 300;
         this._object._attackRange = 120;
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddToStageHandler,false,0,true);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler,false,0,true);
         _roleName = "幼灵";
         this._initAttackPower = 0 + Math.round(Math.random() * 10);
         this._initResistance = 0 + Math.round(Math.random() * 10);
         if(Math.random() < 0.5)
         {
            this._roleType = "火爆";
         }
         else
         {
            this._roleType = "懒惰";
         }
         this._description = "    灵树果所化灵兽，拥有沟通天地万物的能力";
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
      }
      
      override public function onRemovedFromStageHandler(param1:Event) : void
      {
         super.onRemovedFromStageHandler(param1);
      }
      
      public function setMaster(param1:Hero) : *
      {
         this._master = param1;
         this._master.pet = this;
      }
      
      override public function onAddToStageHandler(param1:Event) : void
      {
         this.initProperties();
      }
      
      override public function initProperties() : void
      {
         this.levelUP(_properties.getLevel());
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
      }
      
      public function initAR() : void
      {
         this._properties.setAttackPower(this._initAttackPower + 10 + (this._properties.getLevel() - 1) * 3);
         this._properties.setResistance(this._initResistance + 10 + (this._properties.getLevel() - 1) * 2);
      }
      
      override public function levelUP(param1:int = 1) : void
      {
         var _loc2_:uint = 0;
         var _loc3_:* = undefined;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         if(this._properties.getLevel() < 15)
         {
            if(!this._isFirstTimeToInit)
            {
               this._properties.destroy();
            }
            this._isFirstTimeToInit = false;
            this._properties.setTotalHealthPoint(200 + (this._properties.getLevel() - 1) * 30);
            this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
            this._properties.setTotalManaPoint(60 + (this._properties.getLevel() - 1) * 10);
            this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
            this._properties.setAttackPower(this._initAttackPower + 10 + (this._properties.getLevel() - 1) * 3);
            this._properties.setResistance(this._initResistance + 10 + (this._properties.getLevel() - 1) * 2);
            _loc2_ = this._properties.getLevel() / 10;
            this._properties.setHealthRegeneration(_loc2_);
            this._properties.setTotalExperience((this._properties.getLevel() - 1) * (this._properties.getLevel() - 1) * 100 + 50 * this._properties.getLevel());
            this._properties.initAll();
         }
         if(this._properties.getLevel() == 15)
         {
            _game = ThreeKingdoms._instance._game;
            _loc3_ = new Point(this.x,this.y);
            if(this.parent)
            {
               this.parent.removeChild(this);
            }
            _loc4_ = int(ThreeKingdoms._gameWorld._pets.indexOf(this));
            _loc5_ = int(this._master.getPlayer()._petsVector.indexOf(this));
            if(_loc5_ != -1)
            {
               this._master.getPlayer()._petsVector.splice(_loc5_,1);
            }
            if(_loc4_ != -1)
            {
               ThreeKingdoms._gameWorld._pets.splice(_loc4_,1);
            }
            if(this._roleType == "火爆")
            {
               this.tk._pet2 = GameUtility.getObject("actors.pets.Pet2");
               this.tk._pet2.setPlayer(this.tk._pet2user);
               this.tk._pet2user._roleID = 3;
               this.tk._pet2user.hero = this.tk._pet2;
               this.tk._pet2.setMaster(this._master);
               this.tk._pet2.x = _loc3_.x;
               this.tk._pet2.y = _loc3_.y - 40;
               this.tk._pet2._initAttackPower = this._initAttackPower;
               this.tk._pet2._initResistance = this._initResistance;
               this.tk._pet2._feedLevel = this._feedLevel;
               this.tk._pet2._properties.setLevel(14);
               _game.addChild(this.tk._pet2);
               this.tk._pet2.gotoAndStop("出现");
               ThreeKingdoms._gameWorld.addPet(this.tk._pet2);
               this._master.getPlayer()._petsVector.push(this.tk._pet2);
            }
            else
            {
               this.tk._pet4 = GameUtility.getObject("actors.pets.Pet4");
               this.tk._pet4.setPlayer(this.tk._pet4user);
               this.tk._pet4user._roleID = 5;
               this.tk._pet4._initAttackPower = this._initAttackPower;
               this.tk._pet4._initResistance = this._initResistance;
               this.tk._pet4._feedLevel = this._feedLevel;
               this.tk._pet4._properties.setLevel(14);
               this.tk._pet4user.hero = this.tk._pet4;
               this.tk._pet4.setMaster(this._master);
               this.tk._pet4.x = _loc3_.x;
               this.tk._pet4.y = _loc3_.y - 40;
               _game.addChild(this.tk._pet4);
               this.tk._pet4.gotoAndStop("出现");
               ThreeKingdoms._gameWorld.addPet(this.tk._pet4);
               this._master.getPlayer()._petsVector.push(this.tk._pet4);
            }
         }
         this._properties.setTotalHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(this._properties.getTotalManaPoint());
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
      }
      
      public function updateCounter() : void
      {
         if(!this._master)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack() || this.isJumping())
         {
            return;
         }
         if(this.isDead())
         {
            return;
         }
         ++_count;
         if(_count % 30 == 0)
         {
            _count = 0;
         }
      }
      
      override public function getSpecificAttackPowerByType(param1:String) : int
      {
         switch(param1)
         {
            case "攻击1":
               return this._properties.getTotalAttackPower();
            default:
               return 0;
         }
      }
      
      public function flyToMaster() : void
      {
         if(this._master)
         {
            if(this._currentAttakTarget)
            {
               this._currentAttakTarget = null;
            }
            this.x = this._master.x - 50;
            this.y = this._master.y - 200;
            this.gotoAndStop("出现");
         }
      }
      
      private function myIntelligence() : void
      {
         if(!this._master)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack() || this.isJumping())
         {
            return;
         }
         if(this.isDead())
         {
            return;
         }
         if(GameUtility.getDistance(this,this._master) >= 700)
         {
            if(this._count % 30 == 0)
            {
               this.gotoAndStop("消失");
               return;
            }
         }
         if(!this._currentAttakTarget || this._currentAttakTarget.alpha == 0)
         {
            this.selectAttackTarget();
            if(Math.abs(this.x - this._master.x) < 120)
            {
               this.stayWithMaster();
            }
            else
            {
               this.followMaster();
            }
         }
         else
         {
            if(ThreeKingdoms._gameWorld._enemies.length <= 0)
            {
               this._currentAttakTarget = null;
               return;
            }
            if(this._currentAttakTarget.isDead())
            {
               this._currentAttakTarget = null;
               return;
            }
            if(Boolean(this._currentAttakTarget) && GameUtility.getDistance(this,this._currentAttakTarget) >= _object._maxPatrolView)
            {
               this._currentAttakTarget = null;
               return;
            }
            if(this._count % 30 == 0)
            {
               this.startAttacking();
            }
         }
      }
      
      override public function update() : void
      {
         if(this.currentFrameLabel == "消失" || this.isDead())
         {
            return;
         }
         step();
         this.myIntelligence();
         this.checkJump();
         this.updateCounter();
         this.skillCoolDown();
         if(this.currentLabel == "休息")
         {
            _vx = 0;
         }
      }
      
      private function stayWithMaster() : void
      {
         if(this._master)
         {
            if(GameUtility.getDistance(this,this._master) <= 200)
            {
               if(this._master.x > this.x)
               {
                  this._object._direction = RIGHT;
                  GameUtility.flipHorizontal(this,1);
               }
               else if(this._master.x <= this.x)
               {
                  this._object._direction = LEFT;
                  GameUtility.flipHorizontal(this,-1);
               }
               this._vx = 0;
               this.gotoAndStop("休息");
            }
         }
      }
      
      private function checkJump() : void
      {
         if(!this._master)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack() || this.isJumping())
         {
            return;
         }
         if(this.isDead())
         {
            return;
         }
         if(_isHitLeft || _isHitRight)
         {
            if(GameUtility.getRandomNumber(50))
            {
               if(this.x > 4900)
               {
                  _object._direction = LEFT;
                  GameUtility.flipHorizontal(this,-1);
               }
               else if(this.x < 50)
               {
                  _object._direction = RIGHT;
                  GameUtility.flipHorizontal(this,1);
               }
               else
               {
                  this.jump();
               }
            }
            else
            {
               if(!isInSky() && _isHitRight)
               {
                  _object._direction = LEFT;
                  GameUtility.flipHorizontal(this,-1);
               }
               else
               {
                  !isInSky() && _isHitLeft;
               }
               _object._direction = RIGHT;
               GameUtility.flipHorizontal(this,1);
            }
            _isHitLeft = false;
            _isHitRight = false;
         }
         if(this._object._direction == LEFT)
         {
            _vx = -_walkSpeed;
         }
         else if(this._object._direction == RIGHT)
         {
            _vx = _walkSpeed;
         }
      }
      
      override public function jump() : void
      {
         if(this.isJumping() || this.isUnderAttack() || this.isAttacking())
         {
            return;
         }
         this.gotoAndStop("跳跃");
         _vy = -20;
         this._isJumping = true;
         if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") == 2)
         {
            ThreeKingdoms._instance._protectedProperty.setProperty(this,"_jumpCount",1);
         }
         else if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") == 1)
         {
            ThreeKingdoms._instance._protectedProperty.setProperty(this,"_jumpCount",0);
         }
      }
      
      override public function isJumping() : Boolean
      {
         return this.currentLabel == "跳跃";
      }
      
      public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(this._attackProbablity))
         {
            if(GameUtility.getDistance(this,this._currentAttakTarget) < _object._attackRange)
            {
               this.attack();
            }
            else
            {
               this.followTarget();
            }
         }
         else if(Math.random() < 0.5)
         {
            this.followTarget();
         }
         else
         {
            this.steer();
            this._vx = 0;
            this.gotoAndStop("休息");
         }
      }
      
      public function followTarget() : void
      {
         if(this._currentAttakTarget)
         {
            if(this._currentAttakTarget.x > this.x)
            {
               this._object._direction = RIGHT;
               GameUtility.flipHorizontal(this,1);
            }
            else if(this._currentAttakTarget.x <= this.x)
            {
               this._object._direction = LEFT;
               GameUtility.flipHorizontal(this,-1);
            }
         }
         if(this._object._direction == LEFT)
         {
            this._vx = -_walkSpeed;
         }
         else if(this._object._direction == RIGHT)
         {
            this._vx = _walkSpeed;
         }
         this.gotoAndStop("行走");
      }
      
      override public function attack() : void
      {
         this.steer();
         this._vx = 0;
         if(this.currentLabel != "攻击1")
         {
            this.gotoAndStop("攻击1");
         }
         this._lastHit = "攻击1";
         this.newAttackID();
         this.setYourDaddysTime(12);
      }
      
      public function steer() : void
      {
         if(this._currentAttakTarget)
         {
            if(_count % 30 == 0)
            {
               if(this._currentAttakTarget.x > this.x)
               {
                  this._object._direction = RIGHT;
                  GameUtility.flipHorizontal(this,1);
               }
               else if(this._currentAttakTarget.x <= this.x)
               {
                  this._object._direction = LEFT;
                  GameUtility.flipHorizontal(this,-1);
               }
            }
         }
      }
      
      public function selectAttackTarget() : void
      {
         var _loc2_:Enemy = null;
         if(this.assignTarget())
         {
            return;
         }
         var _loc1_:int = 0;
         while(_loc1_ < _world._enemies.length)
         {
            _loc2_ = _world._enemies[_loc1_] as Enemy;
            if(Boolean(_loc2_) && !_loc2_.isDead())
            {
               if(GameUtility.getDistance(this,_loc2_) <= _object._maxPatrolView)
               {
                  this._currentAttakTarget = _loc2_;
                  return;
               }
            }
            _loc1_++;
         }
      }
      
      public function assignTarget() : Boolean
      {
         if(this._currentAttakTarget)
         {
            return true;
         }
         return false;
      }
      
      protected function followMaster() : void
      {
         if(this._master)
         {
            if(this._master.x > this.x)
            {
               this._object._direction = RIGHT;
               GameUtility.flipHorizontal(this,1);
            }
            else if(this._master.x <= this.x)
            {
               this._object._direction = LEFT;
               GameUtility.flipHorizontal(this,-1);
            }
            if(this._object._direction == LEFT)
            {
               this._vx = -_walkSpeed;
            }
            else if(this._object._direction == RIGHT)
            {
               this._vx = _walkSpeed;
            }
            this.gotoAndStop("行走");
         }
      }
      
      public function skillCoolDown() : void
      {
         if(this._skill1CoolDown-- <= 0)
         {
            this._skill1CoolDown = 0;
         }
         if(this._skill2CoolDown-- <= 0)
         {
            this._skill2CoolDown = 0;
         }
      }
      
      override public function levelClear() : void
      {
         this._currentAttakTarget = null;
         super.levelClear();
      }
      
      override public function isRunning() : Boolean
      {
         return super.isRunning();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1";
      }
      
      override public function isDead() : Boolean
      {
         return this._properties.getCurrentHealthPoint() <= 0;
      }
      
      override public function onCompleteHandler() : void
      {
         if(this._currentAttakTarget)
         {
            this._currentAttakTarget = null;
         }
         super.onCompleteHandler();
      }
   }
}

