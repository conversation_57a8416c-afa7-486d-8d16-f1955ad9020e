package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol765")]
   public dynamic class mcExchange extends MovieClip
   {
      
      public var exCodeTxt:TextField;
      
      public var btnExchange:SimpleButton;
      
      public var btnClose:SimpleButton;
      
      public var mcShow:MovieClip;
      
      public var mcShow2:MovieClip;
      
      public function mcExchange()
      {
         super();
         addFrameScript(0,this.frame1);
      }
      
      internal function frame1() : *
      {
         stop();
      }
   }
}

