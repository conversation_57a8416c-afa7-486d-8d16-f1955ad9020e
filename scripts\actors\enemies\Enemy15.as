package actors.enemies
{
   import actors.Enemy;
   import util.UString;
   
   public class Enemy15 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy15(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 450;
         this._object._alertRange = 450;
         this._object._attackRange = 300;
         this._currentHealthPoint = 3200;
         this._totalHealthPoint = 3200;
         this._resistance = 62;
         this._experience = 100;
         this._attackProbablity = 36;
         this._walkSpeed = 3.2;
         this._goldPrice = 100 + Math.round(Math.random() * 50);
         this._probability = 0.3;
         this._fallEquipmentsList = [{
            "id":78,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":79,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":80,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":81,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":15,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":16,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":17,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":18,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":41,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":42,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":43,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":44,
            "qualityID":[0],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,2],
            "attackInterval":4,
            "attackPower":123 + Math.round(Math.random() * 39),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         super.startAttacking();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1";
      }
   }
}

