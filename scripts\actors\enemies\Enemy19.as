package actors.enemies
{
   import actors.Enemy;
   import actors.Hero;
   import actors.SoundManager;
   import actors.user.User;
   import event.GameEvent;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy19 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      private var _armorProbablity:int = 50;
      
      public function Enemy19(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 500;
         this._object._alertRange = 500;
         this._object._attackRange = 150;
         this._currentHealthPoint = 6800;
         this._totalHealthPoint = 6800;
         this._resistance = 123;
         this._experience = 270;
         this._attackProbablity = 62;
         this._goldPrice = 250;
         this._probability = 0.15;
         this._fallEquipmentsList = [{
            "id":15,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":16,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":17,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":18,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":78,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":79,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":80,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":65,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":81,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":41,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":42,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":43,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":44,
            "qualityID":[1],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,3],
            "attackInterval":4,
            "attackPower":198 + Math.round(Math.random() * 48),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         super.startAttacking();
      }
      
      override public function isUnderAttack() : Boolean
      {
         return this.currentLabel == "攻击2" || this.currentLabel == "被攻击";
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1";
      }
      
      override public function afterAttack(param1:Hero = null, param2:Object = null) : void
      {
         var _loc4_:Boolean = false;
         var _loc5_:int = 0;
         this.showLifeBar();
         this._vx = 0;
         this.beenAttackBack(param1,param2.attackBackVelocity[0],param2.attackBackVelocity[1]);
         ++User._batterTimes;
         var _loc3_:int = param1.getSpecificAttackPowerByType(param1._lastHit);
         if(Math.random() < param1._properties.getCrit())
         {
            _loc3_ *= 2;
            _isCrit = true;
         }
         else
         {
            _isCrit = false;
         }
         if(GameUtility.getRandomNumber(this._armorProbablity))
         {
            _loc5_ = this.getRealHurtAfterCaculation(_loc3_,param2) * (1 - 0.8);
            _loc4_ = true;
         }
         else
         {
            _loc5_ = this.getRealHurtAfterCaculation(_loc3_,param2);
            _loc4_ = false;
         }
         this._currentHealthPoint -= _loc5_;
         if(this._currentHealthPoint <= 0)
         {
            this._currentHealthPoint = 0;
         }
         _world._eventManager.dispatchEvent(new GameEvent(GameEvent.ENEMY_IS_UNDER_ATTACK,[_loc5_,param1]));
         this.addMonsterGetHurtAnimation(_loc5_,_loc3_);
         if(this.isDead())
         {
            if(this.isBoss)
            {
               ThreeKingdoms._instance._gameInfo.addBossHealthPointBar(this.enemyName,100 - Math.round(100 * (this._currentHealthPoint / this._totalHealthPoint)));
               ThreeKingdoms._instance._game.transferDoor.visible = true;
            }
            if(!this.currentLabel != "死亡")
            {
               this.gotoAndStop("死亡");
            }
            Hero(this._theHeroWhoAttackedMe)._properties.setCurrentExperience(_experience);
            if(_lifeBar.parent)
            {
               _lifeBar.parent.removeChild(_lifeBar);
            }
         }
         else
         {
            steer();
            if(_loc4_)
            {
               if(this.currentLabel != "攻击2")
               {
                  this.gotoAndStop("攻击2");
               }
            }
            else if(this.currentLabel != "被攻击")
            {
               this.gotoAndStop("被攻击");
            }
            else if(this.body)
            {
               this.body.gotoAndPlay(1);
            }
         }
         addBeAttackEffect(null);
         SoundManager.play("BeattackByRole1");
      }
   }
}

