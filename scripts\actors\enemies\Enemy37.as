package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   
   public class Enemy37 extends Enemy
   {
      
      private var _isStealth:<PERSON><PERSON>an = false;
      
      private var _stealthTime:uint = 0;
      
      public function Enemy37(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this.enemyName = "猫儿王";
         this.isBoss = true;
         this._object._maxPatrolView = 500;
         this._object._alertRange = 900;
         this._object._attackRange = 500;
         this._currentHealthPoint = 120000;
         this._totalHealthPoint = 120000;
         this._resistance = 220;
         this._experience = 250;
         this._attackProbablity = 75;
         this._goldPrice = 200;
         this._walkSpeed = 4;
         this._probability = 0.1;
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,3],
            "attackInterval":4,
            "attackPower":625 + Math.round(Math.random() * 57),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["隐身"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,3],
            "attackInterval":4,
            "attackPower":253 + Math.round(Math.random() * 79),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["重击"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[5,3],
            "attackInterval":4,
            "attackPower":931 + Math.round(Math.random() * 57),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["诅咒"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,3],
            "attackInterval":4,
            "attackPower":253 + Math.round(Math.random() * 79),
            "attackType":"physical"
         };
         this._fallEquipmentsList = [{
            "id":99,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":100,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":101,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":102,
            "qualityID":[0],
            "type":"equipment"
         }];
      }
      
      override public function update() : void
      {
         super.update();
         if(this._stealthTime > 0)
         {
            --this._stealthTime;
            if(this._stealthTime <= 0)
            {
               this._stealthTime = 0;
               this._isStealth = false;
               this._walkSpeed = 4;
               this.alpha = 1;
            }
         }
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "隐身" || this.currentLabel == "重击" || this.currentLabel == "诅咒";
      }
      
      override protected function myIntelligence() : void
      {
         if(!this._isStealth && this.isUnderAttack() || this.isAttacking() || this.isJumping() || this.isDead() || this._isDizziness)
         {
            return;
         }
         if(this._curAttackTarget == null)
         {
            this.patrol();
            if(!this.isUnderAttack())
            {
               this.selectTarget();
            }
         }
         else if(this.isUnderAttack())
         {
            moveTowardHero();
         }
         else
         {
            this.alreadyGotAttackTarget();
         }
      }
      
      override public function startAttacking() : void
      {
         if(this._curAttackTarget)
         {
            if(this._isStealth)
            {
               if(GameUtility.getRandomNumber(_attackProbablity))
               {
                  if(GameUtility.getDistance(this,this._curAttackTarget) > 200 && GameUtility.getDistance(this,this._curAttackTarget) <= 350)
                  {
                     if(_skill3CoolDown == 0 && GameUtility.getRandomNumber(30))
                     {
                        this.realseSkill3();
                        _skill3CoolDown = 210;
                     }
                     else
                     {
                        moveTowardHero();
                     }
                  }
                  else if(GameUtility.getDistance(this,this._curAttackTarget) <= 200)
                  {
                     if(_skill3CoolDown == 0 && GameUtility.getRandomNumber(30))
                     {
                        this.realseSkill3();
                        _skill3CoolDown = 210;
                     }
                     else
                     {
                        this.attack();
                     }
                  }
               }
            }
            else if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) > 350 && GameUtility.getDistance(this,this._curAttackTarget) <= 450)
               {
                  if(_skill1CoolDown == 0 && GameUtility.getRandomNumber(30))
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 900;
                  }
                  else if(_skill2CoolDown == 0 && GameUtility.getRandomNumber(30))
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 900;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 200 && GameUtility.getDistance(this,this._curAttackTarget) <= 350)
               {
                  if(_skill1CoolDown == 0 && GameUtility.getRandomNumber(30))
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 900;
                  }
                  else if(_skill2CoolDown == 0 && GameUtility.getRandomNumber(30))
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 900;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) <= 200)
               {
                  if(_skill1CoolDown == 0 && GameUtility.getRandomNumber(30))
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 900;
                  }
                  else if(_skill2CoolDown == 0 && GameUtility.getRandomNumber(30))
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 900;
                  }
                  else
                  {
                     this.attack();
                  }
               }
               else
               {
                  moveTowardHero();
               }
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(18);
         this._lastHit = "诅咒";
         this.gotoAndStop("诅咒");
         newAttackID();
      }
      
      override public function realseSkill2() : void
      {
         steer();
         _vx = 0;
         this._framesLength = 25;
         setYourDaddysTime(25);
         this._lastHit = "隐身";
         this.gotoAndStop("隐身");
         this._stealthTime = 400;
         newAttackID();
      }
      
      override protected function nextAction() : void
      {
         this.alpha = 0;
         this._walkSpeed = 6;
         this._isStealth = true;
         this.gotoAndStop("休息");
      }
      
      override public function realseSkill3() : void
      {
         this.alpha = 1;
         this._isStealth = false;
         this._walkSpeed = 4;
         this._object._maxPatrolView = 450;
         steer();
         _vx = 0;
         setYourDaddysTime(25);
         this._lastHit = "重击";
         this.gotoAndStop("重击");
         newAttackID();
      }
      
      override public function attack() : void
      {
         _vx = 0;
         steer();
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(16);
         newAttackID();
      }
   }
}

