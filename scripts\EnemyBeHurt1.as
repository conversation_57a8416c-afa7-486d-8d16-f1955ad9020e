package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol677")]
   public dynamic class EnemyBeHurt1 extends MovieClip
   {
      
      public function EnemyBeHurt1()
      {
         super();
         addFrameScript(6,this.frame7);
      }
      
      internal function frame7() : *
      {
         if(parent)
         {
            parent.removeChild(this);
         }
      }
   }
}

