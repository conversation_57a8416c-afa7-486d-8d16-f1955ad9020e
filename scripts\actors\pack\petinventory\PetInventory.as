package actors.pack.petinventory
{
   import actors.Hero;
   import actors.equipments.Equipment;
   import actors.pack.Package;
   import actors.pets.Pet;
   import actors.pets.Pet1;
   import actors.pets.Pet2;
   import actors.pets.Pet3;
   import actors.pets.Pet4;
   import actors.pets.Pet5;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import util.GameUtility;
   
   public class PetInventory extends MovieClip
   {
      
      public var mcMessage:MovieClip;
      
      public var makeSure:MovieClip;
      
      public var btnClose:SimpleButton;
      
      public var takePet:SimpleButton;
      
      public var releasePet:SimpleButton;
      
      public var foodPet:SimpleButton;
      
      public var mcFood:MovieClip;
      
      public var mcPetInfo:MovieClip;
      
      public var mcPetName:MovieClip;
      
      public var mcPet:MovieClip;
      
      public var _pet:Pet;
      
      public var mcPets:Array = [];
      
      public var _master:Hero;
      
      public var _currentTarget:Pet;
      
      public function PetInventory(param1:Hero)
      {
         super();
         this._pet = param1.pet;
         if(param1)
         {
            this._master = param1;
         }
         this.mcMessage.visible = false;
         this.mcFood.visible = false;
         this.makeSure.visible = false;
         this.addEventListener(Event.ADDED_TO_STAGE,this.added,false,0,true);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.removed,false,0,true);
         var _loc2_:int = 1;
         while(_loc2_ <= 5)
         {
            this.mcPets.push(this.mcPet["mcPet" + _loc2_]);
            this.mcPet["mcPet" + _loc2_].buttonMode = true;
            this.mcPet["mcPet" + _loc2_].addEventListener(MouseEvent.CLICK,this.onClickPetHandler);
            _loc2_++;
         }
      }
      
      private function onClickPetHandler(param1:MouseEvent) : void
      {
         var _loc2_:String = param1.currentTarget.name;
         var _loc3_:int = parseInt(_loc2_.substr(5,1));
         if(_loc3_ > this._master.getPlayer()._petsVector.length)
         {
            return;
         }
         this._currentTarget = this._master.getPlayer()._petsVector[_loc3_ - 1] as Pet;
         this._pet = this._currentTarget;
         if(this._currentTarget)
         {
            this.showCohesionIcon(this._currentTarget._feedLevel);
         }
         this.init();
      }
      
      private function removed(param1:Event) : void
      {
         this.removeEventListener(Event.REMOVED_FROM_STAGE,this.removed);
         this.btnClose.removeEventListener(MouseEvent.CLICK,this.onCloseHandler);
      }
      
      private function added(param1:Event) : void
      {
         this.removeEventListener(Event.ADDED_TO_STAGE,this.added);
         this.btnClose.addEventListener(MouseEvent.CLICK,this.onCloseHandler);
         this.releasePet.addEventListener(MouseEvent.CLICK,this.onReleasePetHandler);
         this.foodPet.addEventListener(MouseEvent.CLICK,this.onFoodPetHandler);
         if(ThreeKingdoms._instance._town)
         {
            this.takePet.visible = true;
            this.takePet.addEventListener(MouseEvent.CLICK,this.onTakePetHandler);
         }
         else
         {
            this.takePet.visible = false;
            this.mcMessage.visible = true;
         }
         this.init();
         if(this._master.pet)
         {
            this.showCohesionIcon(this._master.pet._feedLevel);
         }
      }
      
      private function onFoodPetHandler(param1:MouseEvent) : void
      {
         if(Boolean(this._currentTarget) && !this._currentTarget.isDead())
         {
            this.mcFood.visible = true;
            this.mcFood["txtNum"].text = this._master.getPlayer()._petFoodVector.length + " ";
            this.mcFood["txtProbability"].text = this._master.getPlayer()._petFoodVector.length + " ";
            this.mcFood["txtCohesion"].text = this._currentTarget._feedLevel + " ";
            if(this._currentTarget._roleType == "火爆")
            {
               this.mcFood["txtAddProperty"].text = "喂养成功增加25攻击5点防御 ";
            }
            else if(this._currentTarget._roleType == "懒惰")
            {
               this.mcFood["txtAddProperty"].text = "喂养成功增加15攻击8点防御 ";
            }
            this.mcFood["btnFood"].addEventListener(MouseEvent.CLICK,this.addPetProperty);
            this.mcFood["btnClose"].addEventListener(MouseEvent.CLICK,this.closeMcFood);
         }
      }
      
      private function addPetProperty(param1:MouseEvent) : void
      {
         var _loc2_:Equipment = null;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         if(this._currentTarget)
         {
            if(this._master.getPlayer()._petFoodVector.length > 0 && this._currentTarget._feedLevel < 10)
            {
               _loc2_ = this._master.getPlayer()._petFoodVector[0] as Equipment;
               if(_loc2_)
               {
                  _loc3_ = int(this._master.getPlayer()._petFoodVector.indexOf(_loc2_));
                  _loc4_ = int(this._master.getPlayer()._specialVector.indexOf(_loc2_));
                  if(_loc3_ != -1)
                  {
                     this._master.getPlayer()._petFoodVector.splice(_loc3_,1);
                     this._master.getPlayer()._specialVector.splice(_loc4_,1);
                     _loc2_ = null;
                     if(this._currentTarget)
                     {
                        if(this._currentTarget._roleType == "火爆")
                        {
                           this._currentTarget._initAttackPower += 25;
                           this._currentTarget._initResistance += 5;
                        }
                        else if(this._currentTarget._roleType == "懒惰")
                        {
                           this._currentTarget._initAttackPower += 15;
                           this._currentTarget._initResistance += 8;
                        }
                     }
                     this._currentTarget._feedLevel += 1;
                     this.init();
                  }
                  this.showCohesionIcon(this._currentTarget._feedLevel);
               }
            }
         }
         this.mcFood["btnFood"].removeEventListener(MouseEvent.CLICK,this.addPetProperty);
         this.mcFood["txtNum"].text = this._master.getPlayer()._petFoodVector.length + " ";
         this.mcFood["txtCohesion"].text = this._currentTarget._feedLevel + " ";
         if(this._currentTarget._roleType == "火爆")
         {
            this.mcFood["txtAddProperty"].text = "喂养成功增加25攻击5点防御 ";
         }
         else if(this._currentTarget._roleType == "懒惰")
         {
            this.mcFood["txtAddProperty"].text = "喂养成功增加15攻击8点防御 ";
         }
         this.mcFood["btnFood"].addEventListener(MouseEvent.CLICK,this.addPetProperty);
         this.mcFood["btnClose"].addEventListener(MouseEvent.CLICK,this.closeMcFood);
      }
      
      private function closeMcFood(param1:MouseEvent) : void
      {
         this.mcFood.visible = false;
         this.mcFood["btnFood"].removeEventListener(MouseEvent.CLICK,this.addPetProperty);
         this.mcFood["btnClose"].removeEventListener(MouseEvent.CLICK,this.closeMcFood);
      }
      
      private function onReleasePetHandler(param1:MouseEvent) : void
      {
         this.makeSure.visible = true;
         if(!this._currentTarget)
         {
            this.makeSure["message"].text = "请选择一只需要放生的灵兽！";
         }
         else if(this._currentTarget == this._master.pet || this._master.getPlayer()._petsVector.length == 1)
         {
            this.makeSure["message"].text = "无法放生已携带的灵兽！";
         }
         else
         {
            this.makeSure["message"].text = "你确定要放生这只灵兽吗？";
         }
         this.makeSure["btnOk"].addEventListener(MouseEvent.CLICK,this.sureHandler);
         this.makeSure["btnNo"].addEventListener(MouseEvent.CLICK,this.cancelHandler);
      }
      
      private function sureHandler(param1:MouseEvent) : void
      {
         this.makeSure["message"].text = "";
         this.makeSure["btnOk"].removeEventListener(MouseEvent.CLICK,this.sureHandler);
         this.makeSure["btnNo"].removeEventListener(MouseEvent.CLICK,this.cancelHandler);
         this.makeSure.visible = false;
         if(this._currentTarget == this._master.pet || !this._currentTarget)
         {
            return;
         }
         var _loc2_:int = int(this._master.getPlayer()._petsVector.indexOf(this._currentTarget));
         if(_loc2_ != -1)
         {
            this._master.getPlayer()._petsVector.splice(_loc2_,1);
            this._currentTarget = null;
            this._pet = null;
            if(this._master.pet)
            {
               this._pet = this._master.pet;
            }
         }
         this.init();
      }
      
      private function cancelHandler(param1:MouseEvent) : void
      {
         this.makeSure["message"].text = "";
         this.makeSure["btnOk"].removeEventListener(MouseEvent.CLICK,this.sureHandler);
         this.makeSure["btnNo"].removeEventListener(MouseEvent.CLICK,this.cancelHandler);
         this.makeSure.visible = false;
      }
      
      private function onTakePetHandler(param1:MouseEvent) : void
      {
         var _loc2_:int = 0;
         if(this._currentTarget == this._master.pet && !this._master.pet.isDead())
         {
            this._pet = this._currentTarget;
            ThreeKingdoms._instance._town.removeChild(this._master.pet);
            _loc2_ = int(ThreeKingdoms._gameWorld._pets.indexOf(this._master.pet));
            if(_loc2_ != -1)
            {
               ThreeKingdoms._gameWorld._pets.splice(_loc2_,1);
            }
            this._master.pet = null;
         }
         else if(this._currentTarget)
         {
            this._pet = this._currentTarget;
            this._master.setPet(this._currentTarget);
         }
         this.init();
      }
      
      private function init() : void
      {
         if(this._pet)
         {
            this.mcPetName.visible = true;
            this._pet.initAR();
            this.mcPetName.mcIcon.gotoAndStop(this._pet._roleFrameID);
            this.mcPetName.txtPetName.text = this._pet._roleName;
            this.mcPetName.txtPetDescription.text = this._pet._description;
            this.mcPetInfo.txtPetLevel.text = this._pet._properties.getLevel();
            this.mcPetInfo.txtPetAttackPower.text = this._pet._properties.getTotalAttackPower();
            this.mcPetInfo.txtPetHealthPoint.text = this._pet._properties.getCurrentHealthPoint() + "/" + this._pet._properties.getTotalHealthPoint();
            this.mcPetInfo.txtPetManaPoint.text = this._pet._properties.getCurrentManaPoint() + "/" + this._pet._properties.getTotalManaPoint();
            this.mcPetInfo.txtPetResistance.text = this._pet._properties.getResistance();
            this.mcPetInfo.txtPetType.text = this._pet._roleType;
            if(this._pet is Pet2)
            {
               this.mcPetInfo.mcPetSkillIcon1.icon.gotoAndStop(2);
               this.mcPetInfo.mcPetSkillIcon2.icon.gotoAndStop(1);
               this.mcPetInfo.mcPetSkillIcon3.icon.gotoAndStop(1);
               this.mcPetInfo.mcPetSkillIcon4.icon.gotoAndStop(1);
            }
            else if(this._pet is Pet3)
            {
               this.mcPetInfo.mcPetSkillIcon3.icon.gotoAndStop(2);
               this.mcPetInfo.mcPetSkillIcon2.icon.gotoAndStop(1);
               this.mcPetInfo.mcPetSkillIcon1.icon.gotoAndStop(1);
               this.mcPetInfo.mcPetSkillIcon4.icon.gotoAndStop(1);
            }
            else if(this._pet is Pet4)
            {
               this.mcPetInfo.mcPetSkillIcon2.icon.gotoAndStop(2);
               this.mcPetInfo.mcPetSkillIcon1.icon.gotoAndStop(1);
               this.mcPetInfo.mcPetSkillIcon3.icon.gotoAndStop(1);
               this.mcPetInfo.mcPetSkillIcon4.icon.gotoAndStop(1);
            }
            else if(this._pet is Pet5)
            {
               this.mcPetInfo.mcPetSkillIcon4.icon.gotoAndStop(2);
               this.mcPetInfo.mcPetSkillIcon2.icon.gotoAndStop(1);
               this.mcPetInfo.mcPetSkillIcon1.icon.gotoAndStop(1);
               this.mcPetInfo.mcPetSkillIcon3.icon.gotoAndStop(1);
            }
            this.showPets();
         }
         else
         {
            this.showPets();
            this.mcPetName.mcIcon.gotoAndStop(6);
            this.mcPetName.txtPetName.text = "";
            this.mcPetName.txtPetDescription.text = "";
            this.mcPetInfo.txtPetLevel.text = "";
            this.mcPetInfo.txtPetAttackPower.text = "";
            this.mcPetInfo.txtPetHealthPoint.text = "";
            this.mcPetInfo.txtPetManaPoint.text = "";
            this.mcPetInfo.txtPetResistance.text = "";
            this.mcPetInfo.txtPetType.text = "";
            this.mcPetInfo.mcPetSkillIcon4.icon.gotoAndStop(1);
            this.mcPetInfo.mcPetSkillIcon2.icon.gotoAndStop(1);
            this.mcPetInfo.mcPetSkillIcon1.icon.gotoAndStop(1);
            this.mcPetInfo.mcPetSkillIcon3.icon.gotoAndStop(1);
         }
      }
      
      private function onCloseHandler(param1:MouseEvent) : void
      {
         this.removeChild(this.btnClose);
         stage.focus = null;
         GameUtility.clearDisplayList(Package(this.parent));
         if(Package(this.parent))
         {
            Package(this.parent).parent.removeChild(Package(this.parent));
         }
         ThreeKingdoms._gameWorld.continueGame();
      }
      
      private function showCohesionIcon(param1:uint) : void
      {
         var _loc2_:int = 0;
         while(_loc2_ < 10)
         {
            this.mcPetInfo.mcCohesion["c" + _loc2_].gotoAndStop(1);
            _loc2_++;
         }
         var _loc3_:int = 0;
         while(_loc3_ < param1)
         {
            this.mcPetInfo.mcCohesion["c" + _loc3_].gotoAndStop(2);
            _loc3_++;
         }
      }
      
      private function showPets() : void
      {
         var _loc3_:MovieClip = null;
         var _loc4_:MovieClip = null;
         var _loc5_:PetIcon = null;
         var _loc6_:Pet = null;
         var _loc7_:Petsign = null;
         var _loc1_:int = 0;
         while(_loc1_ < 5)
         {
            _loc3_ = MovieClip(this.mcPet["mcPet" + (_loc1_ + 1)].getChildByName("icon"));
            _loc4_ = MovieClip(this.mcPet["mcPet" + (_loc1_ + 1)].getChildByName("sign"));
            if(_loc4_)
            {
               if(this.mcPet["mcPet" + (_loc1_ + 1)].contains(_loc4_))
               {
                  this.mcPet["mcPet" + (_loc1_ + 1)].removeChild(_loc4_);
               }
            }
            if(_loc3_)
            {
               if(this.mcPet["mcPet" + (_loc1_ + 1)])
               {
                  if(this.mcPet["mcPet" + (_loc1_ + 1)].contains(_loc3_))
                  {
                     this.mcPet["mcPet" + (_loc1_ + 1)].removeChild(_loc3_);
                  }
               }
            }
            _loc1_++;
         }
         var _loc2_:int = 0;
         while(_loc2_ < this._master.getPlayer()._petsVector.length)
         {
            _loc5_ = new PetIcon();
            _loc5_.name = "icon";
            this.mcPet["mcPet" + (_loc2_ + 1)].addChild(_loc5_);
            _loc5_.y = 15;
            _loc6_ = this._master.getPlayer()._petsVector[_loc2_] as Pet;
            if(_loc6_ == this._master.pet)
            {
               _loc7_ = new Petsign();
               _loc7_.name = "sign";
               this.mcPet["mcPet" + (_loc2_ + 1)].addChild(_loc7_);
               _loc7_.x = 6;
               _loc7_.y = 50;
            }
            if(_loc6_ is Pet1)
            {
               _loc5_.gotoAndStop(1);
            }
            else if(_loc6_ is Pet2)
            {
               _loc5_.gotoAndStop(2);
            }
            else if(_loc6_ is Pet3)
            {
               _loc5_.gotoAndStop(3);
            }
            else if(_loc6_ is Pet4)
            {
               _loc5_.gotoAndStop(4);
            }
            else if(_loc6_ is Pet5)
            {
               _loc5_.gotoAndStop(5);
            }
            _loc2_++;
         }
      }
   }
}

