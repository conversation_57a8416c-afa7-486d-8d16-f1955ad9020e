package actors.map
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import util.GameUtility;
   
   public class Game<PERSON><PERSON> extends MovieClip
   {
      
      public var level1:MovieClip;
      
      public var level2:MovieClip;
      
      public var level3:MovieClip;
      
      public var level4:MovieClip;
      
      public var level5:MovieClip;
      
      public var level6:MovieClip;
      
      public var level7:MovieClip;
      
      public var level8:MovieClip;
      
      public var level9:MovieClip;
      
      public var fb1:MovieClip;
      
      public var fb2:MovieClip;
      
      public var fb3:MovieClip;
      
      public var fb4:MovieClip;
      
      public var tk:ThreeKingdoms;
      
      public var mcCloud:MovieClip;
      
      public function GameMap()
      {
         super();
         this.mcCloud.mouseChildren = false;
         this.mcCloud.mouseEnabled = false;
         this.tk = ThreeKingdoms._instance;
         this.addEventListener(Event.ADDED_TO_STAGE,this.added,false,0,true);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.removed,false,0,true);
      }
      
      private function added(param1:Event) : void
      {
         var _loc2_:* = 1;
         while(_loc2_ <= ThreeKingdoms._instance._currentMaxLevel)
         {
            this["level" + _loc2_].mouseChildren = false;
            this["level" + _loc2_].buttonMode = true;
            this["level" + _loc2_].mouseEnabled = true;
            this["level" + _loc2_].addEventListener(MouseEvent.CLICK,this.onMouseClickHandler);
            this["level" + _loc2_].addEventListener(MouseEvent.MOUSE_OVER,this.onMouseOverHandler);
            this["level" + _loc2_].addEventListener(MouseEvent.MOUSE_OUT,this.onMouseOutHandler);
            _loc2_++;
         }
         if(this.tk._currentMaxLevel >= 5)
         {
            this.fb2.buttonMode = true;
            this.fb2.gotoAndStop(2);
            this.fb2.addEventListener(MouseEvent.CLICK,this.onMouseClickHandler);
         }
         if(this.tk._currentMaxLevel >= 7)
         {
            this.fb3.buttonMode = true;
            this.fb3.gotoAndStop(2);
            this.fb3.addEventListener(MouseEvent.CLICK,this.onMouseClickHandler);
         }
         if(this.tk._currentMaxLevel >= 4)
         {
            this.fb1.buttonMode = true;
            this.fb1.gotoAndStop(2);
            this.fb1.addEventListener(MouseEvent.CLICK,this.onMouseClickHandler);
         }
         if(this.tk._currentMaxLevel >= 8)
         {
            this.fb4.buttonMode = true;
            this.fb4.gotoAndStop(2);
            this.fb4.addEventListener(MouseEvent.CLICK,this.onMouseClickHandler);
         }
         this.mcCloud.gotoAndStop(this.tk._currentMaxLevel);
         this["level" + this.tk._currentMaxLevel].gotoAndStop(3);
      }
      
      private function onMouseOutHandler(param1:MouseEvent) : void
      {
         if(param1.currentTarget != this["level" + this.tk._currentMaxLevel])
         {
            param1.currentTarget.gotoAndStop(1);
         }
         else
         {
            param1.currentTarget.gotoAndStop(3);
         }
      }
      
      private function onMouseOverHandler(param1:MouseEvent) : void
      {
         param1.currentTarget.gotoAndStop(2);
      }
      
      private function onMouseClickHandler(param1:MouseEvent) : void
      {
         switch(param1.currentTarget)
         {
            case this.fb1:
               this.tk._currentLevel = 10;
               break;
            case this.fb2:
               this.tk._currentLevel = 11;
               break;
            case this.fb3:
               this.tk._currentLevel = 12;
               break;
            case this.fb4:
               this.tk._currentLevel = 13;
               break;
            default:
               this.tk._currentLevel = int(String(param1.currentTarget.name).substr(5,1));
               param1.currentTarget.gotoAndStop(1);
         }
         this.loadEnemy();
         this.destory();
      }
      
      private function loadEnemy() : void
      {
         if(ThreeKingdoms._instance.thisLevelEnemyIsLoaded[this.tk._currentLevel - 1] == 0)
         {
            this.tk._loadingIndex = 2;
            switch(this.tk._currentLevel)
            {
               case 1:
                  this.tk._loader.add("assets/enemy1.swf");
                  if(!this.tk._loader.get("assets/enemy2.swf"))
                  {
                     this.tk._loader.add("assets/enemy2.swf");
                  }
                  this.tk._loader.add("assets/enemy3.swf");
                  break;
               case 2:
                  if(!this.tk._loader.get("assets/enemy2.swf"))
                  {
                     this.tk._loader.add("assets/enemy2.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy6.swf"))
                  {
                     this.tk._loader.add("assets/enemy6.swf");
                  }
                  this.tk._loader.add("assets/enemy4.swf");
                  this.tk._loader.add("assets/enemy5.swf");
                  break;
               case 3:
                  if(!this.tk._loader.get("assets/enemy6.swf"))
                  {
                     this.tk._loader.add("assets/enemy6.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy8.swf"))
                  {
                     this.tk._loader.add("assets/enemy8.swf");
                  }
                  this.tk._loader.add("assets/enemy7.swf");
                  this.tk._loader.add("assets/enemy9.swf");
                  break;
               case 4:
                  if(!this.tk._loader.get("assets/enemy10.swf"))
                  {
                     this.tk._loader.add("assets/enemy10.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy8.swf"))
                  {
                     this.tk._loader.add("assets/enemy8.swf");
                  }
                  this.tk._loader.add("assets/enemy11.swf");
                  this.tk._loader.add("assets/enemy25.swf");
                  break;
               case 5:
                  if(!this.tk._loader.get("assets/enemy10.swf"))
                  {
                     this.tk._loader.add("assets/enemy10.swf");
                  }
                  this.tk._loader.add("assets/enemy12.swf");
                  if(!this.tk._loader.get("assets/enemy13.swf"))
                  {
                     this.tk._loader.add("assets/enemy13.swf");
                  }
                  this.tk._loader.add("assets/enemy14.swf");
                  break;
               case 6:
                  if(Boolean(this.tk._loader.get("assets/enemy17.swf")) && Boolean(this.tk._loader.get("assets/enemy18.swf")) && Boolean(this.tk._loader.get("assets/enemy15.swf")) && Boolean(this.tk._loader.get("assets/enemy16.swf")))
                  {
                     this.tk.thisLevelEnemyIsLoaded[this.tk._currentLevel - 1] = 1;
                     this.tk.startGame(this.tk._currentLevel);
                  }
                  if(!this.tk._loader.get("assets/enemy18.swf"))
                  {
                     this.tk._loader.add("assets/enemy18.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy17.swf"))
                  {
                     this.tk._loader.add("assets/enemy17.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy16.swf"))
                  {
                     this.tk._loader.add("assets/enemy16.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy15.swf"))
                  {
                     this.tk._loader.add("assets/enemy15.swf");
                  }
                  break;
               case 7:
                  if(Boolean(this.tk._loader.get("assets/enemy19.swf")) && Boolean(this.tk._loader.get("assets/enemy26.swf")) && Boolean(this.tk._loader.get("assets/enemy20.swf")) && Boolean(this.tk._loader.get("assets/enemy22.swf")))
                  {
                     this.tk.thisLevelEnemyIsLoaded[this.tk._currentLevel - 1] = 1;
                     this.tk.startGame(this.tk._currentLevel);
                  }
                  if(!this.tk._loader.get("assets/enemy19.swf"))
                  {
                     this.tk._loader.add("assets/enemy19.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy20.swf"))
                  {
                     this.tk._loader.add("assets/enemy20.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy26.swf"))
                  {
                     this.tk._loader.add("assets/enemy26.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy22.swf"))
                  {
                     this.tk._loader.add("assets/enemy22.swf");
                  }
                  break;
               case 8:
                  if(!this.tk._loader.get("assets/enemy21.swf"))
                  {
                     this.tk._loader.add("assets/enemy21.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy26.swf"))
                  {
                     this.tk._loader.add("assets/enemy26.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy20.swf"))
                  {
                     this.tk._loader.add("assets/enemy20.swf");
                  }
                  this.tk._loader.add("assets/enemy24.swf");
                  break;
               case 9:
                  if(!this.tk._loader.get("assets/enemy22.swf"))
                  {
                     this.tk._loader.add("assets/enemy22.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy21.swf"))
                  {
                     this.tk._loader.add("assets/enemy21.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy18.swf"))
                  {
                     this.tk._loader.add("assets/enemy18.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy17.swf"))
                  {
                     this.tk._loader.add("assets/enemy17.swf");
                  }
                  this.tk._loader.add("assets/enemy23.swf");
                  this.tk._loader.add("assets/gameWin.swf");
                  break;
               case 10:
                  if(!this.tk._loader.get("assets/enemy10.swf"))
                  {
                     this.tk._loader.add("assets/enemy10.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy13.swf"))
                  {
                     this.tk._loader.add("assets/enemy13.swf");
                  }
                  this.tk._loader.add("assets/enemy33.swf");
                  this.tk._loader.add("assets/enemy34.swf");
                  break;
               case 11:
                  this.tk._loader.add("assets/enemy27.swf");
                  this.tk._loader.add("assets/enemy28.swf");
                  this.tk._loader.add("assets/enemy29.swf");
                  if(!this.tk._loader.get("assets/enemy16.swf"))
                  {
                     this.tk._loader.add("assets/enemy16.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy15.swf"))
                  {
                     this.tk._loader.add("assets/enemy15.swf");
                  }
                  if(!this.tk._loader.get("assets/enemy19.swf"))
                  {
                     this.tk._loader.add("assets/enemy19.swf");
                  }
                  break;
               case 12:
                  this.tk._loader.add("assets/enemy30.swf");
                  this.tk._loader.add("assets/enemy31.swf");
                  this.tk._loader.add("assets/enemy32.swf");
                  break;
               case 13:
                  this.tk._loader.add("assets/enemy35.swf");
                  this.tk._loader.add("assets/enemy36.swf");
                  this.tk._loader.add("assets/enemy37.swf");
            }
            ThreeKingdoms._instance.thisLevelEnemyIsLoaded[this.tk._currentLevel - 1] = 1;
         }
         else
         {
            this.tk.startGame(this.tk._currentLevel);
         }
      }
      
      private function destory() : void
      {
         GameUtility.clearDisplayList(this);
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         this.tk._gameMap = null;
      }
      
      private function removed(param1:Event) : void
      {
         var _loc2_:* = 1;
         while(_loc2_ <= this.tk._currentMaxLevel)
         {
            this["level" + _loc2_].removeEventListener(MouseEvent.CLICK,this.onMouseClickHandler);
            this["level" + _loc2_].removeEventListener(MouseEvent.MOUSE_OVER,this.onMouseOverHandler);
            this["level" + _loc2_].removeEventListener(MouseEvent.MOUSE_OUT,this.onMouseOutHandler);
            _loc2_++;
         }
         if(this.tk._currentMaxLevel >= 5)
         {
            this.fb2.removeEventListener(MouseEvent.CLICK,this.onMouseClickHandler);
         }
         if(this.tk._currentMaxLevel >= 4)
         {
            this.fb1.removeEventListener(MouseEvent.CLICK,this.onMouseClickHandler);
         }
         if(this.tk._currentMaxLevel >= 7)
         {
            this.fb3.removeEventListener(MouseEvent.CLICK,this.onMouseClickHandler);
         }
         if(this.tk._currentMaxLevel >= 8)
         {
            this.fb4.removeEventListener(MouseEvent.CLICK,this.onMouseClickHandler);
         }
      }
   }
}

