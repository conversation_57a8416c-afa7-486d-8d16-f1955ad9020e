package actors.pack
{
   import actors.Hero;
   import actors.equipments.Equipment;
   import actors.equipments.Suit;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.filters.BitmapFilterQuality;
   import flash.filters.GlowFilter;
   import flash.text.TextFormat;
   
   public class PropertyInstructionPanel extends Sprite
   {
      
      private var _equipment:Equipment;
      
      private var _suit:Suit;
      
      private var _index:uint = 0;
      
      private var _bigWidth:uint = 0;
      
      public var mcTip1:MovieClip;
      
      public var mcTip2:MovieClip;
      
      private var _additionalBaseAttackPower:Number = 0.2;
      
      private var _additionalBaseArmor:Number = 0.4;
      
      private var _additionalBaseGold:Number = 0.8;
      
      private var _additionalAttackPower:Number = 2.3;
      
      private var _additionalMiss:Number = 0;
      
      private var _additionalCrit:Number = 0;
      
      private var _addtionalHealthPointRegeneration:Number = 0.1;
      
      private var _additionalManaPointRegeneration:Number = 0.2;
      
      private var _additionalMoveSpeed:Number = 0.1;
      
      private var _additionalHealthSkillPoint:Number = 0;
      
      private var _additionalResistance:Number = 0.1;
      
      private var _additionalHealthPointRegeneration:Number = 0.3;
      
      private var _addLuckyPoint:Number;
      
      private var _hasExtraProperty:Boolean = false;
      
      private var suilength:Array;
      
      public function PropertyInstructionPanel(param1:Equipment)
      {
         super();
         this.mouseEnabled = false;
         this.mouseChildren = false;
         this._equipment = param1;
         if(this._equipment._type == "armor" && (this._equipment.getEquipmentQualityByIndex(this._equipment.index) == "仙器" || this._equipment.getEquipmentQualityByIndex(this._equipment.index) == "神器") || this._equipment._type != "weapon" && this._equipment._type != "armor" && this._equipment._type != "suit" && this._equipment._type != "special" && this._equipment._type != "lantern" && this._equipment._type != "valentine")
         {
            this._suit = ThreeKingdoms._instance._suits.getSuitByID(this._equipment._id) as Suit;
         }
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      private function onAddedToStageHandler(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.drawInformation();
      }
      
      private function drawInformation() : void
      {
         this.mcTip1.txtWord.visible = false;
         var _loc1_:TextFormat = new TextFormat();
         if(this._equipment.getEquipmentQualityByIndex(this._equipment.index) != null)
         {
            switch(this._equipment.getEquipmentQualityByIndex(this._equipment.index))
            {
               case "铁器":
                  _loc1_.color = 16777215;
                  this.removeChild(this.mcTip2);
                  this.mcTip2 = null;
                  break;
               case "名器":
                  _loc1_.color = 65280;
                  this.removeChild(this.mcTip2);
                  this.mcTip2 = null;
                  break;
               case "宝器":
                  _loc1_.color = 33023;
                  this.removeChild(this.mcTip2);
                  this.mcTip2 = null;
                  break;
               case "仙器":
                  _loc1_.color = 16711935;
                  if(this._equipment._type == "weapon")
                  {
                     this.removeChild(this.mcTip2);
                     this.mcTip2 = null;
                  }
                  else
                  {
                     this.drawSuitProperty();
                  }
                  break;
               case "神器":
                  _loc1_.color = 14643200;
                  if(this._equipment._type == "weapon")
                  {
                     this.removeChild(this.mcTip2);
                     this.mcTip2 = null;
                  }
                  else
                  {
                     this.drawSuitProperty();
                  }
                  break;
               case "周年版":
                  _loc1_.color = 16711680;
                  if(this._equipment._type == "weapon")
                  {
                     this.removeChild(this.mcTip2);
                     this.mcTip2 = null;
                  }
                  else
                  {
                     this.drawSuitProperty();
                  }
                  break;
               case "绝版":
                  _loc1_.color = 16711680;
                  if(this._equipment._type == "weapon")
                  {
                     this.removeChild(this.mcTip2);
                     this.mcTip2 = null;
                  }
                  else
                  {
                     this.drawSuitProperty();
                  }
                  break;
               default:
                  _loc1_.color = this._equipment._color;
            }
         }
         else
         {
            _loc1_.color = 14643200;
            if(this._equipment._type == "suit" || this._equipment._type == "special" || this._equipment._type == "lantern" || this._equipment._type == "valentine")
            {
               this.removeChild(this.mcTip2);
               this.mcTip2 = null;
            }
            else
            {
               this.drawSuitProperty();
            }
         }
         _loc1_.bold = true;
         this.mcTip1.txtName.text = this._equipment._name;
         var _loc2_:String = this._equipment.getEquipmentQualityByIndex(this._equipment.index);
         if(_loc2_)
         {
            this.mcTip1.txtQuality.text = _loc2_;
         }
         this.mcTip1.txtName.setTextFormat(_loc1_);
         this.mcTip1.txtQuality.setTextFormat(_loc1_);
         this.drawType();
         this.drawBaseAttibute();
         this.drawInstruction();
         this.drawValue();
         this.drawProperty();
      }
      
      private function drawBaseAttibute() : void
      {
         var _loc1_:TextFormat = new TextFormat();
         _loc1_.color = 16777215;
         _loc1_.bold = true;
         if(this._equipment._type == "armor")
         {
            this.mcTip1.txtProperty.text = "基本防御: " + int(int(this._equipment.getEquipmentBasePropertyByIndex(this._equipment.index)) + this._additionalBaseArmor);
         }
         else if(this._equipment._type == "weapon")
         {
            this.mcTip1.txtProperty.text = "基本攻击: " + int(int(this._equipment.getEquipmentBasePropertyByIndex(this._equipment.index)) + this._additionalBaseAttackPower);
         }
         else if(this._equipment._type == "ring")
         {
            this.mcTip1.txtProperty.text = "";
         }
         else if(this._equipment._type == "necklace")
         {
            this.mcTip1.txtProperty.text = "";
         }
         else if(this._equipment._type == "amulet")
         {
            this.mcTip1.txtProperty.text = "";
         }
         else if(this._equipment._type == "lantern" || this._equipment._type == "valentine")
         {
            this.mcTip1.txtProperty.text = "使用方式:";
         }
         else if(this._equipment._type == "special")
         {
            this.mcTip1.txtProperty.text = "喂养方式:";
         }
         this.mcTip1.txtProperty.setTextFormat(_loc1_);
      }
      
      private function drawValue() : void
      {
         var _loc1_:TextFormat = new TextFormat();
         _loc1_.color = 16776960;
         if(Boolean(this._equipment._suitProperty) && Boolean(this._equipment._suitProperty.isNew))
         {
            if(this._equipment._worth[this._equipment.index])
            {
               this.mcTip1.txtWorth.text = String(Math.round((this._equipment._worth[this._equipment.index] - 0.1) * 10)) + "元宝";
            }
            else
            {
               this.mcTip1.txtWorth.text = 0 + "元宝";
            }
         }
         else if(this._equipment._worth[this._equipment.index])
         {
            this.mcTip1.txtWorth.text = String(this._equipment._worth[this._equipment.index]) + "元宝";
         }
         else
         {
            this.mcTip1.txtWorth.text = String(0) + "元宝";
         }
         this.mcTip1.txtWorth.setTextFormat(_loc1_);
      }
      
      private function drawInstruction() : void
      {
         var _loc1_:int = 0;
         var _loc2_:TextFormat = null;
         if(this._equipment._suitProperty != null && this._equipment._suitProperty.startLevel != undefined)
         {
            if(this._equipment._category == "helmet" || this._equipment._category == "shoes" || this._equipment._category == "trousers")
            {
               this.mcTip1.txtAddProperty.text = "+" + String(this._equipment._suitProperty.startLevel * 1);
            }
            else if(this._equipment._category == "coat")
            {
               this.mcTip1.txtAddProperty.text = "+" + String(this._equipment._suitProperty.startLevel * 2);
            }
            else if(this._equipment._category == "sword")
            {
               this.mcTip1.txtAddProperty.text = "+" + String(this._equipment._suitProperty.startLevel * 10);
            }
            _loc1_ = 1;
            while(_loc1_ <= this._equipment._suitProperty.startLevel)
            {
               this.mcTip1["s" + _loc1_].gotoAndStop(2);
               _loc1_++;
            }
         }
         else
         {
            this.mcTip1.txtAddProperty.visible = false;
         }
         if(this._equipment._suitProperty != null && this._equipment._suitProperty.isExclusive != undefined && Boolean(this._equipment._suitProperty.isExclusive))
         {
            this.mcTip1.txtInstruction.visible = false;
            this.mcTip1.txtWord.visible = true;
            this.mcTip1.txtUser.text = this._equipment._suitProperty.txtExclusive;
         }
         else
         {
            this.mcTip1.txtUser.visible = false;
            _loc2_ = new TextFormat();
            _loc2_.color = 16777215;
            this.mcTip1.txtInstruction.wordWrap = true;
            this.mcTip1.txtInstruction.text = "说明：" + this._equipment._instruction;
            this.mcTip1.txtInstruction.setTextFormat(_loc2_);
         }
      }
      
      private function drawType() : void
      {
         var _loc2_:String = null;
         var _loc3_:String = null;
         var _loc1_:TextFormat = new TextFormat();
         _loc1_.color = 16777215;
         if(this._equipment._type == "armor")
         {
            _loc2_ = "防具";
         }
         else if(this._equipment._type == "weapon")
         {
            _loc2_ = "武器";
         }
         else if(this._equipment._type == "ring")
         {
            _loc2_ = "戒指";
         }
         else if(this._equipment._type == "necklace")
         {
            _loc2_ = "项链";
         }
         else if(this._equipment._type == "amulet")
         {
            _loc2_ = "护符";
         }
         else if(this._equipment._type == "special")
         {
            _loc2_ = "食物";
         }
         else if(this._equipment._type == "lantern" || this._equipment._type == "valentine")
         {
            _loc2_ = "物品";
         }
         else if(this._equipment._type == "suit")
         {
            _loc2_ = "时装";
         }
         if(this._equipment._user == "LiuBei")
         {
            _loc3_ = "刘备";
         }
         else if(this._equipment._user == "GuanYu")
         {
            _loc3_ = "关羽";
         }
         else if(this._equipment._user == "ZhangFei")
         {
            _loc3_ = "张飞";
         }
         else if(this._equipment._user == "")
         {
            _loc3_ = "通用";
         }
         else if(this._equipment._user == "Pet")
         {
            _loc3_ = "宠物";
         }
         this.mcTip1.txtType.text = _loc3_ + "·" + _loc2_;
      }
      
      private function drawProperty() : void
      {
         var _loc1_:Object = null;
         this.mcTip1.txtExtraProperty.text = "";
         if(this._equipment._additionalProperty.length == 1)
         {
            _loc1_ = this._equipment._additionalProperty[0] as Object;
         }
         else
         {
            _loc1_ = this._equipment._additionalProperty[this._equipment.index] as Object;
         }
         if(_loc1_)
         {
            if(_loc1_.additionalHealthPoint != undefined || _loc1_.additionalManaPoint != undefined || _loc1_.additionalAttackPower != undefined || _loc1_.additionalResistance != undefined || _loc1_.additionalCrit != undefined || _loc1_.additionalMiss != undefined || _loc1_.additionalHealthPointRegeneration != undefined || _loc1_.additionalManaPointRegeneration != undefined || _loc1_.additionalMoveSpeed != undefined || _loc1_.addHealthSkillPoint != undefined || _loc1_.addLuckyPoint != undefined || _loc1_.bloodThirsty != undefined)
            {
               this._hasExtraProperty = true;
            }
            else
            {
               this._hasExtraProperty = false;
               this.drawSimpleProperty("附加属性","无");
            }
            if(_loc1_.additionalHealthPoint != undefined)
            {
               this.drawSimpleProperty("附加生命值",int(_loc1_.additionalHealthPoint));
            }
            if(_loc1_.additionalManaPoint != undefined)
            {
               this.drawSimpleProperty("附加魔法",int(_loc1_.additionalManaPoint));
            }
            if(_loc1_.additionalAttackPower != undefined)
            {
               this.drawSimpleProperty("附加攻击力",int(_loc1_.additionalAttackPower + this._additionalAttackPower));
            }
            if(_loc1_.additionalResistance != undefined)
            {
               this.drawSimpleProperty("附加防御力",int(_loc1_.additionalResistance + this._additionalResistance));
            }
            if(_loc1_.additionalCrit != undefined)
            {
               this.drawSimpleProperty("附加暴击率",int(_loc1_.additionalCrit * 100) + "%");
            }
            if(_loc1_.additionalMiss != undefined)
            {
               this.drawSimpleProperty("附加闪避率",int(_loc1_.additionalMiss * 100) + "%");
            }
            if(_loc1_.additionalHealthPointRegeneration != undefined)
            {
               this.drawSimpleProperty("附加回血",int(_loc1_.additionalHealthPointRegeneration + this._additionalHealthPointRegeneration));
            }
            if(_loc1_.additionalManaPointRegeneration != undefined)
            {
               this.drawSimpleProperty("附加回魔",int(_loc1_.additionalManaPointRegeneration + this._additionalManaPointRegeneration));
            }
            if(_loc1_.additionalMoveSpeed != undefined)
            {
               this.drawSimpleProperty("附加移动速度",int(_loc1_.additionalMoveSpeed + this._additionalMoveSpeed));
            }
            if(_loc1_.addHealthSkillPoint != undefined)
            {
               this.drawSimpleProperty("附加恢复术治愈量",int(_loc1_.addHealthSkillPoint + this._additionalHealthSkillPoint));
            }
            if(_loc1_.addLuckyPoint != undefined)
            {
               this.drawSimpleProperty("附加装备爆率",_loc1_.addLuckyPoint * 100 + "%");
            }
            if(_loc1_.bloodThirsty != undefined)
            {
               this.drawSimpleProperty("附加嗜血量",_loc1_.bloodThirsty * 100 + "%");
            }
         }
         else if(this._equipment._type == "special")
         {
            this.mcTip1.txtExtraProperty.text = "玩家可以通过灵兽背包喂养来增加和灵兽的亲密度，亲密度提升会增加灵兽大量的攻击和防御等，喂养会消耗圣灵果。";
         }
         else if(this._equipment._type == "lantern" || this._equipment._type == "valentine")
         {
            this.mcTip1.txtExtraProperty.text = "双击使用";
         }
      }
      
      private function drawSuitProperty() : *
      {
         if(this.parent)
         {
            this.suilength = Hero(Package(this.parent).theRole)._properties.getSuitIDLength();
         }
         if(Hero(Package(this.parent).theRole).getPlayer()._currentEquipments.indexOf(this._equipment) == -1)
         {
            this.mcTip2.txtSuitName.text = "[" + "套装：" + this._suit._suitName + "]:" + "(" + 0 + "/" + this._suit._suitCount + ")";
            this.mcTip2.txtSuitName.textColor = 12698049;
            this.mcTip2.txtExtraProperty1.textColor = 12698049;
            this.mcTip2.txtExtraProperty2.textColor = 12698049;
            this.mcTip2.txtExtraProperty3.textColor = 12698049;
            this.mcTip2.txtExtraProperty4.textColor = 12698049;
         }
         else
         {
            this.mcTip2.txtSuitName.text = "[" + "套装：" + this._suit._suitName + "]:" + "(" + this.suilength[this._suit._suitID - 1] + "/" + this._suit._suitCount + ")";
            if(this.suilength[this._suit._suitID - 1] == 1)
            {
               this.mcTip2.txtExtraProperty1.textColor = 12698049;
               this.mcTip2.txtExtraProperty2.textColor = 12698049;
               this.mcTip2.txtExtraProperty3.textColor = 12698049;
               this.mcTip2.txtExtraProperty4.textColor = 12698049;
            }
            else if(this.suilength[this._suit._suitID - 1] == 2)
            {
               this.mcTip2.txtExtraProperty2.textColor = 12698049;
               this.mcTip2.txtExtraProperty3.textColor = 12698049;
               this.mcTip2.txtExtraProperty4.textColor = 12698049;
            }
            else if(this.suilength[this._suit._suitID - 1] == 3)
            {
               this.mcTip2.txtExtraProperty3.textColor = 12698049;
               this.mcTip2.txtExtraProperty4.textColor = 12698049;
            }
         }
         this.mcTip2.txtExtraProperty1.text = "";
         this.mcTip2.txtExtraProperty2.text = "";
         this.mcTip2.txtExtraProperty3.text = "";
         this.mcTip2.txtExtraProperty4.text = "";
         if(this._suit._suitProperty[0].additionalHealthPoint != undefined)
         {
            this.mcTip2.txtExtraProperty1.text = "(2):HP增加 " + int(this._suit._suitProperty[0].additionalHealthPoint);
         }
         if(this._suit._suitProperty[0].additionalManaPoint != undefined)
         {
            this.mcTip2.txtExtraProperty1.text = "(2):HP增加 " + int(this._suit._suitProperty[0].additionalHealthPoint) + "," + "MP增加 " + int(this._suit._suitProperty[0].additionalManaPoint);
         }
         if(this._suit._suitProperty[1].additionalManaPoint != undefined)
         {
            this.mcTip2.txtExtraProperty2.text = "(3):MP增加 " + int(this._suit._suitProperty[1].additionalManaPoint);
         }
         if(this._suit._suitProperty[1].additionalResistance != undefined)
         {
            this.mcTip2.txtExtraProperty2.text = "(3):防御增加 " + int(this._suit._suitProperty[1].additionalResistance);
         }
         if(this._suit._suitProperty[2].additionalResistance != undefined)
         {
            this.mcTip2.txtExtraProperty3.text = "(4):防御增加 " + int(this._suit._suitProperty[2].additionalResistance);
         }
         if(this._suit._suitProperty[2].addHealthSkillPoint != undefined)
         {
            this.mcTip2.txtExtraProperty4.text = "(4):恢复术治愈量增加 " + int(this._suit._suitProperty[2].addHealthSkillPoint);
         }
         if(this._suit._suitProperty[2].bloodThirsty != undefined)
         {
            this.mcTip2.txtExtraProperty4.text = "(4):嗜血量增加 " + this._suit._suitProperty[2].bloodThirsty * 100 + "%";
         }
         if(this._suit._suitProperty[2].additionalCrit != undefined && this._suit._suitProperty[2].additionalMiss == undefined)
         {
            this.mcTip2.txtExtraProperty4.text = "(4):暴击 +" + this._suit._suitProperty[2].additionalCrit * 100 + "%";
         }
         if(this._suit._suitProperty[2].additionalMiss != undefined)
         {
            this.mcTip2.txtExtraProperty3.text = "(4):闪避 +" + this._suit._suitProperty[2].additionalMiss * 100 + "%" + "," + "暴击 +" + this._suit._suitProperty[2].additionalCrit * 100 + "%";
         }
         if(this._suit._suitProperty[2].revival != undefined)
         {
            this.mcTip2.txtExtraProperty4.text = "(4):死亡后复活,并恢复50%血量";
         }
         if(this._suit._suitProperty[2].addCrazy != undefined)
         {
            this.mcTip2.txtExtraProperty4.text = "(4):10%机率触发5秒内暴击率50%";
         }
         if(this._suit._suitProperty[1].addHealthregain != undefined)
         {
            if(this.suilength[this._suit._suitID - 1] == 3 && Hero(Package(this.parent).theRole).getPlayer()._currentEquipments.indexOf(this._equipment) != -1)
            {
               this.mcTip2.txtExtraProperty3.textColor = 3407616;
            }
            this.mcTip2.txtExtraProperty2.text = "(3)每关在血量小于30%时";
            this.mcTip2.txtExtraProperty3.text = "自动回复800血量共3次。";
         }
         if(this._suit._suitProperty[1].addInvincible != undefined)
         {
            if(this.suilength[this._suit._suitID - 1] == 3 && Hero(Package(this.parent).theRole).getPlayer()._currentEquipments.indexOf(this._equipment) != -1)
            {
               this.mcTip2.txtExtraProperty3.textColor = 3407616;
            }
            this.mcTip2.txtExtraProperty2.text = "(3)血量小于40%时触发技能";
            this.mcTip2.txtExtraProperty3.text = "绝对防御效果10秒内无敌。";
         }
      }
      
      private function drawSimpleProperty(param1:String, param2:*) : void
      {
         var _loc3_:TextFormat = new TextFormat();
         this.mcTip1.txtExtraProperty.appendText(param1 + ": " + param2 + "\n");
         if(!this._hasExtraProperty)
         {
            _loc3_.color = 161061264;
            this.mcTip1.txtExtraProperty.setTextFormat(_loc3_);
         }
      }
      
      private function getGlowFilter() : GlowFilter
      {
         return new GlowFilter(16777215,1,2,2,5.3,BitmapFilterQuality.HIGH,false,false);
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
   }
}

