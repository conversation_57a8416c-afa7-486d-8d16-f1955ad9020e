package actors.equipments
{
   import actors.Hero;
   import actors.pets.Pet;
   import base.GameObject;
   import com.greensock.TweenMax;
   import flash.display.MovieClip;
   import flash.net.URLLoader;
   import util.GameUtility;
   import util.HitTest;
   
   public class FallEquipments extends GameObject
   {
      
      private var _obj:Object;
      
      private var _isFirst:Boolean = true;
      
      private var _loader:URLLoader;
      
      private var _bossList:Array;
      
      private var _equipment:Equipment;
      
      private var _index:int;
      
      public function FallEquipments(param1:Object, param2:int)
      {
         var _loc3_:MovieClip = null;
         this._bossList = ["张梁","张宝","张角","王允","貂蝉","吕布","张辽","华雄","董卓"];
         super();
         this._obj = param1;
         _loc3_ = GameUtility.getLibraryObjectFromSWF("newfallEquipment12.swf","fall_" + param1.id);
         _loc3_.x = 0;
         _loc3_.y = 0;
         this._index = param2;
         this.addChild(_loc3_);
         this.collipse = new MovieClip();
         this.collipse.graphics.beginFill(16711680,0);
         this.collipse.graphics.drawRect(-20,-20,40,40);
         this.collipse.graphics.endFill();
         this.addChild(collipse);
      }
      
      override public function update() : void
      {
         var _loc2_:Hero = null;
         var _loc3_:int = 0;
         super.update();
         var _loc1_:uint = ThreeKingdoms._gameWorld._heroes.length;
         while(_loc1_-- > 0)
         {
            _loc2_ = ThreeKingdoms._gameWorld._heroes[_loc1_] as Hero;
            if(!(Math.abs(this.x - _loc2_.x) > 700 || _loc2_.isDead()))
            {
               if(Math.abs(this.y - _loc2_.y) < 200)
               {
                  this.belongsToWhom(_loc2_);
               }
            }
         }
         if(_count++ >= 360)
         {
            _count = 0;
            _loc3_ = int(this._bossList.indexOf(this._obj.name));
            if(_loc3_ == -1)
            {
               this.remove();
            }
         }
      }
      
      private function remove() : void
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         var _loc1_:int = int(ThreeKingdoms._gameWorld._otherList.indexOf(this));
         if(_loc1_ != -1)
         {
            ThreeKingdoms._gameWorld._otherList.splice(_loc1_,1);
            this._bossList = [];
            this._loader = null;
            this._obj = null;
            this._equipment = null;
         }
      }
      
      private function belongsToWhom(param1:Hero) : void
      {
         if(param1 is Pet)
         {
            return;
         }
         if(this._isFirst)
         {
            if(param1.body)
            {
               if(!this.hitTestObject(param1.collipse))
               {
                  return;
               }
               if(HitTest.complexHitTestObject(this,param1.collipse))
               {
                  switch(this._obj.type)
                  {
                     case "equipment":
                        if(param1.getPlayer()._equipmentsVector.length < 30)
                        {
                           ThreeKingdoms._instance._equipments.init();
                           this._equipment = ThreeKingdoms._instance._equipments.getEquipmentByID(this._obj.id) as Equipment;
                           if(this._equipment._quality.length != 0)
                           {
                              this._equipment.index = this._index;
                           }
                           param1.getPlayer()._equipmentsVector.push(this._equipment);
                           break;
                        }
                        return;
                        break;
                     case "special":
                        if(param1.getPlayer()._specialVector.length < 30)
                        {
                           ThreeKingdoms._instance._equipments.init();
                           this._equipment = ThreeKingdoms._instance._equipments.getEquipmentByID(this._obj.id) as Equipment;
                           if(this._equipment._quality.length != 0)
                           {
                              this._equipment.index = this._index;
                           }
                           param1.getPlayer()._specialVector.push(this._equipment);
                           break;
                        }
                        return;
                  }
                  this._isFirst = false;
                  TweenMax.to(this,0.8,{
                     "y":this.y - 100,
                     "alpha":0,
                     "onComplete":this.remove
                  });
               }
            }
         }
      }
   }
}

