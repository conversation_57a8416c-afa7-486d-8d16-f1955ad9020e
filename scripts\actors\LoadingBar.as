package actors
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol923")]
   public class LoadingBar extends MovieClip
   {
      
      public var bar:MovieClip;
      
      public var itemLoaded:TextField;
      
      public var itemsTotal:TextField;
      
      public function LoadingBar()
      {
         super();
      }
      
      public function setProcess(param1:int, param2:*, param3:*) : void
      {
         this.itemLoaded.text = String(param2);
         this.itemsTotal.text = String(param3);
         this.bar.gotoAndStop(param1);
      }
   }
}

