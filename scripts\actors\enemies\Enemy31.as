package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   
   public class Enemy31 extends Enemy
   {
      
      private var _isMetamorphosis:<PERSON>olean = false;
      
      private var count:int = 300;
      
      public function Enemy31(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 1900;
         this._object._alertRange = 1900;
         this._object._attackRange = 900;
         this._currentHealthPoint = 130000;
         this._totalHealthPoint = 130000;
         this._resistance = 130;
         this._experience = 5000;
         this._attackProbablity = 100;
         this._goldPrice = 5000;
         this._walkSpeed = 7;
         this.isBoss = true;
         this._probability = 0.2;
         this.enemyName = "吕布";
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":2,
            "attackBackVelocity":[5,5],
            "attackInterval":4,
            "attackPower":532 + Math.round(Math.random() * 26),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":2,
            "attackBackVelocity":[5,5],
            "attackInterval":4,
            "attackPower":532 + Math.round(Math.random() * 26),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":2,
            "attackBackVelocity":[5,5],
            "attackInterval":4,
            "attackPower":532 + Math.round(Math.random() * 26),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击4"] = {
            "hitMaxCount":2,
            "attackBackVelocity":[5,5],
            "attackInterval":4,
            "attackPower":532 + Math.round(Math.random() * 26),
            "attackType":"physical"
         };
         this._fallEquipmentsList = [{
            "id":23,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":24,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":25,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":26,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":86,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":87,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":88,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":89,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":49,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":50,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":51,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":52,
            "qualityID":[1],
            "type":"equipment"
         }];
      }
      
      override public function update() : void
      {
         if(this._currentHealthPoint <= this._totalHealthPoint / 3 && this._currentHealthPoint > 0)
         {
            if(!this._isMetamorphosis)
            {
               if(this.count > 0)
               {
                  --this.count;
               }
               if(this.count == 0)
               {
                  if(this.isAttacking() || this.isUnderAttack() || isJumping())
                  {
                     return;
                  }
                  this.realseSkill3();
                  this._isMetamorphosis = true;
               }
            }
         }
         super.update();
      }
      
      public function transfer() : void
      {
         var _loc1_:Enemy32 = _game.addEnemy(32,this.x,this.y) as Enemy32;
         _loc1_.setCurrentHealthPoint(_currentHealthPoint);
         this.removeThis();
      }
      
      public function setCurrentHealthPoint(param1:int) : void
      {
         this._currentHealthPoint = param1;
      }
      
      override public function startAttacking() : void
      {
         if(this._curAttackTarget)
         {
            if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) > 400 && GameUtility.getDistance(this,this._curAttackTarget) < 1000)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 120;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 250 && GameUtility.getDistance(this,this._curAttackTarget) <= 400)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 120;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 90;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) < 250)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 120;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 90;
                  }
                  else
                  {
                     this.attack();
                  }
               }
               else
               {
                  moveTowardHero();
               }
            }
            else
            {
               moveTowardHero();
            }
         }
         else
         {
            moveTowardHero();
         }
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4" || this.currentLabel == "攻击5";
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(45);
         newAttackID();
      }
      
      override public function realseSkill2() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         setYourDaddysTime(43);
         newAttackID();
      }
      
      override public function realseSkill3() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击5";
         if(this.currentLabel != "攻击5")
         {
            this.gotoAndStop("攻击5");
         }
         setYourDaddysTime(48);
         newAttackID();
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击4";
         this.gotoAndStop("攻击4");
         setYourDaddysTime(15);
         newAttackID();
      }
   }
}

