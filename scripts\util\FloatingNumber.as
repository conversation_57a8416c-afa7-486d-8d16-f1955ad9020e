package util
{
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.utils.clearInterval;
   import flash.utils.setInterval;
   
   public class FloatingNumber extends Sprite
   {
      
      private var _sid:uint;
      
      private var _counter:int = 0;
      
      public function FloatingNumber()
      {
         super();
      }
      
      public function addFloatingNumberAnimation(param1:String, param2:int, param3:Number, param4:Number, param5:Number) : void
      {
         var _loc7_:uint = 0;
         var _loc8_:String = null;
         var _loc9_:DisplayObject = null;
         var _loc6_:int = 0;
         while(_loc6_ < String(param2).length)
         {
            _loc7_ = uint(String(param2).charAt(_loc6_));
            _loc8_ = param1 + _loc7_;
            _loc9_ = GameUtility.getObject(_loc8_) as DisplayObject;
            _loc9_.x = _loc6_ * param5;
            addChild(_loc9_);
            _loc6_++;
         }
         this.x = param3;
         this.y = param4;
      }
      
      public function addFloatingNumberBitmap(param1:String, param2:int, param3:Number, param4:Number, param5:Number) : void
      {
         var _loc7_:uint = 0;
         var _loc8_:Bitmap = null;
         var _loc6_:int = 0;
         while(_loc6_ < String(param2).length)
         {
            _loc7_ = uint(String(param2).charAt(_loc6_));
            _loc8_ = GameUtility.getImageObject(String(param1 + _loc7_)) as Bitmap;
            _loc8_.x = _loc6_ * param5;
            addChild(_loc8_);
            _loc6_++;
         }
         this._sid = setInterval(this.update,40);
         this.x = param3;
         this.y = param4;
      }
      
      public function update() : void
      {
         if(this)
         {
            this.y -= 3;
         }
         ++this._counter;
         if(this._counter++ > 30)
         {
            clearInterval(this._sid);
            if(Boolean(this) && Boolean(this.parent))
            {
               this.parent.removeChild(this);
            }
         }
      }
   }
}

