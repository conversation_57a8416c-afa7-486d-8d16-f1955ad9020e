package actors.menu
{
   import actors.SoundManager;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   import util.GameUtility;
   
   public class GameMenu extends MovieClip
   {
      
      private var _isnew:Boolean;
      
      public var simpleGame:SimpleButton;
      
      public var doubleGame:SimpleButton;
      
      public var successRoom:SimpleButton;
      
      public var gameShop:SimpleButton;
      
      public var gameHelp:SimpleButton;
      
      public var aboutUs:SimpleButton;
      
      public var tk:ThreeKingdoms;
      
      public var initMenu:MovieClip;
      
      public var menuButton:MovieClip;
      
      public var continueGame:SimpleButton;
      
      public var newGame:SimpleButton;
      
      public var backToMenu:SimpleButton;
      
      public var selectHero:SelectHero;
      
      public var mcGameHelp:GameHelp;
      
      public var mcAboutUs:AboutUs;
      
      public var doubleMC:MovieClip;
      
      public var txtVersion:TextField;
      
      public function GameMenu()
      {
         super();
         this.tk = ThreeKingdoms._instance;
         this.tabChildren = false;
         this.addEventListener(Event.ADDED_TO_STAGE,this.added,false,0,true);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.removed,false,0,true);
      }
      
      private function added(param1:Event) : *
      {
         this.menuButton.simpleGame.addEventListener(MouseEvent.CLICK,this.onSimpleGameClickHandler);
         this.menuButton.doubleGame.addEventListener(MouseEvent.CLICK,this.onDoubleGameClickHandler);
         this.menuButton.gameHelp.addEventListener(MouseEvent.CLICK,this.onGameHelpClickHandler);
         this.menuButton.gameShop.addEventListener(MouseEvent.CLICK,this.onGameShopClickHandler);
         this.menuButton.successRoom.addEventListener(MouseEvent.CLICK,this.onSuccessRoomClickHandler);
         this.menuButton.aboutUs.addEventListener(MouseEvent.CLICK,this.onAboutUsClickHandler);
         this.initMenu.newGame.addEventListener(MouseEvent.CLICK,this.onNewGameClickHandler);
         this.initMenu.continueGame.addEventListener(MouseEvent.CLICK,this.onContinueClickHandler);
         this.initMenu.backToMenu.addEventListener(MouseEvent.CLICK,this.onBackToMenuClickHandler);
         this.doubleMC.visible = false;
      }
      
      private function onAboutUsClickHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         this.mcAboutUs = GameUtility.getObject("actors.menu.AboutUs") as AboutUs;
         addChild(this.mcAboutUs);
      }
      
      private function onSuccessRoomClickHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         navigateToURL(new URLRequest("http://my.4399.com/space-mtag-tagid-81166.html"),"_blank");
      }
      
      private function onGameShopClickHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         navigateToURL(new URLRequest("http://my.4399.com/space-364065380-do-thread-id-30080357-tagid-81166.html"),"_blank");
      }
      
      private function onGameHelpClickHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         this.mcGameHelp = GameUtility.getObject("actors.menu.GameHelp") as GameHelp;
         addChild(this.mcGameHelp);
      }
      
      private function onSimpleGameClickHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         this.tk._gameMode = 1;
         this.tk._players = 1;
         this.tk.judgeLog();
         this.tk.isFirstInit = true;
         if(this.tk._isUserLogin)
         {
            this.startMainGame();
         }
      }
      
      public function startMainGame() : void
      {
         this.initMenu.visible = true;
         this.menuButton.visible = false;
         this.tk._currentLevel = 0;
         this.tk._currentMaxLevel = 1;
         this.menuButton.x = 408.25;
         this.menuButton.y = 614.55;
         this.initMenu.x = 400.9;
         this.initMenu.y = 172.55;
      }
      
      private function onDoubleGameClickHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         this.tk._gameMode = 2;
         this.tk._players = 2;
         this.tk.judgeLog();
         if(this.tk._isUserLogin)
         {
            this.startMainGame();
         }
      }
      
      private function onNewGameClickHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         this.selectHero = GameUtility.getObject("actors.menu.SelectHero") as SelectHero;
         addChild(this.selectHero);
      }
      
      private function onContinueClickHandler(param1:MouseEvent) : void
      {
         if(this.tk._isNewUser)
         {
            this.selectHero = GameUtility.getObject("actors.menu.SelectHero") as SelectHero;
            addChild(this.selectHero);
         }
         else
         {
            ThreeKingdoms.serviceHold.getData(true,0);
         }
      }
      
      private function onBackToMenuClickHandler(param1:Event) : void
      {
         SoundManager.play("select");
         this.menuButton.x = 400.9;
         this.menuButton.y = 173.3;
         this.menuButton.visible = true;
         this.initMenu.visible = false;
         this.initMenu.x = 408.25;
         this.initMenu.y = 614.55;
      }
      
      private function removed(param1:Event) : *
      {
         this.menuButton.simpleGame.removeEventListener(MouseEvent.CLICK,this.onSimpleGameClickHandler);
         this.menuButton.doubleGame.removeEventListener(MouseEvent.CLICK,this.onDoubleGameClickHandler);
         this.menuButton.gameHelp.removeEventListener(MouseEvent.CLICK,this.onGameHelpClickHandler);
         this.menuButton.gameShop.addEventListener(MouseEvent.CLICK,this.onGameShopClickHandler);
         this.menuButton.successRoom.addEventListener(MouseEvent.CLICK,this.onSuccessRoomClickHandler);
         this.menuButton.aboutUs.addEventListener(MouseEvent.CLICK,this.onAboutUsClickHandler);
         this.initMenu.newGame.removeEventListener(MouseEvent.CLICK,this.onNewGameClickHandler);
         this.initMenu.continueGame.removeEventListener(MouseEvent.CLICK,this.onContinueClickHandler);
         this.initMenu.backToMenu.removeEventListener(MouseEvent.CLICK,this.onBackToMenuClickHandler);
      }
   }
}

