package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol883")]
   public dynamic class Saving extends MovieClip
   {
      
      public function Saving()
      {
         super();
         addFrameScript(19,this.frame20);
      }
      
      internal function frame20() : *
      {
         if(this.parent)
         {
            MovieClip(this.parent).removeChild(this);
         }
      }
   }
}

