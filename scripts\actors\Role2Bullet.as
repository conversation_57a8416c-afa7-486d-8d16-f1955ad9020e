package actors
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol770")]
   public dynamic class Role2Bullet extends MovieClip
   {
      
      public var sword:MovieClip;
      
      public function Role2Bullet()
      {
         super();
         addFrameScript(13,this.frame14);
      }
      
      internal function frame14() : *
      {
         if(Bullet(this.parent))
         {
            Bullet(this.parent).destroy();
         }
         stop();
      }
   }
}

