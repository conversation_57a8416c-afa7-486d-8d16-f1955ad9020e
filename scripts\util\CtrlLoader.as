package util
{
   import flash.display.Loader;
   import flash.errors.IllegalOperationError;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IOErrorEvent;
   import flash.events.SecurityErrorEvent;
   import flash.net.URLRequest;
   import flash.system.ApplicationDomain;
   import flash.system.LoaderContext;
   
   public class CtrlLoader extends EventDispatcher
   {
      
      public static var CLASS_LOADED:String = "classLoaded";
      
      public static var LOAD_ERROR:String = "loadError";
      
      private var loader:Loader;
      
      private var swfLib:String;
      
      private var request:URLRequest;
      
      private var loadedClass:Class;
      
      public function CtrlLoader()
      {
         super();
         this.loader = new Loader();
         this.loader.contentLoaderInfo.addEventListener(Event.COMPLETE,this.completeHandler);
         this.loader.contentLoaderInfo.addEventListener(IOErrorEvent.IO_ERROR,this.ioErrorHandler);
         this.loader.contentLoaderInfo.addEventListener(SecurityErrorEvent.SECURITY_ERROR,this.securityErrorHandler);
      }
      
      public function load(param1:String) : void
      {
         this.swfLib = param1;
         this.request = new URLRequest(this.swfLib);
         var _loc2_:LoaderContext = new LoaderContext();
         _loc2_.checkPolicyFile = false;
         _loc2_.applicationDomain = ApplicationDomain.currentDomain;
         this.loader.load(this.request,_loc2_);
      }
      
      public function getClass(param1:String) : Class
      {
         var className:String = param1;
         try
         {
            return this.loader.contentLoaderInfo.applicationDomain.getDefinition(className) as Class;
         }
         catch(e:Error)
         {
            throw new IllegalOperationError(className + " definition not found in " + swfLib);
         }
      }
      
      public function get content() : Object
      {
         return this.loader.content;
      }
      
      private function completeHandler(param1:Event) : void
      {
         dispatchEvent(new Event(CtrlLoader.CLASS_LOADED));
      }
      
      private function ioErrorHandler(param1:Event) : void
      {
         dispatchEvent(new Event(CtrlLoader.LOAD_ERROR));
      }
      
      private function securityErrorHandler(param1:Event) : void
      {
         dispatchEvent(new Event(CtrlLoader.LOAD_ERROR));
      }
   }
}

