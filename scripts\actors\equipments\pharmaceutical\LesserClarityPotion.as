package actors.equipments.pharmaceutical
{
   public class LesserClarityPotion extends LesserHealingSalve
   {
      
      public function LesserClarityPotion()
      {
         super();
         this._cureNumber = 100;
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override protected function cureHero() : void
      {
         _owner._properties.setCurrentManaPoint(this._owner._properties.getCurrentManaPoint() + _cureNumber);
         if(<PERSON><PERSON><PERSON>(_owner.pet) && !_owner.pet.isDead())
         {
            _owner.pet._properties.setCurrentManaPoint(this._owner._properties.getCurrentManaPoint() + _cureNumber);
         }
         this.addHealingAnimation(this._cureNumber,"cureManaPoint");
      }
   }
}

