package actors.info
{
   import actors.Hero;
   import actors.battle.Batter;
   import actors.user.User;
   import base.GameWorld;
   import com.greensock.TweenMax;
   import event.GameEvent;
   import flash.display.MovieClip;
   import flash.events.Event;
   import util.GameUtility;
   
   public class GameInformation extends MovieClip
   {
      
      private var _batterTimes:int = 0;
      
      private var _batterSi:int = 0;
      
      private var _world:GameWorld;
      
      private var _roleInfos:Vector.<Object> = new Vector.<Object>();
      
      private var _preBatterTimes:int = 0;
      
      private var _batterPanel:Batter;
      
      public function GameInformation()
      {
         super();
         this._world = ThreeKingdoms._gameWorld;
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         this.init();
      }
      
      public function init() : void
      {
         var _loc1_:RoleInfo = null;
         var _loc2_:RoleInfo = null;
         _loc1_ = new RoleInfo(Hero(ThreeKingdoms._gameWorld._heroes[0]).getPlayer()._roleID);
         _loc1_.x = 30;
         this.addChild(_loc1_);
         this._roleInfos.push(_loc1_);
         if(ThreeKingdoms._gameWorld._heroes.length == 2)
         {
            _loc2_ = new RoleInfo(Hero(ThreeKingdoms._gameWorld._heroes[1]).getPlayer()._roleID);
            this.addChild(_loc2_);
            _loc2_.x = 620;
            this._roleInfos.push(_loc2_);
         }
      }
      
      public function getRoleInfoByPlayer(param1:User) : RoleInfo
      {
         return this._roleInfos[param1.getControlPlayer()] as RoleInfo;
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         this._world._eventManager.removeEventListener(GameEvent.ENEMY_IS_UNDER_ATTACK,this.onEnemyIsUnderAttackHandler);
         this._world._eventManager.removeEventListener(GameEvent.ENEMY_IS_UNDER_ATTACK,this.onAddUniquePointHandler);
         this._world._eventManager.removeEventListener(GameEvent.HERO_IS_UNDER_ATTACK,this.onHeroIsUnderAttackHandler);
      }
      
      private function onAddedToStageHandler(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this._world._eventManager.addEventListener(GameEvent.ENEMY_IS_UNDER_ATTACK,this.onEnemyIsUnderAttackHandler);
         this._world._eventManager.addEventListener(GameEvent.ENEMY_IS_UNDER_ATTACK,this.onAddUniquePointHandler);
         this._world._eventManager.addEventListener(GameEvent.HERO_IS_UNDER_ATTACK,this.onHeroIsUnderAttackHandler);
      }
      
      private function onAddUniquePointHandler(param1:GameEvent) : void
      {
         var _loc2_:uint = this._roleInfos.length;
         while(_loc2_-- > 0)
         {
            RoleInfo(this._roleInfos[_loc2_]).addUnique(param1._data);
         }
      }
      
      private function onHeroIsUnderAttackHandler(param1:GameEvent) : void
      {
      }
      
      private function onEnemyIsUnderAttackHandler(param1:GameEvent) : void
      {
         if(User._batterTimes >= 2)
         {
            if(this._batterPanel == null)
            {
               this._batterPanel = new Batter();
               addChild(this._batterPanel);
               this._batterPanel.x = 700;
               this._batterPanel.y = 200;
            }
            this._batterPanel.alpha = 1;
            TweenMax.to(this._batterPanel,3.5,{
               "alpha":0,
               "onComplete":this.omCompleteHandler
            });
            this._batterPanel.addBatterNumber(User._batterTimes);
         }
      }
      
      private function omCompleteHandler() : void
      {
         if(Boolean(this._batterPanel) && contains(this._batterPanel))
         {
            removeChild(this._batterPanel);
            this._batterPanel = null;
         }
      }
      
      public function update() : void
      {
         ++this._batterSi;
         if(this._batterSi % 20 == 0)
         {
            this.batter();
         }
         var _loc1_:uint = this._roleInfos.length;
         while(_loc1_-- > 0)
         {
            RoleInfo(this._roleInfos[_loc1_]).update();
         }
      }
      
      private function batter() : void
      {
         if(this._preBatterTimes == User._batterTimes)
         {
            User._batterTimes = 0;
            this._preBatterTimes = 0;
         }
         else
         {
            this._preBatterTimes = User._batterTimes;
         }
      }
      
      public function addBossHealthPointBar(param1:String, param2:int) : void
      {
         var _loc3_:MovieClip = null;
         if(!getChildByName("healthPointBar"))
         {
            _loc3_ = GameUtility.getObject("BossHealthPointBar");
            _loc3_.name = "healthPointBar";
            _loc3_.namemc.gotoAndStop(param1);
            _loc3_.x = 420;
            _loc3_.y = 50;
            this.addChild(_loc3_);
         }
         else
         {
            MovieClip(getChildByName("healthPointBar")).gotoAndStop(param2);
         }
      }
      
      public function destroy() : void
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
   }
}

