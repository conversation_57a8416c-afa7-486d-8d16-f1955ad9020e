package actors
{
   import base.GameObject;
   import event.GameEvent;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.geom.Matrix;
   import util.GameUtility;
   import util.HitTest;
   
   public class Bullet extends MovieClip
   {
      
      public var _mcBullet:MovieClip;
      
      public var _bulletName:String;
      
      public var _role:GameObject;
      
      public var _isDisabled:Boolean = false;
      
      public var _direction:int = -1;
      
      public var _vx:int = 0;
      
      public var _maxAttackCount:int;
      
      public var _isCanBeDestroyed:Boolean;
      
      private var _attackInterval:int = 0;
      
      private var _attackIntervalCount:int = 0;
      
      private var _attackId:int = 0;
      
      private var _currentAction:String;
      
      private var enemysArray:Vector.<Object> = new Vector.<Object>();
      
      public function Bullet(param1:String)
      {
         super();
         this._bulletName = param1;
         if(param1 == "actors.Enemy17Bullet")
         {
            this._mcBullet = GameUtility.getLibraryObjectFromSWF("enemy17.swf","actors.Enemy17Bullet");
         }
         else if(param1 == "actors.enemies.Enemy21Bullet")
         {
            this._mcBullet = GameUtility.getLibraryObjectFromSWF("enemy21.swf","actors.enemies.Enemy21Bullet");
         }
         else if(param1 == "actors.Enemy31Bullet")
         {
            this._mcBullet = GameUtility.getLibraryObjectFromSWF("enemy31.swf","actors.Enemy31Bullet");
         }
         else if(param1 == "actors.pets.bullets.Pet2Bullet")
         {
            this._mcBullet = GameUtility.getLibraryObjectFromSWF("pet2.swf","actors.pets.bullets.Pet2Bullet");
         }
         else
         {
            this._mcBullet = GameUtility.getObject(param1);
         }
         this.addChild(this._mcBullet);
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         removeEventListener(Event.ENTER_FRAME,this.update);
      }
      
      private function onAddedToStageHandler(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         addEventListener(Event.ENTER_FRAME,this.update);
      }
      
      public function update(param1:Event = null) : void
      {
         if(this._isDisabled)
         {
            return;
         }
         this.checkAttack();
         if(this._bulletName == "actors.Enemy17Bullet" || this._bulletName == "actors.enemies.Enemy21Bullet" || this._bulletName == "actors.Enemy31Bullet" || this._bulletName == "actors.pets.bullets.Pet2Bullet")
         {
            this.x += this._vx;
            if(GameUtility.getDistance(this,this._role) > 1500)
            {
               if(this.parent)
               {
                  this.parent.removeChild(this);
               }
            }
         }
      }
      
      public function playAnimation() : void
      {
         this._mcBullet.play();
      }
      
      public function stopAnimation() : void
      {
         this._mcBullet.stop();
      }
      
      public function checkAttack() : void
      {
         var _loc2_:GameObject = null;
         var _loc3_:Object = null;
         if(this._role is Hero)
         {
            this.enemysArray = ThreeKingdoms._gameWorld._enemies;
         }
         else if(this._role is Enemy)
         {
            this.enemysArray = ThreeKingdoms._gameWorld._heroes;
         }
         var _loc1_:int = 0;
         while(_loc1_ < this.enemysArray.length)
         {
            _loc2_ = this.enemysArray[_loc1_] as GameObject;
            if(HitTest.complexHitTestObject(this,_loc2_.collipse))
            {
               if(_loc2_._underAttackIDVector.indexOf(this.getAttackId()) == -1)
               {
                  if(_loc2_ is Hero)
                  {
                     _loc3_ = {
                        "role":this._role,
                        "enemy":_loc2_
                     };
                     ThreeKingdoms._gameWorld._eventManager.dispatchEvent(new GameEvent(GameEvent.HERO_IS_UNDER_ATTACK_BY_BULLET,_loc3_));
                  }
                  else if(_loc2_ is Enemy)
                  {
                     _loc3_ = {
                        "role":this._role,
                        "enemy":_loc2_
                     };
                     ThreeKingdoms._gameWorld._eventManager.dispatchEvent(new GameEvent(GameEvent.ENEMY_IS_ATTACK_BY_BULLET,_loc3_));
                  }
                  _loc2_._underAttackIDVector.push(this.getAttackId());
                  --this._maxAttackCount;
                  if(this._attackIntervalCount <= 0)
                  {
                     this.destroy();
                  }
               }
            }
            _loc1_++;
         }
         if(this._attackIntervalCount > 0)
         {
            ++this._attackIntervalCount;
         }
      }
      
      public function setScale(param1:Number, param2:Number) : void
      {
         var _loc3_:Matrix = this.transform.matrix;
         _loc3_.a = _loc3_.a > 0 ? Math.abs(_loc3_.a) * param1 : -Math.abs(_loc3_.a) * param1;
         _loc3_.d *= param2;
         this.transform.matrix = _loc3_;
      }
      
      public function setSource(param1:GameObject) : void
      {
         this._role = param1;
         this._attackId = param1.getOnlyAttackID();
         this._direction = this._role.transform.matrix.a;
         GameUtility.flipHorizontal(this,this._direction);
      }
      
      public function setAction(param1:String) : void
      {
         this._currentAction = param1;
         var _loc2_:Object = this._role._attackBackInfomationDictionary[this._currentAction];
         if(_loc2_)
         {
            this._maxAttackCount = _loc2_.hitMaxCount;
            this._attackInterval = _loc2_.attackInterval;
         }
      }
      
      public function setDisable() : void
      {
         this._isDisabled = true;
      }
      
      public function getAttackId() : String
      {
         return this.name + this._attackId;
      }
      
      public function newAttackId() : void
      {
         ++this._attackId;
      }
      
      public function getBulletsArray() : Array
      {
         var _loc4_:MovieClip = null;
         var _loc1_:Array = [];
         var _loc2_:int = this._mcBullet.numChildren;
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_)
         {
            _loc4_ = this._mcBullet.getChildByName("sword" + _loc3_) as MovieClip;
            if(_loc4_)
            {
               _loc1_.push(_loc4_);
            }
            _loc3_++;
         }
         return _loc1_;
      }
      
      public function destroy() : void
      {
         var _loc1_:int = 0;
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         if(this._role)
         {
            _loc1_ = int(this._role._magicBulletArray.indexOf(this));
            if(_loc1_ != -1)
            {
               this._role._magicBulletArray.splice(_loc1_,1);
            }
         }
         this.removeEventListener(Event.ENTER_FRAME,this.update);
      }
   }
}

