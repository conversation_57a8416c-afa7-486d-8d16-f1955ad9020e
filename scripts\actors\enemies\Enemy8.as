package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy8 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy8(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 400;
         this._walkSpeed = 3;
         this._object._jumpCount = 0;
         this._object._alertRange = 400;
         this._object._attackRange = 150;
         this._currentHealthPoint = 1350;
         this._totalHealthPoint = 1350;
         this._attackProbablity = 40;
         this._resistance = 38;
         this._experience = 97;
         this._probability = 0.3;
         this._goldPrice = 20 + Math.round(Math.random() * 20);
         if(ThreeKingdoms._instance._currentLevel == 3)
         {
            this._currentHealthPoint = 3000;
            this._totalHealthPoint = 3000;
            this._resistance = 32;
            this._probability = 0.65;
            this._experience = 350;
            this._attackProbablity = 35;
            this._goldPrice = 30 + Math.round(Math.random() * 20);
            this._fallEquipmentsList = [{
               "id":74,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":77,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":11,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":14,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":37,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":40,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":2,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":63,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":28,
               "qualityID":[0],
               "type":"equipment"
            }];
         }
         else if(ThreeKingdoms._instance._currentLevel == 4)
         {
            this._fallEquipmentsList = [{
               "id":74,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":77,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":11,
               "qualityID":[0,1],
               "type":"equipment"
            },{
               "id":14,
               "qualityID":[0,1],
               "type":"equipment"
            },{
               "id":37,
               "qualityID":[0,1],
               "type":"equipment"
            },{
               "id":40,
               "qualityID":[0,1],
               "type":"equipment"
            }];
         }
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":60 + Math.random() * 28,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":60 + Math.random() * 28,
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(_skill1CoolDown == 0)
         {
            this.realseSkill1();
            _skill1CoolDown = 120 + Math.round(Math.random() * 180);
         }
         else if(GameUtility.getRandomNumber(15))
         {
            wait();
         }
         else if(GameUtility.getRandomNumber(_attackProbablity))
         {
            steer();
            _vx = 0;
            setYourDaddysTime(32);
            this._lastHit = "攻击1";
            this.gotoAndStop("攻击1");
            newAttackID();
         }
         else
         {
            moveTowardHero();
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(29);
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         newAttackID();
      }
   }
}

