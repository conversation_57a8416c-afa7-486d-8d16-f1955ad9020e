package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol356")]
   public dynamic class GameMsg extends MovieClip
   {
      
      public var msg:MovieClip;
      
      public function GameMsg()
      {
         super();
         addFrameScript(29,this.frame30);
      }
      
      internal function frame30() : *
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
   }
}

