package util
{
   import flash.display.*;
   import flash.events.*;
   import flash.utils.getTimer;
   
   public class Input
   {
      
      public static var ascii:Array;
      
      public static var keyState:Array;
      
      private static var keyArr:Array;
      
      private static var keyBuffer:Array;
      
      private static var bufferSize:int;
      
      public static var m_stageMc:Stage;
      
      public static var doubleCount:int;
      
      public static var doubleCount2:int;
      
      public static var lastKey:int = 0;
      
      public static var lastKey2:int = 0;
      
      public static var timeSinceLastKey:* = 0;
      
      public static var mouseDown:Boolean = false;
      
      public static var mouseReleased:Boolean = false;
      
      public static var mousePressed:Boolean = false;
      
      public static var mouseOver:Boolean = false;
      
      public static var mouseX:Number = 0;
      
      public static var mouseY:Number = 0;
      
      public static var mouseOffsetX:Number = 0;
      
      public static var mouseOffsetY:Number = 0;
      
      public static var mouseDragX:Number = 0;
      
      public static var mouseDragY:Number = 0;
      
      public static var mouse:Sprite = new Sprite();
      
      private var keyId:int = 0;
      
      private var keyId2:int = 0;
      
      private var lastKeyId:int = 0;
      
      private var lastKeyId2:int = 0;
      
      protected var curUpTime:int;
      
      protected var curUpTime2:int;
      
      protected var lastUpTime:int;
      
      protected var lastUpTime2:int;
      
      public function Input(param1:Stage)
      {
         super();
         m_stageMc = param1;
         ascii = new Array(222);
         this.fillAscii();
         keyState = new Array(222);
         keyArr = new Array();
         var _loc2_:int = 0;
         while(_loc2_ < 222)
         {
            keyState[_loc2_] = new int(0);
            if(ascii[_loc2_] != undefined)
            {
               keyArr.push(_loc2_);
            }
            _loc2_++;
         }
         bufferSize = 5;
         keyBuffer = new Array(bufferSize);
         var _loc3_:int = 0;
         while(_loc3_ < bufferSize)
         {
            keyBuffer[_loc3_] = new Array(0,0);
            _loc3_++;
         }
         m_stageMc.addEventListener(Event.ACTIVATE,this._add);
         m_stageMc.addEventListener(Event.DEACTIVATE,this._remvoe);
         mouse.graphics.lineStyle(0.1,0,100);
         mouse.graphics.moveTo(0,0);
         mouse.graphics.lineTo(0,0.1);
      }
      
      public static function update() : *
      {
         var _loc1_:int = 0;
         while(_loc1_ < keyArr.length)
         {
            if(keyState[keyArr[_loc1_]] != 0)
            {
               ++keyState[keyArr[_loc1_]];
            }
            _loc1_++;
         }
         var _loc2_:int = 0;
         while(_loc2_ < bufferSize)
         {
            ++keyBuffer[_loc2_][1];
            _loc2_++;
         }
         mouseReleased = false;
         mousePressed = false;
         mouseOver = false;
      }
      
      public static function getKeyHold(param1:int) : int
      {
         return Math.max(0,keyState[param1]);
      }
      
      public static function isKeyDown(param1:int) : Boolean
      {
         return keyState[param1] > 0;
      }
      
      public static function isKeyPressed(param1:int) : Boolean
      {
         timeSinceLastKey = 0;
         return keyState[param1] == 1;
      }
      
      public static function isKeyReleased(param1:int) : Boolean
      {
         return keyState[param1] == -1;
      }
      
      public static function isKeyInBuffer(param1:int, param2:int, param3:int) : Boolean
      {
         return keyBuffer[param2][0] == param1 && keyBuffer[param2][1] <= param3;
      }
      
      public static function isKeyDouble(param1:int) : Boolean
      {
         return doubleCount == 1;
      }
      
      public static function isKeyDouble2(param1:int) : Boolean
      {
         return doubleCount2 == 1;
      }
      
      public static function getKeyString(param1:uint) : String
      {
         return ascii[param1];
      }
      
      private function _remvoe(param1:Event) : void
      {
         m_stageMc.removeEventListener(KeyboardEvent.KEY_DOWN,this.keyPress);
         m_stageMc.removeEventListener(KeyboardEvent.KEY_UP,this.keyRelease);
         m_stageMc.removeEventListener(MouseEvent.MOUSE_DOWN,this.mousePress);
         m_stageMc.removeEventListener(MouseEvent.CLICK,this.mouseRelease);
         m_stageMc.removeEventListener(MouseEvent.MOUSE_MOVE,this.mouseMove);
         m_stageMc.removeEventListener(Event.MOUSE_LEAVE,this.mouseLeave);
      }
      
      public function _add(param1:*) : void
      {
         m_stageMc.addEventListener(KeyboardEvent.KEY_DOWN,this.keyPress);
         m_stageMc.addEventListener(KeyboardEvent.KEY_UP,this.keyRelease);
         m_stageMc.addEventListener(MouseEvent.MOUSE_DOWN,this.mousePress);
         m_stageMc.addEventListener(MouseEvent.CLICK,this.mouseRelease);
         m_stageMc.addEventListener(MouseEvent.MOUSE_MOVE,this.mouseMove);
         m_stageMc.addEventListener(Event.MOUSE_LEAVE,this.mouseLeave);
      }
      
      public function mousePress(param1:MouseEvent) : *
      {
         mousePressed = true;
         mouseDown = true;
         mouseDragX = 0;
         mouseDragY = 0;
      }
      
      public function mouseRelease(param1:MouseEvent) : *
      {
         mouseDown = false;
         mouseReleased = true;
      }
      
      public function mouseLeave(param1:Event) : *
      {
         mouseReleased = mouseDown;
         mouseDown = false;
      }
      
      public function mouseMove(param1:MouseEvent) : *
      {
         if(mouseDown != param1.buttonDown)
         {
            mouseDown = param1.buttonDown;
            mouseReleased = !param1.buttonDown;
            mousePressed = param1.buttonDown;
            mouseDragX = 0;
            mouseDragY = 0;
         }
         mouseX = param1.stageX - m_stageMc.x;
         mouseY = param1.stageY - m_stageMc.y;
         mouseOffsetX = mouseX - mouse.x;
         mouseOffsetY = mouseY - mouse.y;
         if(mouseDown)
         {
            mouseDragX += mouseOffsetX;
            mouseDragY += mouseOffsetY;
         }
         mouse.x = mouseX;
         mouse.y = mouseY;
      }
      
      public function keyPress(param1:KeyboardEvent) : *
      {
         keyState[param1.keyCode] = Math.max(keyState[param1.keyCode],1);
         if(param1.keyCode == 65 || param1.keyCode == 68)
         {
            if(lastKey == 0)
            {
               lastKey = param1.keyCode;
            }
            if(this.lastKeyId == 0)
            {
               this.lastKeyId = this.keyId;
            }
            if(param1.keyCode != lastKey)
            {
               ++this.keyId;
            }
            if(lastKey == param1.keyCode && this.lastKeyId != this.keyId)
            {
               this.curUpTime = getTimer();
               if(this.curUpTime - this.lastUpTime >= 500)
               {
                  doubleCount = 0;
               }
               else
               {
                  doubleCount = 1;
               }
               this.lastUpTime = this.curUpTime;
            }
            if(param1.keyCode != lastKey)
            {
               doubleCount = 0;
               this.curUpTime = getTimer();
               this.lastUpTime = this.curUpTime;
               keyState[lastKey] = -1;
            }
            lastKey = param1.keyCode;
            this.lastKeyId = this.keyId;
         }
         if(param1.keyCode == 37 || param1.keyCode == 39)
         {
            if(lastKey2 == 0)
            {
               lastKey2 = param1.keyCode;
            }
            if(this.lastKeyId2 == 0)
            {
               this.lastKeyId2 = this.keyId2;
            }
            if(param1.keyCode != lastKey2)
            {
               ++this.keyId2;
            }
            if(lastKey2 == param1.keyCode && this.lastKeyId2 != this.keyId2)
            {
               this.curUpTime2 = getTimer();
               if(this.curUpTime2 - this.lastUpTime2 >= 500)
               {
                  doubleCount2 = 0;
               }
               else
               {
                  doubleCount2 = 1;
               }
               this.lastUpTime2 = this.curUpTime2;
            }
            if(param1.keyCode != lastKey2)
            {
               doubleCount2 = 0;
               this.curUpTime2 = getTimer();
               this.lastUpTime2 = this.curUpTime2;
               keyState[lastKey2] = -1;
            }
            lastKey2 = param1.keyCode;
            this.lastKeyId2 = this.keyId2;
         }
      }
      
      public function keyRelease(param1:KeyboardEvent) : *
      {
         keyState[param1.keyCode] = -1;
         var _loc2_:int = bufferSize - 1;
         while(_loc2_ > 0)
         {
            keyBuffer[_loc2_] = keyBuffer[_loc2_ - 1];
            _loc2_--;
         }
         if(param1.keyCode == 65 || param1.keyCode == 68)
         {
            if(param1.keyCode == lastKey)
            {
               ++this.keyId;
            }
            doubleCount = 0;
         }
         if(param1.keyCode == 37 || param1.keyCode == 39)
         {
            if(param1.keyCode == lastKey2)
            {
               ++this.keyId2;
            }
            doubleCount2 = 0;
         }
         keyBuffer[0] = [param1.keyCode,0];
      }
      
      private function fillAscii() : *
      {
         ascii[65] = "A";
         ascii[66] = "B";
         ascii[67] = "C";
         ascii[68] = "D";
         ascii[69] = "E";
         ascii[70] = "F";
         ascii[71] = "G";
         ascii[72] = "H";
         ascii[73] = "I";
         ascii[74] = "J";
         ascii[75] = "K";
         ascii[76] = "L";
         ascii[77] = "M";
         ascii[78] = "N";
         ascii[79] = "O";
         ascii[80] = "P";
         ascii[81] = "Q";
         ascii[82] = "R";
         ascii[83] = "S";
         ascii[84] = "T";
         ascii[85] = "U";
         ascii[86] = "V";
         ascii[87] = "W";
         ascii[88] = "X";
         ascii[89] = "Y";
         ascii[90] = "Z";
         ascii[48] = "0";
         ascii[49] = "1";
         ascii[50] = "2";
         ascii[51] = "3";
         ascii[52] = "4";
         ascii[53] = "5";
         ascii[54] = "6";
         ascii[55] = "7";
         ascii[56] = "8";
         ascii[57] = "9";
         ascii[32] = "Spacebar";
         ascii[17] = "Ctrl";
         ascii[16] = "Shift";
         ascii[192] = "~";
         ascii[38] = "up";
         ascii[40] = "down";
         ascii[37] = "left";
         ascii[39] = "right";
         ascii[96] = "Numpad 0";
         ascii[97] = "Numpad 1";
         ascii[98] = "Numpad 2";
         ascii[99] = "Numpad 3";
         ascii[100] = "Numpad 4";
         ascii[101] = "Numpad 5";
         ascii[102] = "Numpad 6";
         ascii[103] = "Numpad 7";
         ascii[104] = "Numpad 8";
         ascii[105] = "Numpad 9";
         ascii[111] = "Numpad /";
         ascii[106] = "Numpad *";
         ascii[109] = "Numpad -";
         ascii[107] = "Numpad +";
         ascii[110] = "Numpad .";
         ascii[45] = "Insert";
         ascii[46] = "Delete";
         ascii[33] = "Page Up";
         ascii[34] = "Page Down";
         ascii[35] = "End";
         ascii[36] = "Home";
         ascii[112] = "F1";
         ascii[113] = "F2";
         ascii[114] = "F3";
         ascii[115] = "F4";
         ascii[116] = "F5";
         ascii[117] = "F6";
         ascii[118] = "F7";
         ascii[119] = "F8";
         ascii[188] = ",";
         ascii[190] = ".";
         ascii[186] = ";";
         ascii[222] = "\'";
         ascii[219] = "[";
         ascii[221] = "]";
         ascii[189] = "-";
         ascii[187] = "+";
         ascii[220] = "\\";
         ascii[191] = "/";
         ascii[9] = "TAB";
         ascii[8] = "Backspace";
      }
   }
}

