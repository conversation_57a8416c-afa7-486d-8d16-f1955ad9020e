package actors.skill
{
   import actors.Hero;
   import actors.SoundManager;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SkillControl extends MovieClip
   {
      
      private var _role:Hero;
      
      private var _isActivePanel:Boolean = true;
      
      public var instrmc:MovieClip;
      
      public var btnPassiveSkill:SimpleButton;
      
      public var btnActiveSkill:SimpleButton;
      
      public var txtGold:TextField;
      
      public var mcKeys:MovieClip;
      
      public function SkillControl(param1:Hero = null)
      {
         super();
         this._role = param1;
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      private function onAddedToStageHandler(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.btnActiveSkill.addEventListener(MouseEvent.CLICK,this.onClickActiveSkillHandler);
         this.btnPassiveSkill.addEventListener(MouseEvent.CLICK,this.onClickPassiveSkillHandler);
         this.instrmc.gotoAndStop(this._role._roleName);
         this.txtGold.text = int(this._role._properties.getGold()).toString();
         this.setBtnEnabled();
         this.initPassiveSkill();
         this.initActiveSkill();
      }
      
      public function setGoldText() : void
      {
         this.txtGold.text = int(this._role._properties.getGold()) + "";
         this.txtGold.selectable = false;
      }
      
      private function initActiveSkill() : void
      {
         this.instrmc["mcKeys"].gotoAndStop(this._role.getPlayer()._controlPlayer + 1);
         var _loc1_:uint = this._role.getPlayer()._learnSkill.length;
         while(_loc1_-- > 0)
         {
            if(this._role.getPlayer()._learnSkill[_loc1_] < 3)
            {
               this.instrmc["btnLevelUP" + (_loc1_ + 1)].addEventListener(MouseEvent.CLICK,this.onClickLearnSkillHandler);
            }
            if(this._role.getPlayer()._learnSkill[_loc1_] > 0)
            {
               this.instrmc["skillIcon" + (_loc1_ + 1)].gotoAndStop(3);
            }
         }
         this.initTextFieldForActiveSkills();
      }
      
      private function initPassiveSkill() : void
      {
         var _loc1_:uint = this._role.getPlayer()._passiveSkills.length;
         while(_loc1_-- > 0)
         {
            this.instrmc["btnLevelUP" + (_loc1_ + 1)].addEventListener(MouseEvent.CLICK,this.onClickLearnSkillHandler);
         }
         if(this.instrmc["passiveSkills"])
         {
            this.instrmc["passiveSkills"].gotoAndStop(this._role._roleName);
         }
         this.initTextFieldForPassiveSkills();
      }
      
      private function initTextFieldForActiveSkills() : void
      {
         var _loc1_:int = 1;
         while(_loc1_ <= 5)
         {
            this.instrmc["txtSkillLevel" + _loc1_].text = this._role.getPlayer()._learnSkill[_loc1_ - 1].toString();
            this.instrmc["txtSkillLevel" + _loc1_].selectable = false;
            _loc1_++;
         }
         _loc1_ = 1;
         while(_loc1_ <= 5)
         {
            this.instrmc["txtCurrentEffect" + _loc1_].text = this._role.getPlayer().getSkillInfo(0,_loc1_);
            this.instrmc["txtCurrentEffect" + _loc1_].selectable = false;
            _loc1_++;
         }
         _loc1_ = 1;
         while(_loc1_ <= 5)
         {
            this.instrmc["txtNextLevelEffect" + _loc1_].text = this._role.getPlayer().getNextLevelSkillInfo(0,_loc1_);
            this.instrmc["txtNextLevelEffect" + _loc1_].selectable = false;
            _loc1_++;
         }
         _loc1_ = 1;
         while(_loc1_ <= 5)
         {
            this.instrmc["txtNeedGold" + _loc1_].text = this._role.getPlayer().getgoldSkillInfo(0,_loc1_);
            this.instrmc["txtNeedGold" + _loc1_].selectable = false;
            _loc1_++;
         }
      }
      
      private function initTextFieldForPassiveSkills() : void
      {
         var _loc1_:int = 1;
         while(_loc1_ <= 5)
         {
            this.instrmc["txtSkillLevel" + _loc1_].text = this._role.getPlayer()._passiveSkills[_loc1_ - 1].toString();
            this.instrmc["txtSkillLevel" + _loc1_].selectable = false;
            _loc1_++;
         }
         _loc1_ = 1;
         while(_loc1_ <= 5)
         {
            this.instrmc["txtCurrentEffect" + _loc1_].text = this._role.getPlayer().getSkillInfo(1,_loc1_);
            this.instrmc["txtCurrentEffect" + _loc1_].selectable = false;
            _loc1_++;
         }
         _loc1_ = 1;
         while(_loc1_ <= 5)
         {
            this.instrmc["txtNextLevelEffect" + _loc1_].text = this._role.getPlayer().getNextLevelSkillInfo(1,_loc1_);
            this.instrmc["txtNextLevelEffect" + _loc1_].selectable = false;
            _loc1_++;
         }
         _loc1_ = 1;
         while(_loc1_ <= 5)
         {
            this.instrmc["txtNeedGold" + _loc1_].text = this._role.getPlayer().getgoldSkillInfo(1,_loc1_);
            this.instrmc["txtNeedGold" + _loc1_].selectable = false;
            _loc1_++;
         }
      }
      
      private function onClickLearnSkillHandler(param1:MouseEvent) : void
      {
         var _loc3_:uint = 0;
         var _loc2_:uint = 0;
         if(this._isActivePanel)
         {
            _loc3_ = parseInt(String(param1.currentTarget.name).substr(10,1));
            if(this._role._properties.getGold() >= int(this._role.getPlayer().getgoldSkillInfo(0,_loc3_)))
            {
               _loc2_ = uint(int(this._role.getPlayer().getgoldSkillInfo(0,_loc3_)));
               SoundManager.play("learnSkill");
               if(this._role.getPlayer()._learnSkill[_loc3_ - 1] < 3)
               {
                  this._role.getPlayer()._learnSkill[_loc3_ - 1] = this._role.getPlayer()._learnSkill[_loc3_ - 1] + 1;
               }
               this.initTextFieldForActiveSkills();
               this.instrmc["skillIcon" + _loc3_].gotoAndStop(3);
            }
         }
         else
         {
            _loc3_ = parseInt(String(param1.currentTarget.name).substr(10,1));
            if(this._role._properties.getGold() >= int(this._role.getPlayer().getgoldSkillInfo(1,_loc3_)))
            {
               _loc2_ = uint(int(this._role.getPlayer().getgoldSkillInfo(1,_loc3_)));
               this._role.getPlayer()._passiveSkills[_loc3_ - 1] = this._role.getPlayer()._passiveSkills[_loc3_ - 1] + 1;
               SoundManager.play("learnSkill");
               this._role._properties.addPassive(_loc3_,this._role.getPlayer()._passiveSkills[_loc3_ - 1]);
               this.initTextFieldForPassiveSkills();
            }
         }
         this._role._properties.setGold(-_loc2_);
         this.setGoldText();
         this.setBtnEnabled();
      }
      
      private function setBtnEnabled() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < 5)
         {
            if(this._isActivePanel)
            {
               if(this._role.getPlayer()._learnSkill[_loc1_] >= 3)
               {
                  this.instrmc["btnLevelUP" + (_loc1_ + 1)].enabled = false;
                  this.instrmc["btnLevelUP" + (_loc1_ + 1)].mouseEnabled = false;
               }
               else
               {
                  this.instrmc["btnLevelUP" + (_loc1_ + 1)].enabled = true;
                  this.instrmc["btnLevelUP" + (_loc1_ + 1)].mouseEnabled = true;
               }
            }
            else if(this._role.getPlayer()._passiveSkills[_loc1_] >= 3)
            {
               this.instrmc["btnLevelUP" + (_loc1_ + 1)].enabled = false;
               this.instrmc["btnLevelUP" + (_loc1_ + 1)].mouseEnabled = false;
            }
            else
            {
               this.instrmc["btnLevelUP" + (_loc1_ + 1)].enabled = true;
               this.instrmc["btnLevelUP" + (_loc1_ + 1)].mouseEnabled = true;
            }
            _loc1_++;
         }
      }
      
      private function onClickPassiveSkillHandler(param1:MouseEvent) : void
      {
         this.instrmc.gotoAndStop(4);
         this.initPassiveSkill();
         this._isActivePanel = false;
         this.setBtnEnabled();
      }
      
      private function onClickActiveSkillHandler(param1:MouseEvent) : void
      {
         this.instrmc.gotoAndStop(this._role._roleName);
         this.initActiveSkill();
         this._isActivePanel = true;
         this.setBtnEnabled();
      }
   }
}

