package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy9 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy9(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._attackProbablity = 60;
         this._experience = 755;
         this.isBoss = true;
         this.enemyName = "张角";
         this._currentHealthPoint = 12000;
         this._totalHealthPoint = 12000;
         this._goldPrice = 50 + Math.round(Math.random() * 50);
         this._object._maxPatrolView = 500;
         this._object._alertRange = 600;
         this._object._attackRange = 200;
         this._walkSpeed = 5;
         this._resistance = 42;
         this._probability = 0.3;
         this._fallEquipmentsList = [{
            "id":3,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":29,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":64,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":11,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":12,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":13,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":14,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":74,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":75,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":76,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":77,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":37,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":38,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":39,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":40,
            "qualityID":[2],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":4,
            "attackBackVelocity":[2,0],
            "attackInterval":4,
            "attackPower":100 + Math.random() * 10,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":4,
            "attackBackVelocity":[4,0],
            "attackInterval":4,
            "attackPower":30 + Math.random() * 10,
            "attackType":"magic"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":4,
            "attackBackVelocity":[2,0],
            "attackInterval":4,
            "attackPower":25 + Math.random() * 10,
            "attackType":"magic"
         };
      }
      
      override public function startAttacking() : void
      {
         var _loc1_:Number = NaN;
         if(GameUtility.getRandomNumber(_attackProbablity))
         {
            this._vx = 0;
            _loc1_ = Math.random();
            if(_loc1_ <= 0.5)
            {
               if(_skill1CoolDown == 0)
               {
                  steer();
                  this.realseSkill1();
                  this._skill1CoolDown = 180;
               }
               else
               {
                  steer();
                  this.attack();
               }
            }
            else if(_skill2CoolDown == 0)
            {
               steer();
               this.realseSkill2();
               this._skill2CoolDown = 240;
            }
            else
            {
               steer();
               this.attack();
            }
         }
         else
         {
            moveTowardHero();
         }
      }
      
      override public function attack() : void
      {
         setYourDaddysTime(18);
         this.gotoAndStop("攻击1");
         this._lastHit = "攻击1";
         newAttackID();
      }
      
      override public function realseSkill1() : void
      {
         setYourDaddysTime(38);
         this.gotoAndStop("攻击2");
         this._lastHit = "攻击2";
         newAttackID();
      }
      
      override public function realseSkill2() : void
      {
         setYourDaddysTime(84);
         this.gotoAndStop("攻击3");
         this._lastHit = "攻击3";
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return super.isAttacking();
      }
   }
}

