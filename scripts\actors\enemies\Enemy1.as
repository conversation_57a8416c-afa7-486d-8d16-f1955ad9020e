package actors.enemies
{
   import actors.Enemy;
   
   public class Enemy1 extends Enemy
   {
      
      public function Enemy1(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._walkSpeed = 2.4;
         this._object._alertRange = 200;
         this._object._maxPatrolView = 200;
         this._object._attackRange = 80;
         this._currentHealthPoint = 105;
         this._totalHealthPoint = 105;
         this._resistance = 1;
         this._probability = 0.3;
         this._experience = 8;
         this._attackProbablity = 20;
         this._goldPrice = 3 + Math.round(Math.random() * 5);
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,-2],
            "attackInterval":4,
            "attackPower":16,
            "attackType":"physical"
         };
         this._fallEquipmentsList = [{
            "id":1,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":7,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":8,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":27,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":33,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":34,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":62,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":70,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":71,
            "qualityID":[0],
            "type":"equipment"
         }];
      }
      
      override public function update() : void
      {
         super.update();
      }
   }
}

