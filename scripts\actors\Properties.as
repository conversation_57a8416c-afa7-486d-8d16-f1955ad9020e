package actors
{
   import actors.equipments.Equipment;
   import actors.equipments.Suit;
   import actors.equipments.Suits;
   import actors.pets.Pet;
   import actors.roles.Role1;
   import actors.roles.Role2;
   import actors.roles.Role3;
   import base.GameWorld;
   import com.edgarcai.encrypt.BinaryEncrypt;
   import com.edgarcai.gamelogic.Antiwear;
   import event.GameEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   import flash.text.TextFormat;
   
   public class Properties
   {
      
      private var _lastSetPowerNumber:Number = -100;
      
      private var _lastResistanceNumber:Number = 0;
      
      private var _lastSetResistanceNumber:Number = -100;
      
      private var _lastPowerNumber:Number = 0;
      
      private var _lastSetLevelNumber:Number = -100;
      
      private var _lastLevelNumber:Number = 1;
      
      public var _world:GameWorld;
      
      public var _propertiesObject:Object;
      
      private var _owner:Hero;
      
      private var _counter:int = 0;
      
      private var _suits:Suits;
      
      private var _suitLength:Array = [0,0,0,0,0,0,0,0,0,0,0];
      
      public var _protectData:Antiwear;
      
      private var _binaryEn:BinaryEncrypt;
      
      private var _lastGold:Number = 0;
      
      private var _lastSetGold:Number = -100;
      
      private var _lastSetCP:Number = -100;
      
      private var _lastCP:Number = 0;
      
      private var _lastCM:Number = 0;
      
      private var _lastSetCM:Number = -100;
      
      private var _lastTH:Number = 0;
      
      private var _lastSetTh:Number = -100;
      
      private var _lastSetCrit:Number = -100;
      
      private var _lastCrit:Number = 0;
      
      private var _lastSetMiss:Number = -100;
      
      private var _lastMiss:Number = 0;
      
      private var addWarning:Boolean = false;
      
      public function Properties(param1:Hero)
      {
         super();
         this._world = ThreeKingdoms._gameWorld;
         this._suits = new Suits();
         this._owner = param1;
         this._binaryEn = new BinaryEncrypt();
         this._protectData = new Antiwear(this._binaryEn);
         this.init();
      }
      
      private function init() : void
      {
         this._propertiesObject = {
            "level":1,
            "gold":0,
            "attackPower":0,
            "magicAttackPower":0,
            "currentHealthPoint":0,
            "currentManaPoint":0,
            "healthRegeneration":0,
            "manaRegeneration":0,
            "totalHealthPoint":0,
            "totalManaPoint":0,
            "crit":0,
            "miss":0,
            "currentExperience":0,
            "totalExperience":0,
            "additionalVelocity":0,
            "additionalAttackPower":0,
            "resistance":0,
            "additionalPassiveHealthPoint":0,
            "additionalPassiveManaPoint":0,
            "additionalPassiveCrit":0,
            "additionalPassiveMiss":0,
            "additionalLucky":0,
            "additionalPassiveHealthRegeneration":0,
            "additionalPassiveManaRegeneration":0,
            "moveSpeed":0,
            "skillPoint":0,
            "foreverAttackPower":0,
            "foreverResistance":0,
            "foreverCrit":0,
            "foreverMiss":0,
            "addHealthregain":false,
            "addInvincible":false,
            "revival":false,
            "addCrazy":false,
            "bloodThirsty":0
         };
         this._protectData.level = this._propertiesObject.level;
         this._protectData.gold = this._propertiesObject.gold;
         this._protectData.attackPower = this._propertiesObject.attackPower;
         this._protectData.magicAttackPower = this._propertiesObject.magicAttackPower;
         this._protectData.currentHealthPoint = this._propertiesObject.currentHealthPoint;
         this._protectData.currentManaPoint = this._propertiesObject.currentManaPoint;
         this._protectData.healthRegeneration = this._propertiesObject.healthRegeneration;
         this._protectData.manaRegeneration = this._propertiesObject.manaRegeneration;
         this._protectData.totalHealthPoint = this._propertiesObject.totalHealthPoint;
         this._protectData.totalManaPoint = this._propertiesObject.totalManaPoint;
         this._protectData.crit = this._propertiesObject.crit;
         this._protectData.miss = this._propertiesObject.miss;
         this._protectData.currentExperience = this._propertiesObject.currentExperience;
         this._protectData.totalExperience = this._propertiesObject.totalExperience;
         this._protectData.additionalVelocity = this._propertiesObject.additionalVelocity;
         this._protectData.additionalAttackPower = this._propertiesObject.additionalAttackPower;
         this._protectData.resistance = this._propertiesObject.resistance;
         this._protectData.additionalPassiveHealthPoint = this._propertiesObject.additionalPassiveHealthPoint;
         this._protectData.additionalPassiveManaPoint = this._propertiesObject.additionalPassiveManaPoint;
         this._protectData.additionalPassiveCrit = this._propertiesObject.additionalPassiveCrit;
         this._protectData.additionalPassiveMiss = this._propertiesObject.additionalPassiveMiss;
         this._protectData.additionalLucky = this._propertiesObject.additionalLucky;
         this._protectData.additionalPassiveHealthRegeneration = this._propertiesObject.additionalPassiveHealthRegeneration;
         this._protectData.additionalPassiveManaRegeneration = this._propertiesObject.additionalPassiveManaRegeneration;
         this._protectData.moveSpeed = this._propertiesObject.moveSpeed;
         this._protectData.skillPoint = this._propertiesObject.skillPoint;
         this._protectData.foreverCrit = this._propertiesObject.foreverCrit;
         this._protectData.foreverMiss = this._propertiesObject.foreverMiss;
         this._protectData.foreverResistance = this._propertiesObject.foreverResistance;
         this._protectData.foreverAttackPower = this._propertiesObject.foreverAttackPower;
         this._protectData.bloodThirsty = this._propertiesObject.bloodThirsty;
      }
      
      public function getLevel() : int
      {
         return this._protectData.level;
      }
      
      public function getForeverCrit() : Number
      {
         return this._protectData.foreverCrit;
      }
      
      public function setForeverCirt(param1:Number) : void
      {
         if(param1 >= 0.05)
         {
            param1 = 0.05;
         }
         this.setCrit(this.getCrit() - this.getForeverCrit());
         this._protectData.foreverCrit = param1;
         this.setCrit(this.getCrit() + param1);
         this._owner.getPlayer()._protectedObject.foreverCrit = this._protectData.foreverCrit;
      }
      
      public function getForeverMiss() : Number
      {
         return this._protectData.foreverMiss;
      }
      
      public function setForeverMiss(param1:Number) : void
      {
         if(param1 >= 0.05)
         {
            param1 = 0.05;
         }
         this.setMiss(this.getMiss() - this.getForeverMiss());
         this._protectData.foreverMiss = param1;
         this.setMiss(this.getMiss() + param1);
         this._owner.getPlayer()._protectedObject.foreverMiss = this._protectData.foreverMiss;
      }
      
      public function getForeverResistance() : Number
      {
         return this._protectData.foreverResistance;
      }
      
      public function setForeverResistance(param1:Number) : void
      {
         if(param1 > 50)
         {
            param1 = 50;
         }
         this.setResistance(this.getResistance() - this.getForeverResistance());
         this._protectData.foreverResistance = param1;
         this.setResistance(this.getResistance() + param1);
         this._owner.getPlayer()._protectedObject.foreverResistance = this._protectData.foreverResistance;
      }
      
      public function getForeverAttackPower() : Number
      {
         return this._protectData.foreverAttackPower;
      }
      
      public function setForeverAttackPower(param1:Number) : void
      {
         if(param1 > 88)
         {
            param1 = 88;
         }
         this.setAttackPower(this.getAttackPower() - this.getForeverAttackPower());
         this._protectData.foreverAttackPower = param1;
         this.setAttackPower(this.getAttackPower() + param1);
         this._owner.getPlayer()._protectedObject.foreverAttackPower = this._protectData.foreverAttackPower;
      }
      
      public function setLevel(param1:int) : void
      {
         if(this._owner is Role1 || this._owner is Role2 || this._owner is Role3)
         {
            if(this._protectData.level < 90)
            {
               this._protectData.level += param1;
            }
         }
         else if(this._owner is Pet)
         {
            if(this._protectData.level < 50)
            {
               this._protectData.level += param1;
            }
         }
         this._owner.getPlayer()._protectedObject.currentLevel = this.getLevel();
      }
      
      public function getSuitIDLength() : Array
      {
         return this._suitLength;
      }
      
      public function setOwner(param1:Hero) : void
      {
         this._owner = param1;
      }
      
      public function getGold() : int
      {
         return this._protectData.gold;
      }
      
      public function setGold(param1:Number) : void
      {
         this._protectData.gold += Math.round(param1);
         this._owner.getPlayer()._protectedObject.gold = Math.round(this._protectData.gold);
      }
      
      public function getCurrentHealthPoint() : Number
      {
         return this._protectData.currentHealthPoint;
      }
      
      public function setCurrentHealthPoint(param1:Number) : void
      {
         if(param1 <= 0)
         {
            param1 = 0;
         }
         if(param1 >= this._protectData.totalHealthPoint)
         {
            param1 = Number(this._protectData.totalHealthPoint);
         }
         this._protectData.currentHealthPoint = param1;
      }
      
      public function getCurrentManaPoint() : Number
      {
         return this._protectData.currentManaPoint;
      }
      
      public function setCurrentManaPoint(param1:Number) : void
      {
         if(param1 < 0)
         {
            param1 = 0;
         }
         if(param1 >= this._protectData.totalManaPoint)
         {
            param1 = Number(this._protectData.totalManaPoint);
         }
         this._protectData.currentManaPoint = param1;
      }
      
      public function getTotalHealthPoint() : Number
      {
         return this._protectData.totalHealthPoint;
      }
      
      public function setTotalHealthPoint(param1:Number) : void
      {
         this._protectData.totalHealthPoint = param1;
      }
      
      public function getTotalManaPoint() : Number
      {
         return this._protectData.totalManaPoint;
      }
      
      public function setTotalManaPoint(param1:Number) : void
      {
         this._protectData.totalManaPoint = param1;
      }
      
      public function getHealthRegeneration() : Number
      {
         return this._protectData.healthRegeneration;
      }
      
      public function setHealthRegeneration(param1:Number) : void
      {
         this._protectData.healthRegeneration = param1;
      }
      
      public function getManaRegeneration() : Number
      {
         return this._protectData.manaRegeneration;
      }
      
      public function setManaRegeneration(param1:Number) : void
      {
         this._protectData.manaRegeneration = param1;
      }
      
      public function getCurrentExperience() : Number
      {
         return this._protectData.currentExperience;
      }
      
      public function setCurrentExperience(param1:Number) : void
      {
         this._protectData.currentExperience += param1;
         this._owner.getPlayer()._protectedObject.currentExperience = this.getCurrentExperience();
         this.isLevelUP();
      }
      
      public function setTotalExperience(param1:Number) : void
      {
         this._protectData.totalExperience = param1;
      }
      
      public function getTotalExperience() : Number
      {
         return this._protectData.totalExperience;
      }
      
      private function isLevelUP() : void
      {
         if(this._protectData.currentExperience >= this._protectData.totalExperience)
         {
            if(this._owner is Pet)
            {
            }
            if(this._owner.mcLevelUp)
            {
               this._owner.mcLevelUp.visible = true;
               this._owner.mcLevelUp.sb.play();
            }
            SoundManager.play("levelUp");
            this._protectData.currentExperience = 0;
            this._owner.getPlayer()._protectedObject.currentExperience = this._protectData.currentExperience;
            this.setLevel(1);
            this._owner.levelUP(this.getLevel());
            this._world._eventManager.dispatchEvent(new GameEvent(GameEvent.LEVEL_UP));
         }
      }
      
      public function getAttackPower() : Number
      {
         return this._protectData.attackPower;
      }
      
      public function setAttackPower(param1:Number) : void
      {
         this._protectData.attackPower = param1;
      }
      
      public function getMagicAttackPower() : Number
      {
         return this._protectData.magicAttackPower;
      }
      
      public function setMagicAttackPower(param1:Number) : void
      {
         this._protectData.magicAttackPower = param1;
      }
      
      public function getCrit() : Number
      {
         return this._protectData.crit;
      }
      
      public function setCrit(param1:Number) : void
      {
         this._protectData.crit = param1;
      }
      
      public function getMiss() : Number
      {
         return this._protectData.miss;
      }
      
      public function setMiss(param1:Number) : void
      {
         this._protectData.miss = param1;
      }
      
      public function getAdditionalVelocity() : Number
      {
         return this._protectData.additionalVelocity;
      }
      
      public function setAdditionalVelocity(param1:Number) : void
      {
         this._protectData.additionalVelocity = param1;
      }
      
      public function getAdditionalAttackPower() : Number
      {
         return this._protectData.additionalAttackPower;
      }
      
      public function getTotalAttackPower() : Number
      {
         return this.getAttackPower() + this.getAdditionalAttackPower();
      }
      
      public function getSkillPoint() : Number
      {
         return this._protectData.skillPoint;
      }
      
      public function getLuckyPoint() : Number
      {
         return this._protectData.additionalLucky;
      }
      
      public function setBloodPoint(param1:Number) : void
      {
         this._protectData.bloodThirsty = param1;
      }
      
      public function getBloodPoint() : Number
      {
         return this._protectData.bloodThirsty;
      }
      
      public function setAdditionalAttackPower(param1:Number) : void
      {
         this._protectData.additionalAttackPower = param1;
      }
      
      public function getResistance() : Number
      {
         return this._protectData.resistance;
      }
      
      public function setResistance(param1:Number) : void
      {
         this._protectData.resistance = param1;
      }
      
      public function getMoveSpeed() : int
      {
         return this._protectData.moveSpeed;
      }
      
      public function setMoveSpeed(param1:int) : void
      {
         this._protectData.moveSpeed = param1;
      }
      
      public function setSkillPoint(param1:int) : void
      {
         this._protectData.skillPoint = param1;
      }
      
      public function setLuckyPoint(param1:Number) : void
      {
         this._protectData.additionalLucky = param1;
      }
      
      public function getHurt(param1:uint) : void
      {
         this.setCurrentHealthPoint(this.getCurrentHealthPoint() - (param1 - this.getResistance()));
      }
      
      public function update() : void
      {
         var _loc1_:TextField = null;
         var _loc2_:TextFormat = null;
         if(this._owner.getPlayer()._isBadPlayer && ThreeKingdoms._instance._game && ThreeKingdoms._instance._gameInfo && !this.addWarning)
         {
            _loc1_ = new TextField();
            _loc2_ = new TextFormat();
            _loc1_.text = "检测到您使用非法外挂修改游戏，请文明游戏,如有疑问请查看 “论坛公告”";
            navigateToURL(new URLRequest("http://my.4399.com/space-117022674-do-thread-id-2477391-tagid-81166.html"),"blank");
            _loc2_.size = 20;
            _loc2_.color = 16711680;
            _loc1_.autoSize = TextFieldAutoSize.LEFT;
            _loc1_.setTextFormat(_loc2_);
            _loc1_.x = 100;
            _loc1_.y = 100;
            ThreeKingdoms._instance.addChild(_loc1_);
            this.addWarning = true;
         }
         if(this._protectData.currentHealthPoint <= 0)
         {
            this._world._eventManager.dispatchEvent(new GameEvent(GameEvent.HERO_DEAD));
         }
         else if(this._counter++ >= 30)
         {
            this.setCurrentHealthPoint(this.getCurrentHealthPoint() + this.getHealthRegeneration());
            this.setCurrentManaPoint(this.getCurrentManaPoint() + this.getManaRegeneration());
            this._counter = 0;
         }
      }
      
      public function removeAllPassive() : void
      {
         var _loc2_:uint = 0;
         var _loc1_:uint = this._owner.getPlayer()._passiveSkills.length;
         while(_loc1_-- > 0)
         {
            _loc2_ = uint(this._owner.getPlayer()._passiveSkills[_loc1_]);
            this.removePassive(_loc1_ + 1,_loc2_);
         }
      }
      
      public function removePassive(param1:uint, param2:uint) : void
      {
         this.analyPassive(param1,param2,-1);
      }
      
      public function analyPassive(param1:uint, param2:uint, param3:Number = 1) : void
      {
         var _loc4_:Number = NaN;
         if(param3 == -1)
         {
            _loc4_ = -this._owner.getPlayer().getPassiveSkillPoint(param1,param2);
         }
         else
         {
            _loc4_ = this._owner.getPlayer().getPassiveSkillPoint(param1,param2);
         }
         if(param2 != 0)
         {
            switch(param1)
            {
               case 1:
                  this._protectData.additionalPassiveHealthPoint = _loc4_;
                  this.setTotalHealthPoint(this.getTotalHealthPoint() + this._protectData.additionalPassiveHealthPoint);
                  break;
               case 2:
                  this._protectData.additionalPassiveManaPoint = _loc4_;
                  this.setTotalManaPoint(this.getTotalManaPoint() + this._protectData.additionalPassiveManaPoint);
                  break;
               case 3:
                  this._protectData.additionalResistance = _loc4_;
                  this.setResistance(this.getResistance() + this._protectData.additionalResistance);
                  break;
               case 4:
                  this._protectData.additionalPassiveHealthRegeneration = _loc4_;
                  this._protectData.additionalPassiveManaRegeneration = _loc4_;
                  this.setHealthRegeneration(this.getHealthRegeneration() + this._protectData.additionalPassiveHealthRegeneration);
                  this.setManaRegeneration(this.getManaRegeneration() + this._protectData.additionalPassiveManaRegeneration);
                  break;
               case 5:
                  if(this._owner is Role1)
                  {
                     this._protectData.additionalPassiveMiss = _loc4_ / 100;
                     this.setMiss(this.getMiss() + this._protectData.additionalPassiveMiss);
                  }
                  else if(this._owner is Role2)
                  {
                     this._protectData.additionalPassiveCrit = _loc4_ / 100;
                     this.setCrit(this.getCrit() + this._protectData.additionalPassiveCrit);
                  }
                  else
                  {
                     this._protectData.additionalAttackPower = _loc4_;
                     this.setAttackPower(this.getAttackPower() + this._protectData.additionalAttackPower);
                  }
            }
         }
      }
      
      public function addEquipment(param1:Equipment) : void
      {
         if(param1._type == "armor")
         {
            if(param1._suitProperty.startLevel != undefined)
            {
               if(param1._category == "helmet" || param1._category == "shoes")
               {
                  this.setResistance(this.getResistance() + param1._suitProperty.startLevel * 1);
               }
               else if(param1._category == "trousers")
               {
                  this.setResistance(this.getResistance() + param1._suitProperty.startLevel * 1);
               }
               else
               {
                  this.setResistance(this.getResistance() + param1._suitProperty.startLevel * 2);
               }
            }
            this.setResistance(this.getResistance() + int(param1.getEquipmentBasePropertyByIndex(param1.index)));
         }
         else if(param1._type == "weapon")
         {
            if(param1._suitProperty.startLevel != undefined)
            {
               this.setAttackPower(this.getAttackPower() + param1._suitProperty.startLevel * 10);
            }
            this.setAttackPower(this.getAttackPower() + int(param1.getEquipmentBasePropertyByIndex(param1.index)));
         }
         var _loc2_:Object = param1.getAdditionalPropertyByID(param1._id) as Object;
         if(_loc2_)
         {
            if(_loc2_.additionalHealthPoint != undefined)
            {
               this.setTotalHealthPoint(this.getTotalHealthPoint() + _loc2_.additionalHealthPoint);
            }
            if(_loc2_.additionalManaPoint != undefined)
            {
               this.setTotalManaPoint(this.getTotalManaPoint() + _loc2_.additionalManaPoint);
            }
            if(_loc2_.additionalAttackPower != undefined)
            {
               this.setAttackPower(this.getAttackPower() + _loc2_.additionalAttackPower);
            }
            if(_loc2_.additionalResistance != undefined)
            {
               this.setResistance(this.getResistance() + _loc2_.additionalResistance);
            }
            if(_loc2_.additionalCrit != undefined)
            {
               this.setCrit(this.getCrit() + _loc2_.additionalCrit);
            }
            if(_loc2_.additionalMiss != undefined)
            {
               this.setMiss(this.getMiss() + _loc2_.additionalMiss);
            }
            if(_loc2_.additionalHealthPointRegeneration != undefined)
            {
               this.setHealthRegeneration(this.getHealthRegeneration() + _loc2_.additionalHealthPointRegeneration);
            }
            if(_loc2_.additionalManaPointRegeneration != undefined)
            {
               this.setManaRegeneration(this.getManaRegeneration() + _loc2_.additionalManaPointRegeneration);
            }
            if(_loc2_.additionalMoveSpeed != undefined)
            {
               this.setMoveSpeed(this.getMoveSpeed() + _loc2_.additionalMoveSpeed);
            }
            if(_loc2_.addHealthSkillPoint != undefined)
            {
               this.setSkillPoint(this.getSkillPoint() + _loc2_.addHealthSkillPoint);
            }
            if(_loc2_.bloodThirsty != undefined)
            {
               this.setBloodPoint(this.getBloodPoint() + _loc2_.bloodThirsty);
            }
            if(_loc2_.addLuckyPoint != undefined)
            {
               this.setLuckyPoint(this.getLuckyPoint() + _loc2_.addLuckyPoint);
            }
         }
      }
      
      public function removeEquipment(param1:Equipment) : void
      {
         if(param1._type == "armor")
         {
            if(param1._suitProperty.startLevel != undefined)
            {
               if(param1._category == "helmet" || param1._category == "shoes")
               {
                  this.setResistance(this.getResistance() - param1._suitProperty.startLevel * 1);
               }
               else if(param1._category == "trousers")
               {
                  this.setResistance(this.getResistance() - param1._suitProperty.startLevel * 1);
               }
               else
               {
                  this.setResistance(this.getResistance() - param1._suitProperty.startLevel * 2);
               }
            }
            this.setResistance(this.getResistance() - int(param1.getEquipmentBasePropertyByIndex(param1.index)));
         }
         else if(param1._type == "weapon")
         {
            if(param1._suitProperty.startLevel != undefined)
            {
               this.setAttackPower(this.getAttackPower() - param1._suitProperty.startLevel * 10);
            }
            this.setAttackPower(this.getAttackPower() - int(param1.getEquipmentBasePropertyByIndex(param1.index)));
         }
         var _loc2_:Object = param1.getAdditionalPropertyByID(param1._id);
         if(_loc2_)
         {
            if(_loc2_.additionalHealthPoint != undefined)
            {
               this.setTotalHealthPoint(this.getTotalHealthPoint() - _loc2_.additionalHealthPoint);
               if(this.getTotalHealthPoint() < 0)
               {
                  this.setTotalHealthPoint(1);
               }
            }
            if(_loc2_.additionalManaPoint != undefined)
            {
               this.setTotalManaPoint(this.getTotalManaPoint() - _loc2_.additionalManaPoint);
               if(this.getTotalManaPoint() < 0)
               {
                  this.setTotalManaPoint(1);
               }
            }
            if(_loc2_.additionalAttackPower != undefined)
            {
               this.setAttackPower(this.getAttackPower() - _loc2_.additionalAttackPower);
            }
            if(_loc2_.additionalResistance != undefined)
            {
               this.setResistance(this.getResistance() - _loc2_.additionalResistance);
            }
            if(_loc2_.additionalCrit != undefined)
            {
               this.setCrit(this.getCrit() - _loc2_.additionalCrit);
            }
            if(_loc2_.additionalMiss != undefined)
            {
               this.setMiss(this.getMiss() - _loc2_.additionalMiss);
            }
            if(_loc2_.additionalHealthPointRegeneration != undefined)
            {
               this.setHealthRegeneration(this.getHealthRegeneration() - _loc2_.additionalHealthPointRegeneration);
            }
            if(_loc2_.additionalManaPointRegeneration != undefined)
            {
               this.setManaRegeneration(this.getManaRegeneration() - _loc2_.additionalManaPointRegeneration);
            }
            if(_loc2_.additionalMoveSpeed != undefined)
            {
               this.setMoveSpeed(this.getMoveSpeed() - _loc2_.additionalMoveSpeed);
            }
            if(_loc2_.addHealthSkillPoint != undefined)
            {
               this.setSkillPoint(this.getSkillPoint() - _loc2_.addHealthSkillPoint);
            }
            if(_loc2_.bloodThirsty != undefined)
            {
               this.setBloodPoint(this.getBloodPoint() - _loc2_.bloodThirsty);
            }
            if(_loc2_.addLuckyPoint != undefined)
            {
               this.setLuckyPoint(this.getLuckyPoint() - _loc2_.addLuckyPoint);
            }
         }
      }
      
      public function addAllEquipments() : void
      {
         var _loc2_:Equipment = null;
         var _loc3_:Suit = null;
         var _loc1_:uint = this._owner.getPlayer()._currentEquipments.length;
         while(_loc1_-- > 0)
         {
            _loc2_ = Equipment(this._owner.getPlayer()._currentEquipments[_loc1_]);
            this.addEquipment(_loc2_);
            if(_loc2_._type == "armor" && (_loc2_.getEquipmentQualityByIndex(_loc2_.index) == "仙器" || _loc2_.getEquipmentQualityByIndex(_loc2_.index) == "神器") || _loc2_._type != "armor" && _loc2_._type != "weapon" && _loc2_._type != "suit" && _loc2_._type != "special" && _loc2_._type != "lantern" && _loc2_._type != "valentine")
            {
               _loc3_ = this._suits.getSuitByID(_loc2_._id) as Suit;
               this._suitLength[_loc3_._suitID - 1] += 1;
            }
         }
         var _loc4_:uint = this._suitLength.length;
         while(_loc4_-- > 0)
         {
            if(this._suitLength[_loc4_] >= 2)
            {
               this.addSuitProperty(_loc4_ + 1,this._suitLength[_loc4_]);
            }
         }
         this.setCurrentHealthPoint(this.getTotalHealthPoint());
         this.setCurrentManaPoint(this.getTotalManaPoint());
      }
      
      private function addSuitProperty(param1:int, param2:int) : void
      {
         var _loc3_:Suit = this._suits.getSuitID(param1) as Suit;
         if(_loc3_)
         {
            if(param2 == 2)
            {
               if(_loc3_._suitProperty[0].additionalHealthPoint != undefined)
               {
                  this.setTotalHealthPoint(this.getTotalHealthPoint() + _loc3_._suitProperty[0].additionalHealthPoint);
               }
               if(_loc3_._suitProperty[0].additionalManaPoint != undefined)
               {
                  this.setTotalManaPoint(this.getTotalManaPoint() + _loc3_._suitProperty[0].additionalManaPoint);
               }
            }
            else if(param2 == 3)
            {
               if(_loc3_._suitProperty[0].additionalHealthPoint != undefined)
               {
                  this.setTotalHealthPoint(this.getTotalHealthPoint() + _loc3_._suitProperty[0].additionalHealthPoint);
               }
               if(_loc3_._suitProperty[0].additionalManaPoint != undefined)
               {
                  this.setTotalManaPoint(this.getTotalManaPoint() + _loc3_._suitProperty[0].additionalManaPoint);
               }
               if(_loc3_._suitProperty[1].additionalManaPoint != undefined)
               {
                  this.setTotalManaPoint(this.getTotalManaPoint() + _loc3_._suitProperty[1].additionalManaPoint);
               }
               if(_loc3_._suitProperty[1].additionalResistance != undefined)
               {
                  this.setResistance(this.getResistance() + _loc3_._suitProperty[1].additionalResistance);
               }
               if(_loc3_._suitProperty[1].addHealthregain != undefined)
               {
                  if(this._propertiesObject.addHealthregain == false)
                  {
                     this._propertiesObject.addHealthregain = true;
                  }
               }
               if(_loc3_._suitProperty[1].addInvincible != undefined)
               {
                  if(this._propertiesObject.addInvincible == false)
                  {
                     this._propertiesObject.addInvincible = true;
                  }
               }
            }
            else if(param2 == 4)
            {
               if(_loc3_._suitProperty[0].additionalHealthPoint != undefined)
               {
                  this.setTotalHealthPoint(this.getTotalHealthPoint() + _loc3_._suitProperty[0].additionalHealthPoint);
               }
               if(_loc3_._suitProperty[0].additionalManaPoint != undefined)
               {
                  this.setTotalManaPoint(this.getTotalManaPoint() + _loc3_._suitProperty[0].additionalManaPoint);
               }
               if(_loc3_._suitProperty[1].additionalManaPoint != undefined)
               {
                  this.setTotalManaPoint(this.getTotalManaPoint() + _loc3_._suitProperty[1].additionalManaPoint);
               }
               if(_loc3_._suitProperty[1].additionalResistance != undefined)
               {
                  this.setResistance(this.getResistance() + _loc3_._suitProperty[1].additionalResistance);
               }
               if(_loc3_._suitProperty[2].additionalResistance != undefined)
               {
                  this.setResistance(this.getResistance() + _loc3_._suitProperty[2].additionalResistance);
               }
               if(_loc3_._suitProperty[2].addHealthSkillPoint != undefined)
               {
                  this.setSkillPoint(this.getSkillPoint() + _loc3_._suitProperty[2].addHealthSkillPoint);
               }
               if(_loc3_._suitProperty[2].bloodThirsty != undefined)
               {
                  this.setBloodPoint(this.getBloodPoint() + _loc3_._suitProperty[2].bloodThirsty);
               }
               if(_loc3_._suitProperty[2].additionalMiss != undefined)
               {
                  this.setMiss(this.getMiss() + _loc3_._suitProperty[2].additionalMiss);
               }
               if(_loc3_._suitProperty[2].additionalCrit != undefined)
               {
                  this.setCrit(this.getCrit() + _loc3_._suitProperty[2].additionalCrit);
               }
               if(_loc3_._suitProperty[2].addCrazy != undefined)
               {
                  if(this._propertiesObject.addCrazy == false)
                  {
                     this._propertiesObject.addCrazy = true;
                  }
               }
               if(_loc3_._suitProperty[2].revival != undefined)
               {
                  if(this._propertiesObject.revival == false)
                  {
                     this._propertiesObject.revival = true;
                  }
               }
            }
         }
      }
      
      private function removeSuitProperty(param1:int, param2:int) : void
      {
         var _loc3_:Suit = this._suits.getSuitID(param1) as Suit;
         if(_loc3_)
         {
            if(param2 == 2)
            {
               if(_loc3_._suitProperty[0].additionalHealthPoint != undefined)
               {
                  this.setTotalHealthPoint(this.getTotalHealthPoint() - _loc3_._suitProperty[0].additionalHealthPoint);
               }
               if(_loc3_._suitProperty[0].additionalManaPoint != undefined)
               {
                  this.setTotalManaPoint(this.getTotalManaPoint() - _loc3_._suitProperty[0].additionalManaPoint);
               }
            }
            else if(param2 == 3)
            {
               if(_loc3_._suitProperty[0].additionalHealthPoint != undefined)
               {
                  this.setTotalHealthPoint(this.getTotalHealthPoint() - _loc3_._suitProperty[0].additionalHealthPoint);
               }
               if(_loc3_._suitProperty[0].additionalManaPoint != undefined)
               {
                  this.setTotalManaPoint(this.getTotalManaPoint() - _loc3_._suitProperty[0].additionalManaPoint);
               }
               if(_loc3_._suitProperty[1].additionalManaPoint != undefined)
               {
                  this.setTotalManaPoint(this.getTotalManaPoint() - _loc3_._suitProperty[1].additionalManaPoint);
               }
               if(_loc3_._suitProperty[1].additionalResistance != undefined)
               {
                  this.setResistance(this.getResistance() - _loc3_._suitProperty[1].additionalResistance);
               }
               if(_loc3_._suitProperty[1].addHealthregain != undefined)
               {
                  if(this._propertiesObject.addHealthregain == true)
                  {
                     this._propertiesObject.addHealthregain = false;
                  }
               }
               if(_loc3_._suitProperty[1].addInvincible != undefined)
               {
                  if(this._propertiesObject.addInvincible == true)
                  {
                     this._propertiesObject.addInvincible = false;
                  }
               }
            }
            else if(param2 == 4)
            {
               if(_loc3_._suitProperty[0].additionalHealthPoint != undefined)
               {
                  this.setTotalHealthPoint(this.getTotalHealthPoint() - _loc3_._suitProperty[0].additionalHealthPoint);
               }
               if(_loc3_._suitProperty[0].additionalManaPoint != undefined)
               {
                  this.setTotalManaPoint(this.getTotalManaPoint() - _loc3_._suitProperty[0].additionalManaPoint);
               }
               if(_loc3_._suitProperty[1].additionalManaPoint != undefined)
               {
                  this.setTotalManaPoint(this.getTotalManaPoint() - _loc3_._suitProperty[1].additionalManaPoint);
               }
               if(_loc3_._suitProperty[1].additionalResistance != undefined)
               {
                  this.setResistance(this.getResistance() - _loc3_._suitProperty[1].additionalResistance);
               }
               if(_loc3_._suitProperty[2].additionalResistance != undefined)
               {
                  this.setResistance(this.getResistance() - _loc3_._suitProperty[2].additionalResistance);
               }
               if(_loc3_._suitProperty[2].bloodThirsty != undefined)
               {
                  this.setBloodPoint(this.getBloodPoint() - _loc3_._suitProperty[2].bloodThirsty);
               }
               if(_loc3_._suitProperty[2].addHealthSkillPoint != undefined)
               {
                  this.setSkillPoint(this.getSkillPoint() - _loc3_._suitProperty[2].addHealthSkillPoint);
               }
               if(_loc3_._suitProperty[2].additionalMiss != undefined)
               {
                  this.setMiss(this.getMiss() - _loc3_._suitProperty[2].additionalMiss);
               }
               if(_loc3_._suitProperty[2].additionalCrit != undefined)
               {
                  this.setCrit(this.getCrit() - _loc3_._suitProperty[2].additionalCrit);
               }
               if(_loc3_._suitProperty[2].addCrazy != undefined)
               {
                  if(this._propertiesObject.addCrazy == true)
                  {
                     this._propertiesObject.addCrazy = false;
                  }
               }
               if(_loc3_._suitProperty[2].revival != undefined)
               {
                  if(this._propertiesObject.revival == true)
                  {
                     this._propertiesObject.revival = false;
                  }
               }
            }
         }
      }
      
      public function jugeSuit() : void
      {
         var _loc3_:Equipment = null;
         var _loc4_:Suit = null;
         var _loc1_:uint = this._suitLength.length;
         while(_loc1_-- > 0)
         {
            if(this._suitLength[_loc1_] >= 2)
            {
               this.removeSuitProperty(_loc1_ + 1,this._suitLength[_loc1_]);
            }
         }
         this._suitLength = [0,0,0,0,0,0,0,0,0,0,0];
         var _loc2_:uint = this._owner.getPlayer()._currentEquipments.length;
         while(_loc2_-- > 0)
         {
            _loc3_ = Equipment(this._owner.getPlayer()._currentEquipments[_loc2_]);
            if(_loc3_._type == "armor" && (_loc3_.getEquipmentQualityByIndex(_loc3_.index) == "仙器" || _loc3_.getEquipmentQualityByIndex(_loc3_.index) == "神器") || _loc3_._type != "armor" && _loc3_._type != "weapon" && _loc3_._type != "suit" && _loc3_._type != "special" && _loc3_._type != "lantern" && _loc3_._type != "valentine")
            {
               _loc4_ = this._suits.getSuitByID(_loc3_._id) as Suit;
               this._suitLength[_loc4_._suitID - 1] += 1;
            }
         }
         var _loc5_:uint = this._suitLength.length;
         while(_loc5_-- > 0)
         {
            if(this._suitLength[_loc5_] >= 2)
            {
               this.addSuitProperty(_loc5_ + 1,this._suitLength[_loc5_]);
            }
         }
      }
      
      public function removeAllEquipments() : void
      {
         var _loc2_:Equipment = null;
         var _loc1_:uint = this._owner.getPlayer()._currentEquipments.length;
         while(_loc1_-- > 0)
         {
            _loc2_ = Equipment(this._owner.getPlayer()._currentEquipments[_loc1_]);
            this.removeEquipment(_loc2_);
         }
         var _loc3_:uint = this._suitLength.length;
         while(_loc3_-- > 0)
         {
            if(this._suitLength[_loc3_] >= 2)
            {
               this.removeSuitProperty(_loc3_ + 1,this._suitLength[_loc3_]);
            }
         }
         this._suitLength = [0,0,0,0,0,0,0,0,0,0,0];
      }
      
      public function addPassive(param1:uint, param2:uint, param3:Boolean = true) : void
      {
         if(param3)
         {
            this.removePassive(param1,param2 - 1);
         }
         this.analyPassive(param1,param2);
      }
      
      private function addAllPassive() : void
      {
         var _loc2_:uint = 0;
         var _loc1_:uint = this._owner.getPlayer()._passiveSkills.length;
         while(_loc1_-- > 0)
         {
            _loc2_ = uint(this._owner.getPlayer()._passiveSkills[_loc1_]);
            this.addPassive(_loc1_ + 1,_loc2_,false);
         }
      }
      
      public function initAll() : void
      {
         this.addAllEquipments();
         this.addAllPassive();
         this.addForever();
      }
      
      private function addForever() : void
      {
         this.setAttackPower(this.getAttackPower() + this.getForeverAttackPower());
         this.setResistance(this.getResistance() + this.getForeverResistance());
         this.setCrit(this.getCrit() + this.getForeverCrit());
         this.setMiss(this.getMiss() + this.getForeverMiss());
      }
      
      private function removeForever() : void
      {
         this.setAttackPower(this.getAttackPower() - this.getForeverAttackPower());
         this.setResistance(this.getResistance() - this.getForeverResistance());
         this.setCrit(this.getCrit() - this.getForeverCrit());
         this.setMiss(this.getMiss() - this.getForeverMiss());
      }
      
      public function destroy() : void
      {
         this.removeAllEquipments();
         this.removeAllPassive();
         this.removeForever();
      }
   }
}

