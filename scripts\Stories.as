package
{
   import adobe.utils.*;
   import flash.accessibility.*;
   import flash.desktop.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.globalization.*;
   import flash.media.*;
   import flash.net.*;
   import flash.net.drm.*;
   import flash.printing.*;
   import flash.profiler.*;
   import flash.sampler.*;
   import flash.sensors.*;
   import flash.system.*;
   import flash.text.*;
   import flash.text.engine.*;
   import flash.text.ime.*;
   import flash.ui.*;
   import flash.utils.*;
   import flash.xml.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol862")]
   public dynamic class Stories extends MovieClip
   {
      
      public var mcTitle:MovieClip;
      
      public var mcRole:MovieClip;
      
      public function Stories()
      {
         super();
         addFrameScript(0,this.frame1,54,this.frame55);
      }
      
      internal function frame1() : *
      {
         this.mcTitle.gotoAndStop(ThreeKingdoms._instance._currentLevel);
         if(ThreeKingdoms._instance._doubleGet)
         {
            this.mcRole.mcDouble.visible = true;
         }
         else
         {
            this.mcRole.mcDouble.visible = false;
         }
         if(ThreeKingdoms._instance._gameMode == 1)
         {
            if(ThreeKingdoms._instance._role1)
            {
               this.mcRole.gotoAndStop(1);
            }
            else if(ThreeKingdoms._instance._role2)
            {
               this.mcRole.gotoAndStop(2);
            }
            else if(ThreeKingdoms._instance._role3)
            {
               this.mcRole.gotoAndStop(3);
            }
         }
         else if(ThreeKingdoms._instance._gameMode == 2)
         {
            if(Boolean(ThreeKingdoms._instance._role1) && Boolean(ThreeKingdoms._instance._role2))
            {
               this.mcRole.gotoAndStop(4);
            }
            else if(Boolean(ThreeKingdoms._instance._role1) && Boolean(ThreeKingdoms._instance._role3))
            {
               this.mcRole.gotoAndStop(5);
            }
            else if(Boolean(ThreeKingdoms._instance._role2) && Boolean(ThreeKingdoms._instance._role3))
            {
               this.mcRole.gotoAndStop(6);
            }
         }
      }
      
      internal function frame55() : *
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         stop();
      }
   }
}

