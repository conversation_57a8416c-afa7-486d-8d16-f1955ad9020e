package actors.equipments
{
   public class Suits
   {
      
      private var _suit1:Suit;
      
      private var _suit2:Suit;
      
      private var _suit3:Suit;
      
      private var _suit4:Suit;
      
      private var _suit5:Suit;
      
      private var _suit6:Suit;
      
      private var _suit8:Suit;
      
      private var _suit9:Suit;
      
      private var _suit10:Suit;
      
      private var _suitID:int = 1;
      
      private var _suit7:Suit;
      
      private var _suit11:Suit;
      
      public function Suits()
      {
         super();
         this.init();
      }
      
      public function init() : void
      {
         this._suit1 = new Suit(1,"银鹰",4,[{"additionalHealthPoint":100},{"additionalManaPoint":60},{
            "additionalResistance":5,
            "addHealthSkillPoint":50
         }]);
         this._suit2 = new Suit(2,"白炎",4,[{"additionalHealthPoint":180},{"additionalManaPoint":120},{
            "additionalResistance":15,
            "addHealthSkillPoint":80
         }]);
         this._suit3 = new Suit(3,"崩龙",4,[{
            "additionalHealthPoint":220,
            "additionalManaPoint":180
         },{"additionalResistance":25},{
            "additionalMiss":0.02,
            "additionalCrit":0.02,
            "addCrazy":true
         }]);
         this._suit4 = new Suit(4,"猛士",4,[{"additionalHealthPoint":120},{"additionalManaPoint":80},{
            "additionalResistance":5,
            "additionalCrit":0.02
         }]);
         this._suit5 = new Suit(5,"红纹",4,[{"additionalHealthPoint":180},{"additionalManaPoint":120},{
            "additionalResistance":10,
            "additionalCrit":0.04
         }]);
         this._suit6 = new Suit(6,"凤凰",4,[{
            "additionalHealthPoint":220,
            "additionalManaPoint":150
         },{"additionalResistance":25},{
            "additionalMiss":0.02,
            "additionalCrit":0.03,
            "revival":true
         }]);
         this._suit8 = new Suit(8,"蛮牛",4,[{"additionalHealthPoint":150},{"additionalManaPoint":50},{
            "additionalResistance":5,
            "bloodThirsty":0.05
         }]);
         this._suit9 = new Suit(9,"战魂",4,[{"additionalHealthPoint":220},{"additionalManaPoint":80},{
            "additionalResistance":12,
            "bloodThirsty":0.08
         }]);
         this._suit10 = new Suit(10,"圣虎",4,[{
            "additionalHealthPoint":300,
            "additionalManaPoint":120
         },{"additionalResistance":25},{
            "additionalMiss":0.03,
            "additionalCrit":0.02,
            "bloodThirsty":0.12
         }]);
         this._suit7 = new Suit(7,"白虎护信",3,[{},{"addHealthregain":true},{}]);
         this._suit11 = new Suit(11,"玄武护信",3,[{},{"addInvincible":true},{}]);
      }
      
      public function getSuitByID(param1:int) : Suit
      {
         switch(param1)
         {
            case 15:
            case 16:
            case 17:
            case 18:
               this._suitID = 1;
               break;
            case 19:
            case 20:
            case 21:
            case 22:
               this._suitID = 2;
               break;
            case 23:
            case 24:
            case 25:
            case 26:
               this._suitID = 3;
               break;
            case 41:
            case 42:
            case 43:
            case 44:
               this._suitID = 4;
               break;
            case 45:
            case 46:
            case 47:
            case 48:
               this._suitID = 5;
               break;
            case 49:
            case 50:
            case 51:
            case 52:
               this._suitID = 6;
               break;
            case 78:
            case 79:
            case 80:
            case 81:
               this._suitID = 8;
               break;
            case 82:
            case 83:
            case 84:
            case 85:
               this._suitID = 9;
               break;
            case 86:
            case 87:
            case 88:
            case 89:
               this._suitID = 10;
               break;
            case 53:
            case 54:
            case 55:
               this._suitID = 7;
               break;
            case 100:
            case 101:
            case 102:
               this._suitID = 11;
               break;
            default:
               this._suitID = 0;
         }
         return this["_suit" + this._suitID] as Suit;
      }
      
      public function getSuitID(param1:int) : Suit
      {
         return this["_suit" + param1] as Suit;
      }
   }
}

