package actors.enemies
{
   import actors.Enemy;
   import util.UString;
   
   public class Enemy13 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy13(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 400;
         this._object._alertRange = 400;
         this._object._attackRange = 125;
         this._currentHealthPoint = 11000;
         this._totalHealthPoint = 11000;
         this._attackProbablity = 62;
         this._resistance = 30;
         this._experience = 800;
         this._goldPrice = 100 + Math.round(Math.random() * 50);
         this._walkSpeed = 3.6;
         this._probability = 0.4;
         this._fallEquipmentsList = [{
            "id":15,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":16,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":17,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":18,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":78,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":79,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":80,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":81,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":41,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":42,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":43,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":44,
            "qualityID":[1],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":112 + Math.round(Math.random() * 20),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
   }
}

