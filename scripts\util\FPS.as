package util
{
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.system.System;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   import flash.utils.getTimer;
   
   public class FPS extends Sprite
   {
      
      private var last:uint = getTimer();
      
      private var ticks:uint = 0;
      
      public var tf:TextField;
      
      public function FPS(param1:int = 0, param2:int = 0, param3:uint = 0, param4:Boolean = false, param5:uint = 0)
      {
         super();
         x = param1;
         y = param2;
         this.tf = new TextField();
         this.tf.textColor = param3;
         this.tf.text = "----- fps";
         this.tf.selectable = false;
         this.tf.background = param4;
         this.tf.backgroundColor = param5;
         this.tf.autoSize = TextFieldAutoSize.LEFT;
         this.tf.selectable = false;
         addChild(this.tf);
         width = this.tf.textWidth;
         height = this.tf.textHeight;
         addEventListener(Event.ENTER_FRAME,this.tick);
         addEventListener(Event.ADDED_TO_STAGE,this.added);
         this.visible = false;
      }
      
      public function added(param1:Event) : void
      {
         parent.stage.addEventListener(KeyboardEvent.KEY_DOWN,this.key);
      }
      
      public function key(param1:KeyboardEvent) : void
      {
         if(param1.keyCode == 113)
         {
            this.visible = !this.visible;
         }
      }
      
      public function tick(param1:Event) : void
      {
         var _loc4_:Number = NaN;
         ++this.ticks;
         var _loc2_:uint = uint(getTimer());
         var _loc3_:uint = uint(_loc2_ - this.last);
         if(_loc3_ >= 1000)
         {
            _loc4_ = this.ticks / _loc3_ * 1000;
            this.tf.text = "fps:" + _loc4_.toFixed(1) + " , mem:" + System.totalMemory;
            this.ticks = 0;
            this.last = _loc2_;
         }
      }
   }
}

