package base
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import game.Game;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol114")]
   public class EnemyAppearPoint extends MovieClip
   {
      
      public var delay:int;
      
      public var interval:Number;
      
      public var totalNum:int;
      
      public var enemyType:int;
      
      public var isRandom:Boolean;
      
      public var stopPointIdx:int;
      
      public var enemyDisappearPoint:MovieClip;
      
      public var _gameWorld:GameWorld;
      
      public var _game:Game;
      
      private var tk:ThreeKingdoms;
      
      private var count:int = 0;
      
      private var isStart:Boolean = false;
      
      private var currentCount:int = 0;
      
      public var isOver:Boolean = false;
      
      public var isReady:Boolean = false;
      
      private var randomArray:Array = [];
      
      public function EnemyAppearPoint()
      {
         super();
         this.tk = ThreeKingdoms._instance;
         this._gameWorld = ThreeKingdoms._gameWorld;
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.__removed,true,0,false);
      }
      
      private function __removed(param1:Event) : void
      {
         this.removeEventListener(Event.ENTER_FRAME,this.update);
      }
      
      public function start() : void
      {
         this.addEventListener(Event.ENTER_FRAME,this.update);
      }
      
      public function update(param1:Event) : void
      {
         var _loc2_:int = 0;
         if(!this.tk._game)
         {
            this.removeEventListener(Event.ENTER_FRAME,this.update);
         }
         if(!this.isStart)
         {
            if(this.count / 24 >= this.delay)
            {
               this.isStart = true;
               this.count = 0;
            }
         }
         if(this.isStart)
         {
            if(this.currentCount < this.totalNum)
            {
               if(this.count / 30 >= this.interval)
               {
                  this.isReady = true;
               }
            }
            if(this.count / 30 >= this.interval)
            {
               this.count = 0;
            }
         }
         if(this.isReady)
         {
            if(this._gameWorld._enemies.length < ThreeKingdoms._instance.maxEnemiesPerScreen)
            {
               ThreeKingdoms._instance._game.addEnemy(this.enemyType,this.x,this.y);
               ++this.currentCount;
               this.isReady = false;
            }
         }
         if(this.currentCount >= this.totalNum)
         {
            this.isOver = true;
            if(this.parent)
            {
               this.parent.removeChild(this);
               _loc2_ = int(ThreeKingdoms._gameWorld._sendEnemyPoint.indexOf(this));
               if(_loc2_ != -1)
               {
                  ThreeKingdoms._gameWorld._sendEnemyPoint.splice(_loc2_,1);
               }
            }
         }
         if(!this.isReady)
         {
            ++this.count;
         }
      }
   }
}

