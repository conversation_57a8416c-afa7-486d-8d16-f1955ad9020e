package
{
   import actors.*;
   import actors.equipments.*;
   import actors.holidays.HolidayManager;
   import actors.info.*;
   import actors.map.*;
   import actors.memory.Memory;
   import actors.menu.*;
   import actors.pets.*;
   import actors.roles.*;
   import actors.user.User;
   import base.*;
   import br.com.stimuli.loading.*;
   import calista.utils.Base64;
   import com.adobe.crypto.MD5;
   import com.adobe.serialization.json.AJSONDecoder;
   import event.GameEvent;
   import flash.display.MovieClip;
   import flash.display.StageAlign;
   import flash.display.StageQuality;
   import flash.display.StageScaleMode;
   import flash.errors.IllegalOperationError;
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import flash.net.navigateToURL;
   import flash.system.Security;
   import flash.utils.ByteArray;
   import flash.utils.Timer;
   import game.Game;
   import unit4399.events.*;
   import util.*;
   
   public class ThreeKingdoms extends MovieClip
   {
      
      public static var _instance:ThreeKingdoms;
      
      public static var _gameWorld:GameWorld;
      
      public static var _4399_function_ad_id:String = "92d6cef76cd06829e088932fe9fd819b";
      
      public static var _4399_function_score_id:String = "d8c8d4731a33a0a581edc746e73eadc7200";
      
      public static var _4399_function_archives_id:String = "********************************";
      
      public static var _4399_function_gameList_id:String = "944c23f5e64a80647f8d0f3435f5c7a8";
      
      public static var serviceHold:* = null;
      
      private var tipText:MovieClip;
      
      public var _players:uint = 1;
      
      public var _gameMode:int;
      
      public var _playerNum:int;
      
      public var thisLevelEnemyIsLoaded:Array = [0,0,0,0,0,0,0,0,0,0,0,0,0];
      
      public var _weightPercentLoaded:Number = 0;
      
      public var _currentLevel:uint = 1;
      
      public var _currentMaxLevel:uint = 1;
      
      public var maxEnemiesPerScreen:int = 6;
      
      public var _loader:BulkLoader;
      
      public var _memory:Memory;
      
      public var _gameMenu:GameMenu;
      
      public var _gameMap:GameMap;
      
      public var _viewController:ViewController;
      
      public var _town:Town;
      
      public var _game:Game;
      
      public var _longshot:LongShot;
      
      public var _protectedProperty:ProtectedProperty;
      
      public var _gameInfo:GameInformation;
      
      public var _equipments:Equipments;
      
      public var _suits:Suits;
      
      public var _gameQuality:MovieClip;
      
      public var _mcExchange:MovieClip;
      
      public var ipadNews:MovieClip;
      
      public var fps:FPS;
      
      public var _user1:User;
      
      public var _user2:User;
      
      public var _pet1user:User;
      
      public var _pet2user:User;
      
      public var _pet3user:User;
      
      public var _pet4user:User;
      
      public var _pet5user:User;
      
      public var _role1:Role1;
      
      public var _role2:Role2;
      
      public var _role3:Role3;
      
      public var _pet1:Pet1;
      
      public var _pet2:Pet2;
      
      public var _pet3:Pet3;
      
      public var _pet4:Pet4;
      
      public var _pet5:Pet5;
      
      public var isFirstInit:Boolean = true;
      
      public var _doubleGet:Boolean = false;
      
      public var _doubleGet2:Boolean = false;
      
      public var _isNewMemory:Boolean;
      
      public var _isNewUser:Boolean = false;
      
      public var _isGameOver:Boolean = false;
      
      public var _userName:String = "";
      
      public var _isEnemiesLoaded:Boolean = false;
      
      public var _loadingIndex:int = 0;
      
      public var _isSavedByUser:Boolean = false;
      
      public var _saveCount:int;
      
      private var _saveTimer:Timer;
      
      private var _lastLoadedItem:int = 0;
      
      public var _isDebug:Boolean = false;
      
      public var _HideSuit:Boolean = false;
      
      public var _isUserLogin:Boolean = true;
      
      public var firstArray:Array;
      
      public var Array2:Array;
      
      public var Array3:Array;
      
      private var _ctrlLoader:CtrlLoader;
      
      private var ctrlUrl:String = "http://cdn.comment.4399pk.com/control/ctrl_mo_v5.swf?200";
      
      private var gameID:String = "100014728";
      
      public var is4399PointGet:Boolean = false;
      
      public var get4399PointTimer:Timer;
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      private var gift:MovieClip;
      
      private var ipadIcon:MovieClip;
      
      private var lantern:MovieClip;
      
      private var _isSpring:Boolean = false;
      
      private var gift2:MovieClip;
      
      private var _isShare:Boolean = false;
      
      public var _laFrame:uint = 1;
      
      private var endTime1351:Date = new Date(2013,5,3,0,0);
      
      private var timer1351:Timer;
      
      private var beginTime:Date;
      
      private var gameVersion:String = "2.0";
      
      public function ThreeKingdoms()
      {
         super();
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         if(this._protectedProperty)
         {
            this._protectedProperty.clear();
         }
         this._protectedProperty = new ProtectedProperty();
      }
      
      private function holidayInit() : void
      {
         HolidayManager.getInstance().Init();
      }
      
      public function getDress() : void
      {
         var _loc1_:FallEquipments = null;
         var _loc2_:FallEquipments = null;
         var _loc3_:FallEquipments = null;
         var _loc4_:FallEquipments = null;
         var _loc5_:FallEquipments = null;
      }
      
      public function setHold(param1:*) : void
      {
         serviceHold = param1;
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      private function onAddedToStageHandler(param1:Event) : void
      {
         if(param1 != null)
         {
            removeEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         }
         _instance = this;
         _instance.stop();
         _gameWorld = new GameWorld(stage);
         this._loader = new BulkLoader("main");
         this._loader.add("assets/newmusic.swf");
         this._loader.add("assets/newgameMenu4.swf");
         this._loader.add("assets/new9role1.swf");
         this._loader.add("assets/new9role2.swf");
         this._loader.add("assets/new9role3.swf");
         this._loader.add("assets/town.swf");
         this._loader.add("assets/pet1.swf");
         this._loader.add("assets/pet2.swf");
         this._loader.add("assets/pet3.swf");
         this._loader.add("assets/pet4.swf");
         this._loader.add("assets/pet5.swf");
         this._loader.add("assets/scene2.swf");
         this._loader.add("assets/newroleInfo.swf");
         this._loader.add("assets/gameMap2.swf");
         this._loader.add("assets/newskillPanel.swf");
         this._loader.add("assets/newfallEquipment12.swf",{"id":"fallEquipment"});
         this._loader.add("assets/newpackage14.swf");
         this._loader.add("assets/childrenholiday.swf");
         this._loader.add("assets/wechatgift.swf");
         this._loader.addEventListener(BulkProgressEvent.PROGRESS,this.onBulkProgressHandler);
         this._loader.addEventListener(BulkProgressEvent.COMPLETE,this.onBulkCompleteHandler);
         this._loader.start();
         _gameWorld._eventManager.addEventListener(GameEvent.SELECT_HERO_IS_DONE,this.onSelectHeroIsDoneHandler);
         _gameWorld._eventManager.addEventListener(GameEvent.HERO_DEAD,this.onHeroDeadHandler);
         _gameWorld._eventManager.addEventListener(GameEvent.GAME_SAVED,this.gameSaveHandler);
         Security.allowDomain("*");
         Security.allowInsecureDomain("*");
         stage.showDefaultContextMenu = false;
         stage.scaleMode = StageScaleMode.EXACT_FIT;
         stage.align = StageAlign.TOP_LEFT;
         stage.showDefaultContextMenu = false;
         stage.addEventListener("serverTimeEvent",this.getServeTime);
         stage.addEventListener("logreturn",this.saveProcess);
         stage.addEventListener("userLoginOut",this.onUserLogOutHandler);
         stage.addEventListener("getuserdata",this.saveProcess);
         stage.addEventListener("saveuserdata",this.saveProcess);
         stage.addEventListener("MVC_CLOSE_PANEL",this.closePanelHandler);
         stage.addEventListener("getuserdatalist",this.saveProcess);
         stage.addEventListener("netSaveError",this.netSaveErrorHandler);
         stage.addEventListener("netGetError",this.netGetErrorHandler);
         stage.tabChildren = false;
      }
      
      private function getServeTime(param1:DataEvent) : void
      {
         if(param1.data == null)
         {
            return;
         }
         var _loc2_:* = String(this.timeToNumber(String(param1.data))).substr(0,8);
         var _loc3_:* = String(this.timeToNumber(String(param1.data))).substr(8,2);
         var _loc4_:uint = uint(_loc2_);
         var _loc5_:uint = uint(_loc3_);
         if(_loc4_ >= 20130101 && _loc4_ <= 20131231)
         {
            this._isShare = true;
         }
         if(_loc4_ > 20130530 && _loc4_ < 20130604)
         {
            this._doubleGet = true;
         }
         if(_loc4_ > 20130616)
         {
            HolidayManager.getInstance().is20130601Over = true;
         }
         if(_loc4_ > 20130627)
         {
            HolidayManager.getInstance().is20130627Over = true;
         }
      }
      
      private function getTimeDiff() : String
      {
         var _loc2_:uint = 0;
         var _loc3_:uint = 0;
         var _loc4_:uint = 0;
         var _loc5_:uint = 0;
         var _loc1_:Number = Math.round((this.endTime1351.getTime() - this.beginTime.getTime()) * 0.001);
         if(_loc1_ >= 0)
         {
            _loc2_ = Math.floor(_loc1_ / 60 / 60 / 24);
            _loc1_ %= 60 * 60 * 24;
            _loc3_ = Math.floor(_loc1_ / 60 / 60);
            _loc1_ %= 60 * 60;
            _loc4_ = Math.floor(_loc1_ / 60);
            _loc5_ = _loc1_ % 60;
         }
         return _loc2_ + " 天 " + this.fixZero(_loc3_) + " 小时 " + this.fixZero(_loc4_) + " 分 " + this.fixZero(_loc5_) + " 秒";
      }
      
      private function fixZero(param1:uint) : String
      {
         return param1 < 10 ? "0" + String(param1) : String(param1);
      }
      
      private function timerHandler(param1:*) : void
      {
         this.beginTime = new Date();
         var _loc2_:String = this.getTimeDiff();
         if(this.tipText != null)
         {
            this.tipText.txt.text = _loc2_;
         }
      }
      
      private function loadErrorHandler(param1:Event) : void
      {
         throw new IllegalOperationError("Cannot load ctrl_mo_v4.swf");
      }
      
      private function classLoadedHandler(param1:Event) : void
      {
         YuanChuangInface.getInstance().setInterface(stage,this._ctrlLoader.content,this.gameID);
      }
      
      private function gameSaveHandler(param1:GameEvent) : void
      {
         this._isSavedByUser = true;
         this._saveCount = 30;
         this._saveTimer = new Timer(1000);
         this._saveTimer.addEventListener(TimerEvent.TIMER,this.saveCounter);
         this._saveTimer.start();
      }
      
      private function saveCounter(param1:TimerEvent) : void
      {
         --this._saveCount;
         if(this._saveCount <= 0)
         {
            this._saveTimer.stop();
            this._saveTimer.removeEventListener(TimerEvent.TIMER,this.saveCounter);
            this._isSavedByUser = false;
         }
      }
      
      private function netGetErrorHandler(param1:Event) : void
      {
      }
      
      private function netSaveErrorHandler(param1:DataEvent) : void
      {
         var _loc2_:* = "网络取" + param1.data + "档失败了！";
      }
      
      private function closePanelHandler(param1:Event) : void
      {
         var _loc2_:MovieClip = this.getChildByName("SaveMc") as MovieClip;
         if(_loc2_)
         {
            this.removeChild(_loc2_);
            _loc2_ = null;
         }
         stage.focus = null;
         if(ThreeKingdoms._gameWorld._isPause && this._isUserLogin)
         {
            ThreeKingdoms._gameWorld.continueGame();
         }
      }
      
      private function onUserLogOutHandler(param1:Event) : void
      {
         this._isUserLogin = false;
         if(this._gameInfo)
         {
            removeChild(this._gameInfo);
            this._gameInfo = null;
         }
         if(this._game)
         {
            this._game.detory();
            this._game = null;
         }
         if(this._town)
         {
            removeChild(this._town);
            this._town = null;
         }
         this._role1 = null;
         this._role2 = null;
         this._role3 = null;
         this._user1 = new User();
         this._user2 = new User();
         this._user1._controlPlayer = 0;
         this._user2._controlPlayer = 1;
         this._memory = new Memory();
         ThreeKingdoms._gameWorld.destroy();
         this.showGameMenu();
      }
      
      private function onHeroDeadHandler(param1:GameEvent) : void
      {
      }
      
      public function judgeLog() : void
      {
         var _loc1_:Object = serviceHold.isLog;
         if(_loc1_)
         {
            this._isUserLogin = true;
            this._userName = _loc1_.nickName;
         }
         else
         {
            serviceHold.showLogPanel();
         }
      }
      
      private function saveProcess(param1:Event) : void
      {
         var _loc2_:ByteArray = null;
         var _loc3_:Object = null;
         var _loc4_:Array = null;
         var _loc5_:Array = null;
         var _loc6_:* = undefined;
         var _loc7_:* = undefined;
         var _loc8_:Object = null;
         var _loc9_:* = undefined;
         switch(param1.type)
         {
            case "logreturn":
               this._isUserLogin = true;
               this.send4399PointApi("login");
               this.get4399PointTimer = new Timer(1800000,1);
               this.get4399PointTimer.start();
               this.get4399PointTimer.addEventListener(TimerEvent.TIMER_COMPLETE,this.onTimerEventHandler);
               this._userName = param1["ret"].nickName;
               this._gameMenu.startMainGame();
               break;
            case "saveuserdata":
               if(param1["ret"] as Boolean == true)
               {
                  this._isNewUser = false;
                  _loc6_ = GameUtility.getObject("Saving") as MovieClip;
                  _loc6_.x = this.stage.stageWidth * 0.5;
                  _loc6_.y = this.stage.stageHeight * 0.5;
                  this.addChild(_loc6_);
                  if(this._memory._neeUI)
                  {
                     serviceHold.getList();
                  }
               }
               break;
            case "getuserdata":
               if(param1["ret"].title.indexOf("刘备") != -1 && param1["ret"].title.indexOf("关羽") != -1 || param1["ret"].title.indexOf("刘备") != -1 && param1["ret"].title.indexOf("张飞") != -1 || param1["ret"].title.indexOf("张飞") != -1 && param1["ret"].title.indexOf("关羽") != -1)
               {
                  this._gameMode = 2;
                  this._players = 2;
               }
               else
               {
                  this._gameMode = 1;
                  this._players = 1;
               }
               this._memory._saveIndex = param1["ret"].index;
               _loc2_ = new ByteArray();
               _loc2_ = Base64.decodeToByteArray(param1["ret"].data);
               _loc2_.uncompress();
               _loc3_ = _loc2_.readObject();
               this._memory.memoryValue(_loc3_);
               if(this._gameMenu)
               {
                  removeChild(this._gameMenu);
                  this._gameMenu = null;
               }
               this._memory.getMemory();
               this.initGame();
               break;
            case "getuserdatalist":
               _loc4_ = param1["ret"] as Array;
               _loc5_ = new Array(0,0,0,0,0,0,0,0);
               if(_loc4_ == null)
               {
                  break;
               }
               for(_loc7_ in _loc4_)
               {
                  _loc8_ = _loc4_[_loc7_];
                  if(_loc8_ != null)
                  {
                     _loc9_ = this.timeToNumber(_loc8_.datetime);
                     _loc5_.splice(_loc8_.index,1,_loc9_);
                  }
               }
               this.getIndex(_loc5_);
               break;
         }
      }
      
      private function onTimerEventHandler(param1:TimerEvent) : void
      {
         this.send4399PointApi("30min");
      }
      
      private function send4399PointApi(param1:String) : void
      {
         var _loc2_:String = param1;
         var _loc3_:int = 72550;
         var _loc4_:int = int(serviceHold.isLog.uid);
         var _loc5_:Date = new Date();
         var _loc6_:int = int(_loc5_.getTime());
         var _loc7_:String = "2d2dfa46e848c848b2357359d9548ce6";
         var _loc8_:String = "|";
         var _loc9_:String = "/services/game-4399api";
         var _loc10_:String = MD5.hash(_loc2_ + _loc8_ + _loc3_ + _loc8_ + _loc6_ + _loc8_ + _loc4_ + _loc8_ + _loc9_ + _loc8_ + _loc7_);
         var _loc11_:URLLoader = new URLLoader();
         var _loc12_:URLRequest = new URLRequest("http://my.4399.com/services/game-4399api?");
         var _loc13_:URLVariables = new URLVariables();
         _loc13_.desc = _loc2_;
         _loc13_.uid = _loc4_;
         _loc13_.gameId = _loc3_;
         _loc13_.time = _loc6_;
         _loc13_.syn = _loc10_;
         _loc12_.method = URLRequestMethod.POST;
         _loc12_.data = _loc13_;
         _loc11_.addEventListener(Event.COMPLETE,this.on4399PointCompleteHandler);
         _loc11_.load(_loc12_);
      }
      
      private function on4399PointCompleteHandler(param1:Event) : void
      {
         var _loc2_:String = param1.target.data;
         var _loc3_:AJSONDecoder = new AJSONDecoder(_loc2_,true);
         var _loc4_:Object = _loc3_.getValue();
         switch(_loc4_["code"])
         {
            case 0:
            case 97:
            case -2:
            case 1:
         }
      }
      
      private function getIndex(param1:*) : void
      {
         var _loc2_:Array = param1.concat();
         _loc2_.sort(Array.NUMERIC);
         ThreeKingdoms._instance._memory._saveIndex = param1.indexOf(_loc2_[param1.length - 1]);
      }
      
      private function timeToNumber(param1:String) : Number
      {
         var _loc4_:Array = null;
         var _loc2_:RegExp = /\d+/g;
         var _loc3_:String = "";
         _loc4_ = param1.match(_loc2_);
         var _loc5_:* = 0;
         while(_loc5_ < _loc4_.length)
         {
            _loc3_ += _loc4_[_loc5_];
            _loc5_++;
         }
         return Number(_loc3_);
      }
      
      private function showSavingPanel(param1:Object) : void
      {
         _gameWorld.continueGame();
      }
      
      private function onSelectHeroIsDoneHandler(param1:GameEvent) : void
      {
         _gameWorld._eventManager.removeEventListener(GameEvent.SELECT_HERO_IS_DONE,this.onSelectHeroIsDoneHandler);
      }
      
      private function onBulkCompleteHandler(param1:BulkProgressEvent) : void
      {
         this._lastLoadedItem = param1.itemsTotal;
         var _loc2_:MovieClip = this.getChildByName("onBulkProgress") as MovieClip;
         if(_loc2_)
         {
            this.removeChild(_loc2_);
            _loc2_ = null;
         }
         if(this._loadingIndex == 0)
         {
            this._user1 = new User();
            this._user1._controlPlayer = 0;
            this._user2 = new User();
            this._user2._controlPlayer = 1;
            this._pet1user = new User();
            this._pet2user = new User();
            this._pet3user = new User();
            this._pet4user = new User();
            this._pet5user = new User();
            this._memory = new Memory();
            this.showGameMenu();
         }
         else if(this._loadingIndex == 2)
         {
            this._isEnemiesLoaded = true;
            this.startGame(this._currentLevel);
         }
      }
      
      public function showGameMenu() : *
      {
         var _loc1_:int = 0;
         _loc1_ = 1;
         while(_loc1_ <= 100)
         {
            _loc1_++;
         }
         if(this._longshot)
         {
            this.removeChild(this._longshot);
            this._longshot = null;
         }
         this.fps = new FPS(10,10,16711680);
         if(this._gameQuality)
         {
            this.removeChild(this._gameQuality);
            this._gameQuality = null;
         }
         if(this._game)
         {
            this.removeChild(this._game);
         }
         if(this._gameMenu == null)
         {
            this._gameMenu = GameUtility.getObject("actors.menu.GameMenu") as GameMenu;
         }
         this._gameMenu.menuButton.x = 400.9;
         this._gameMenu.menuButton.y = 173.3;
         this._gameMenu.initMenu.x = 408.25;
         this._gameMenu.initMenu.y = 614.55;
         this._gameMenu.initMenu.visible = false;
         this._gameMenu.menuButton.visible = true;
         this._gameMenu.txtVersion.text = this.gameVersion;
         this.addChild(this._gameMenu);
         SoundManager.play("begin");
         if(serviceHold)
         {
            serviceHold.getServerTime();
         }
      }
      
      public function showGameMap() : *
      {
         if(this._gameInfo)
         {
            removeChild(this._gameInfo);
         }
         if(this._viewController)
         {
            this._viewController = null;
         }
         if(this._gameMap == null)
         {
            this._gameMap = GameUtility.getObject("actors.map.GameMap") as GameMap;
         }
         this.addChild(this._gameMap);
      }
      
      public function initGame() : *
      {
         this._mcExchange = null;
         serviceHold.getServerTime();
         this.tabChildren = false;
         _gameWorld.destroy();
         if(this._gameMenu)
         {
            GameUtility.clearDisplayList(this._gameMenu);
            this.removeChild(this._gameMenu);
            this._gameMenu = null;
         }
         if(this._longshot)
         {
            this.removeChild(this._longshot);
            this._longshot = null;
         }
         SoundManager.play("town");
         if(this._game)
         {
            this._game.detory();
            this._game = null;
         }
         stage.focus = null;
         this._longshot = GameUtility.getObject("actors.map.LongShot");
         addChild(this._longshot);
         this._town = new Town();
         this.addChild(this._town);
         _gameWorld.addHero(this["_role" + this._user1._roleID]);
         _gameWorld._heroes[0].initProperties();
         _gameWorld._heroes[0].levelClear();
         _gameWorld._heroes[0].x = 300;
         _gameWorld._heroes[0].y = 510;
         _gameWorld._heroes[0]._vx = 0;
         _gameWorld._heroes[0].gotoAndStop(1);
         this._town.addChild(Hero(_gameWorld._heroes[0]));
         if(_gameWorld._heroes[0].pet)
         {
            _gameWorld._heroes[0].pet.levelClear();
            _gameWorld.addPet(Hero(_gameWorld._heroes[0]).pet);
            _gameWorld._heroes[0].pet.x = 250;
            _gameWorld._heroes[0].pet.y = 510;
            this._town.addChild(_gameWorld._heroes[0].pet);
         }
         if(this._gameMode == 2)
         {
            _gameWorld.addHero(this["_role" + this._user2._roleID]);
            _gameWorld._heroes[1].initProperties();
            _gameWorld._heroes[1].levelClear();
            _gameWorld._heroes[1].x = 400;
            _gameWorld._heroes[1].y = 510;
            _gameWorld._heroes[1]._vx = 0;
            _gameWorld._heroes[1].gotoAndStop(1);
            this._town.addChild(Hero(_gameWorld._heroes[1]));
            if(_gameWorld._heroes[1].pet)
            {
               _gameWorld._heroes[1].pet.levelClear();
               _gameWorld.addPet(Hero(_gameWorld._heroes[1]).pet);
               _gameWorld._heroes[1].pet.x = 350;
               _gameWorld._heroes[1].pet.y = 510;
               this._town.addChild(_gameWorld._heroes[1].pet);
            }
         }
         this._gameInfo = new GameInformation();
         this._gameInfo.destroy();
         addChild(this._gameInfo);
         if(this._gameQuality)
         {
            addChild(this._gameQuality);
            this._gameQuality.visible = true;
         }
         else
         {
            this._gameQuality = new Quality();
            addChild(this._gameQuality);
            this._gameQuality.x = 74;
            this._gameQuality.y = 15;
            this._gameQuality.addEventListener(MouseEvent.CLICK,this.qualityChoose);
            stage.quality = StageQuality.HIGH;
            this._gameQuality["medium"].buttonMode = true;
            this._gameQuality["high"].buttonMode = true;
            this._gameQuality["low"].buttonMode = true;
            this._gameQuality["high"].gotoAndStop(2);
            this._gameQuality.visible = true;
         }
         if(this._isShare)
         {
            this.gift = GameUtility.getObject("shareGift");
            this._town.addChild(this.gift);
            this.gift.x = 370;
            this.gift.y = 200;
            this.gift.buttonMode = true;
            this.gift.addEventListener(MouseEvent.CLICK,this.onGiftHandler);
         }
         if(this._doubleGet)
         {
            this.timer1351 = new Timer(1000);
            this.timer1351.addEventListener(TimerEvent.TIMER,this.timerHandler);
            this.tipText = new TxtIcon();
            this.tipText.x = 10;
            this.tipText.y = 155;
            this.timer1351.start();
            this._town.addChild(this.tipText);
         }
         this.ipadIcon = GameUtility.getObject("IpadIcon");
         this._town.addChild(this.ipadIcon);
         this.ipadIcon.x = 320;
         this.ipadIcon.y = 200;
         this.ipadIcon.buttonMode = true;
         this.ipadIcon.addEventListener(MouseEvent.CLICK,this.onIpadNews);
         this._equipments = new Equipments();
         this._suits = new Suits();
         _gameWorld.start();
         this.holidayInit();
         _gameWorld._eventManager.addEventListener(GameEvent.GAME_SAVED,this.gameSaveHandler);
      }
      
      private function onIpadNews(param1:MouseEvent) : void
      {
         if(this.ipadNews == null)
         {
            this.ipadNews = new IpadNews();
            this._town.addChild(this.ipadNews);
            this.ipadNews.x = 488.3;
            this.ipadNews.y = 298;
            this.ipadNews["btnEnter"].addEventListener(MouseEvent.CLICK,this.btnEnter);
            this.ipadNews["btnClose"].addEventListener(MouseEvent.CLICK,this.onCloseIpadHandler);
         }
      }
      
      private function btnEnter(param1:MouseEvent) : void
      {
         navigateToURL(new URLRequest("http://my.4399.com/forums-thread-tagid-81166-id-33278261.html"),"_blank");
      }
      
      private function onCloseIpadHandler(param1:MouseEvent) : void
      {
         if(this.ipadNews != null)
         {
            this.ipadNews["btnEnter"].removeEventListener(MouseEvent.CLICK,this.btnEnter);
            this.ipadNews["btnClose"].removeEventListener(MouseEvent.CLICK,this.onCloseIpadHandler);
            this._town.removeChild(this.ipadNews);
            this.ipadNews = null;
         }
      }
      
      private function exChangeHandler(param1:MouseEvent) : void
      {
         var _loc11_:MovieClip = null;
         if(_loc11_ == null)
         {
            _loc11_ = GameUtility.getObject("getGift");
         }
         if(Hero(_gameWorld._heroes[0]).getPlayer()._equipmentsVector.length >= 30)
         {
            _loc11_["mc"]["txt"].text = "玩家1背包已满,请卖出部分装备。";
            _loc11_.x = 488.3;
            _loc11_.y = 298;
            this.addChild(_loc11_);
            return;
         }
         if(this._gameMode == 2)
         {
            if(Hero(_gameWorld._heroes[1]).getPlayer()._equipmentsVector.length >= 30)
            {
               _loc11_["mc"]["txt"].text = "玩家2背包已满,请卖出部分装备。";
               _loc11_.x = 488.3;
               _loc11_.y = 298;
               this.addChild(_loc11_);
               return;
            }
         }
         var _loc2_:String = "116";
         var _loc3_:String = serviceHold.isLog.uid;
         var _loc4_:String = String(this._mcExchange["exCodeTxt"].text);
         var _loc5_:String = "||";
         var _loc6_:String = "31b60eda055cbf9b8ede4ca5424faecc";
         var _loc7_:String = MD5.hash(_loc3_ + _loc5_ + _loc2_ + _loc5_ + _loc4_ + _loc5_ + _loc6_);
         var _loc8_:URLLoader = new URLLoader();
         var _loc9_:URLRequest = new URLRequest("http://my.4399.com/jifen/api-apply?");
         var _loc10_:URLVariables = new URLVariables();
         _loc10_.gid = _loc2_;
         _loc10_.uid = _loc3_;
         _loc10_.activation = _loc4_;
         _loc10_.token = _loc7_;
         _loc9_.method = URLRequestMethod.POST;
         _loc9_.data = _loc10_;
         _loc8_.addEventListener(Event.COMPLETE,this.onGiftDataCompleteHandler);
         _loc8_.load(_loc9_);
         if(this._mcExchange)
         {
            this._mcExchange["btnExchange"].removeEventListener(MouseEvent.CLICK,this.exChangeHandler);
         }
      }
      
      private function onGiftDataCompleteHandler(param1:Event) : void
      {
         var _loc8_:Equipment = null;
         var _loc9_:Equipment = null;
         var _loc10_:Equipment = null;
         var _loc2_:String = param1.target.data;
         var _loc3_:String = "";
         var _loc4_:String = "";
         var _loc5_:AJSONDecoder = new AJSONDecoder(_loc2_,true);
         var _loc6_:Object = _loc5_.getValue();
         _loc3_ = _loc6_.code;
         _loc4_ = _loc6_.msg;
         var _loc7_:MovieClip = GameUtility.getObject("getGift");
         if(_loc3_ == "100")
         {
            _loc8_ = this._equipments.getEquipmentByID(110) as Equipment;
            _loc9_ = this._equipments.getEquipmentByID(111) as Equipment;
            _loc10_ = this._equipments.getEquipmentByID(112) as Equipment;
            if(Hero(_gameWorld._heroes[0])._properties.getLevel() <= 10)
            {
               _loc8_.index = _loc9_.index = _loc10_.index = 0;
            }
            else if(Hero(_gameWorld._heroes[0])._properties.getLevel() > 10 && Hero(_gameWorld._heroes[0])._properties.getLevel() <= 25)
            {
               _loc8_.index = _loc9_.index = _loc10_.index = 1;
            }
            else
            {
               _loc8_.index = _loc9_.index = _loc10_.index = 2;
            }
            if(Hero(_gameWorld._heroes[0]) is Role1)
            {
               Hero(_gameWorld._heroes[0]).getPlayer()._equipmentsVector.push(_loc8_);
            }
            else if(Hero(_gameWorld._heroes[0]) is Role2)
            {
               Hero(_gameWorld._heroes[0]).getPlayer()._equipmentsVector.push(_loc9_);
            }
            else
            {
               Hero(_gameWorld._heroes[0]).getPlayer()._equipmentsVector.push(_loc10_);
            }
            if(this._gameMode == 2)
            {
               if(Hero(_gameWorld._heroes[1]) is Role1)
               {
                  Hero(_gameWorld._heroes[1]).getPlayer()._equipmentsVector.push(_loc8_);
               }
               else if(Hero(_gameWorld._heroes[1]) is Role2)
               {
                  Hero(_gameWorld._heroes[1]).getPlayer()._equipmentsVector.push(_loc9_);
               }
               else
               {
                  Hero(_gameWorld._heroes[1]).getPlayer()._equipmentsVector.push(_loc10_);
               }
            }
            this._memory._neeUI = false;
            this._memory.setMemory();
            _loc7_["mc"]["txt"].text = "领取成功,请到背包查看";
            if(this._mcExchange)
            {
               this._mcExchange["btnExchange"].removeEventListener(MouseEvent.CLICK,this.exChangeHandler);
               this._mcExchange["btnClose"].removeEventListener(MouseEvent.CLICK,this.onCloseHandler);
               this._town.removeChild(this._mcExchange);
               this._mcExchange = null;
            }
         }
         else
         {
            if(this._mcExchange)
            {
               this._mcExchange["btnExchange"].addEventListener(MouseEvent.CLICK,this.exChangeHandler);
            }
            switch(_loc3_)
            {
               case "99":
                  _loc7_["mc"]["txt"].text = "未知错误";
                  break;
               case "101":
                  _loc7_["mc"]["txt"].text = "参数错误";
                  break;
               case "102":
                  _loc7_["mc"]["txt"].text = "激活码不存在";
                  break;
               case "103":
                  _loc7_["mc"]["txt"].text = "激活码还没被兑换";
                  break;
               case "104":
                  _loc7_["mc"]["txt"].text = "激活码已经被使用过了哦";
                  break;
               case "105":
                  _loc7_["mc"]["txt"].text = "激活码只能被领取者使用";
                  break;
               case "106":
                  _loc7_["mc"]["txt"].text = "每个账号只有一次使用激活码的机会，该账号已经使用过了";
                  break;
               case "107":
                  _loc7_["mc"]["txt"].text = "token失效";
                  break;
               case "108":
                  _loc7_["mc"]["txt"].text = "激活码过期了";
                  break;
               case "109":
                  _loc7_["mc"]["txt"].text = "今天激活码已经兑换完";
                  break;
               default:
                  _loc7_["mc"]["txt"].text = "领取失败";
            }
         }
         _loc7_.x = 488.3;
         _loc7_.y = 298;
         this.addChild(_loc7_);
      }
      
      private function onLoadDataCompleteHandler(param1:Event) : void
      {
         var _loc5_:Equipment = null;
         var _loc6_:Equipment = null;
         var _loc7_:Equipment = null;
         var _loc2_:String = param1.target.data;
         var _loc3_:Array = _loc2_.split("|");
         var _loc4_:MovieClip = GameUtility.getObject("getGift");
         if(_loc3_[0] == "1")
         {
            _loc5_ = this._equipments.getEquipmentByID(110) as Equipment;
            _loc6_ = this._equipments.getEquipmentByID(111) as Equipment;
            _loc7_ = this._equipments.getEquipmentByID(112) as Equipment;
            if(Hero(_gameWorld._heroes[0])._properties.getLevel() <= 10)
            {
               _loc5_.index = _loc6_.index = _loc7_.index = 0;
            }
            else if(Hero(_gameWorld._heroes[0])._properties.getLevel() > 10 && Hero(_gameWorld._heroes[0])._properties.getLevel() <= 25)
            {
               _loc5_.index = _loc6_.index = _loc7_.index = 1;
            }
            else
            {
               _loc5_.index = _loc6_.index = _loc7_.index = 2;
            }
            if(Hero(_gameWorld._heroes[0]) is Role1)
            {
               Hero(_gameWorld._heroes[0]).getPlayer()._equipmentsVector.push(_loc5_);
            }
            else if(Hero(_gameWorld._heroes[0]) is Role2)
            {
               Hero(_gameWorld._heroes[0]).getPlayer()._equipmentsVector.push(_loc6_);
            }
            else
            {
               Hero(_gameWorld._heroes[0]).getPlayer()._equipmentsVector.push(_loc7_);
            }
            if(this._gameMode == 2)
            {
               if(Hero(_gameWorld._heroes[1]) is Role1)
               {
                  Hero(_gameWorld._heroes[1]).getPlayer()._equipmentsVector.push(_loc5_);
               }
               else if(Hero(_gameWorld._heroes[1]) is Role2)
               {
                  Hero(_gameWorld._heroes[1]).getPlayer()._equipmentsVector.push(_loc6_);
               }
               else
               {
                  Hero(_gameWorld._heroes[1]).getPlayer()._equipmentsVector.push(_loc7_);
               }
            }
            this._memory._neeUI = false;
            this._memory.setMemory();
            _loc4_["mc"]["txt"].text = "领取成功,请到背包查看";
            if(!this.gift)
            {
            }
            if(this._mcExchange)
            {
               this._mcExchange["btnExchange"].removeEventListener(MouseEvent.CLICK,this.exChangeHandler);
               this._mcExchange["btnClose"].removeEventListener(MouseEvent.CLICK,this.onCloseHandler);
               this._town.removeChild(this._mcExchange);
               this._mcExchange = null;
            }
         }
         else if(_loc3_.length > 1)
         {
            if(this._mcExchange)
            {
               this._mcExchange["btnExchange"].addEventListener(MouseEvent.CLICK,this.exChangeHandler);
            }
            switch(_loc3_[1])
            {
               case "001":
                  _loc4_["mc"]["txt"].text = "活动还没开始";
                  break;
               case "002":
                  _loc4_["mc"]["txt"].text = "活动已经结束";
                  break;
               case "003":
                  _loc4_["mc"]["txt"].text = "未登录";
                  break;
               case "004":
                  _loc4_["mc"]["txt"].text = "兑换码无效";
                  break;
               default:
                  _loc4_["mc"]["txt"].text = "领取失败";
            }
         }
         _loc4_.x = 488.3;
         _loc4_.y = 298;
         this.addChild(_loc4_);
      }
      
      private function onGift2Handler(param1:MouseEvent) : void
      {
         var _loc3_:MovieClip = null;
         var _loc4_:MovieClip = null;
         if(_gameWorld._heroes.length == 1 && Hero(_gameWorld._heroes[0]).getPlayer()._petsVector.length >= 5)
         {
            _loc3_ = GameUtility.getObject("Hint");
            _loc3_.x = this.gift2.x;
            _loc3_.y = this.gift2.y;
            this.addChild(_loc3_);
            return;
         }
         if(_gameWorld._heroes.length > 1 && (Hero(_gameWorld._heroes[1]).getPlayer()._petsVector.length >= 5 || Hero(_gameWorld._heroes[0]).getPlayer()._petsVector.length >= 5))
         {
            _loc4_ = GameUtility.getObject("Hint");
            _loc4_.x = this.gift2.x;
            _loc4_.y = this.gift2.y;
            this.addChild(_loc4_);
            return;
         }
         var _loc2_:MovieClip = GameUtility.getObject("GetPet");
         this._town.addChild(_loc2_);
         if(Hero(_gameWorld._heroes[0]).getPlayer()._petsVector.length < 5)
         {
            this._pet1 = GameUtility.getObject("actors.pets.Pet1");
            this._pet1.setPlayer(this._pet1user);
            Hero(_gameWorld._heroes[0]).getPlayer()._petsVector.push(this._pet1);
            this._pet1._initAttackPower = 50 + Math.round(Math.random() * 50);
            this._pet1._initResistance = 50 + Math.round(Math.random() * 50);
            this._pet1.initProperties();
            _loc2_.x = 400;
            _loc2_.y = 400;
         }
         if(_gameWorld._heroes.length > 1 && Hero(_gameWorld._heroes[1]).getPlayer()._petsVector.length < 5)
         {
            this._pet1 = GameUtility.getObject("actors.pets.Pet1");
            this._pet1.setPlayer(this._pet1user);
            Hero(_gameWorld._heroes[1]).getPlayer()._petsVector.push(this._pet1);
            this._pet1._initAttackPower = 50 + Math.round(Math.random() * 50);
            this._pet1._initResistance = 50 + Math.round(Math.random() * 50);
            this._pet1.initProperties();
            _loc2_.x = 400;
            _loc2_.y = 500;
         }
         if(this._town)
         {
            this._memory._neeUI = false;
            this._memory.setMemory();
            this.gift2.removeEventListener(MouseEvent.CLICK,this.onGift2Handler);
            this._town.removeChild(this.gift2);
            this.gift2 = null;
         }
      }
      
      private function onGiftHandler(param1:MouseEvent) : void
      {
         if(this._mcExchange == null)
         {
            this._mcExchange = new mcExchange();
            this._town.addChild(this._mcExchange);
            this._mcExchange.x = 488.3;
            this._mcExchange.y = 298;
            if(this._gameMode == 1)
            {
               this._mcExchange.gotoAndStop(1);
            }
            else
            {
               this._mcExchange.gotoAndStop(2);
            }
            if(Hero(_gameWorld._heroes[0]) is Role1)
            {
               this._mcExchange["mcShow"]["mc1"].alpha = 1;
            }
            else if(Hero(_gameWorld._heroes[0]) is Role2)
            {
               this._mcExchange["mcShow"]["mc2"].alpha = 1;
            }
            else
            {
               this._mcExchange["mcShow"]["mc3"].alpha = 1;
            }
            if(this._gameMode == 2)
            {
               if(Hero(_gameWorld._heroes[1]) is Role1)
               {
                  this._mcExchange["mcShow2"]["mc1"].alpha = 1;
               }
               else if(Hero(_gameWorld._heroes[1]) is Role2)
               {
                  this._mcExchange["mcShow2"]["mc2"].alpha = 1;
               }
               else
               {
                  this._mcExchange["mcShow2"]["mc3"].alpha = 1;
               }
            }
            this._mcExchange["exCodeTxt"].text = "";
            this._mcExchange["btnExchange"].addEventListener(MouseEvent.CLICK,this.exChangeHandler);
            this._mcExchange["btnClose"].addEventListener(MouseEvent.CLICK,this.onCloseHandler);
         }
      }
      
      private function onCloseHandler(param1:MouseEvent) : void
      {
         if(this._mcExchange)
         {
            this._mcExchange["btnExchange"].removeEventListener(MouseEvent.CLICK,this.exChangeHandler);
            this._mcExchange["btnClose"].removeEventListener(MouseEvent.CLICK,this.onCloseHandler);
            this._town.removeChild(this._mcExchange);
            this._mcExchange = null;
         }
      }
      
      private function qualityChoose(param1:MouseEvent) : void
      {
         if(param1.target == this._gameQuality["high"])
         {
            stage.quality = StageQuality.HIGH;
         }
         else if(param1.target == this._gameQuality["medium"])
         {
            stage.quality = StageQuality.MEDIUM;
         }
         else if(param1.target == this._gameQuality["low"])
         {
            stage.quality = StageQuality.LOW;
         }
         this._gameQuality["medium"].gotoAndStop(1);
         this._gameQuality["high"].gotoAndStop(1);
         this._gameQuality["low"].gotoAndStop(1);
         param1.target.gotoAndStop(2);
      }
      
      public function startGame(param1:*) : void
      {
         stage.focus = null;
         this.tabChildren = false;
         this._longshot = GameUtility.getObject("actors.map.LongShot") as LongShot;
         this._longshot.gotoAndStop(param1);
         addChild(this._longshot);
         this._game = new Game();
         addChild(this._game);
         if(param1 == 1 || param1 == 2)
         {
            SoundManager.play("stage1");
         }
         if(param1 == 3)
         {
            SoundManager.play("stage2");
         }
         if(param1 == 4 || param1 == 5)
         {
            SoundManager.play("stage3");
         }
         if(param1 > 5 && param1 < 9)
         {
            SoundManager.play("stage4");
         }
         if(param1 == 9)
         {
            SoundManager.play("stage5");
         }
         _gameWorld.addHero(this["_role" + this._user1._roleID]);
         _gameWorld._heroes[0].initProperties();
         _gameWorld._heroes[0].levelClear();
         _gameWorld._heroes[0].x = 300;
         _gameWorld._heroes[0].y = 450;
         _gameWorld._heroes[0]._vx = 0;
         _gameWorld._heroes[0].gotoAndStop(1);
         this._game.addChild(Hero(_gameWorld._heroes[0]));
         if(_gameWorld._heroes[0].pet)
         {
            _gameWorld._heroes[0].pet.levelClear();
            _gameWorld.addPet(Hero(_gameWorld._heroes[0]).pet);
            _gameWorld._heroes[0].pet.x = 250;
            _gameWorld._heroes[0].pet.y = 450;
            this._game.addChild(_gameWorld._heroes[0].pet);
         }
         if(this._gameMode == 2)
         {
            _gameWorld.addHero(this["_role" + this._user2._roleID]);
            _gameWorld._heroes[1].initProperties();
            _gameWorld._heroes[1].levelClear();
            _gameWorld._heroes[1].x = 400;
            _gameWorld._heroes[1].y = 450;
            _gameWorld._heroes[1]._vx = 0;
            _gameWorld._heroes[1].gotoAndStop(1);
            this._game.addChild(Hero(_gameWorld._heroes[1]));
            if(_gameWorld._heroes[1].pet)
            {
               _gameWorld._heroes[1].pet.levelClear();
               _gameWorld.addPet(Hero(_gameWorld._heroes[1]).pet);
               _gameWorld._heroes[1].pet.x = 350;
               _gameWorld._heroes[1].pet.y = 450;
               this._game.addChild(_gameWorld._heroes[1].pet);
            }
         }
         this._viewController = new ViewController();
         this._gameInfo = new GameInformation();
         this._gameInfo.mouseChildren = true;
         addChild(this._gameInfo);
         if(this._gameQuality)
         {
            addChild(this._gameQuality);
            this._gameQuality.visible = true;
         }
         else
         {
            this._gameQuality = new Quality();
            this._gameQuality["medium"].buttonMode = true;
            this._gameQuality["high"].buttonMode = true;
            this._gameQuality["low"].buttonMode = true;
            this._gameQuality["high"].gotoAndStop(2);
            this._gameQuality.visible = true;
            this._gameQuality.addEventListener(MouseEvent.CLICK,this.qualityChoose);
            this._gameQuality.x = 74;
            this._gameQuality.y = 15;
            addChild(this._gameQuality);
            stage.quality = StageQuality.HIGH;
         }
         var _loc2_:* = GameUtility.getObject("Stories") as MovieClip;
         _loc2_.x = this.stage.stageWidth * 0.5;
         _loc2_.y = this.stage.stageHeight * 0.5;
         addChild(_loc2_);
         _gameWorld._eventManager.addEventListener(GameEvent.GAME_SAVED,this.gameSaveHandler);
         _gameWorld.start();
      }
      
      private function onBulkProgressHandler(param1:BulkProgressEvent) : void
      {
         var _loc2_:* = undefined;
         if(!this.getChildByName("onBulkProgress"))
         {
            _loc2_ = new LoadingBar();
            _loc2_.name = "onBulkProgress";
            if(this._loadingIndex == 0)
            {
               _loc2_.setProcess(Math.floor(param1.weightPercent * 100),param1.itemsLoaded + 1,param1.itemsTotal);
            }
            else
            {
               _loc2_.setProcess(Math.floor(param1.weightPercent * 100),param1.itemsLoaded + 1 - 11,param1.itemsTotal - 11);
            }
            this.addChild(_loc2_);
         }
         else if(this._loadingIndex == 0)
         {
            LoadingBar(this.getChildByName("onBulkProgress")).setProcess(Math.floor(param1.weightPercent * 100),param1.itemsLoaded + 1,param1.itemsTotal);
         }
         else
         {
            LoadingBar(this.getChildByName("onBulkProgress")).setProcess(Math.floor(param1.weightPercent * 100),param1.itemsLoaded - this._lastLoadedItem + 1,param1.itemsTotal - this._lastLoadedItem);
         }
      }
   }
}

