package actors.menu
{
   import actors.SoundManager;
   import actors.memory.Memory;
   import actors.user.User;
   import event.GameEvent;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import util.GameUtility;
   import util.UString;
   
   public class SettingMenu extends MovieClip
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public var continueGame:SimpleButton;
      
      public var controlSound:SimpleButton;
      
      public var backTown:SimpleButton;
      
      public var gameHelp:SimpleButton;
      
      public var backMenu:SimpleButton;
      
      public var btnBack:SimpleButton;
      
      public var mcCloseSound:MovieClip;
      
      public var mcOpenSound:MovieClip;
      
      public var saveGame:SimpleButton;
      
      public var saveTimer:TextField;
      
      private var tk:ThreeKingdoms;
      
      public function SettingMenu()
      {
         super();
         this.tk = ThreeKingdoms._instance;
         this.mcCloseSound.mouseEnabled = false;
         this.mcOpenSound.mouseEnabled = false;
         this.saveTimer.visible = false;
         this.addEventListener(Event.ADDED_TO_STAGE,this.added,false,0,true);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.removed,false,0,true);
      }
      
      private function removed(param1:Event) : void
      {
         ThreeKingdoms._gameWorld.continueGame();
         this.continueGame.removeEventListener(MouseEvent.CLICK,this.onContinueGameHandler);
         this.controlSound.removeEventListener(MouseEvent.CLICK,this.onControlSoundHandler);
         this.backTown.removeEventListener(MouseEvent.CLICK,this.onBackTownHandler);
         this.gameHelp.removeEventListener(MouseEvent.CLICK,this.onGameHelpHandler);
         this.backMenu.removeEventListener(MouseEvent.CLICK,this.onBackMenuHandler);
         this.btnBack.removeEventListener(MouseEvent.CLICK,this.onBtnBackHandler);
         if(!ThreeKingdoms._instance._isSavedByUser)
         {
            this.saveGame.removeEventListener(MouseEvent.CLICK,this.onSaveGameHandler);
         }
         if(this.saveTimer.visible)
         {
            this.removeEventListener(Event.ENTER_FRAME,this.setCountText);
         }
      }
      
      private function added(param1:Event) : void
      {
         if(param1 != null)
         {
            removeEventListener(Event.ADDED_TO_STAGE,this.added);
         }
         GameUtility.GC();
         if(this.tk._gameQuality)
         {
            this.tk._gameQuality.visible = false;
         }
         ThreeKingdoms._gameWorld.pauseGame();
         this.continueGame.addEventListener(MouseEvent.CLICK,this.onContinueGameHandler);
         this.controlSound.addEventListener(MouseEvent.CLICK,this.onControlSoundHandler);
         this.backTown.addEventListener(MouseEvent.CLICK,this.onBackTownHandler);
         this.gameHelp.addEventListener(MouseEvent.CLICK,this.onGameHelpHandler);
         this.backMenu.addEventListener(MouseEvent.CLICK,this.onBackMenuHandler);
         this.btnBack.addEventListener(MouseEvent.CLICK,this.onBtnBackHandler);
         if(!ThreeKingdoms._instance._isSavedByUser)
         {
            this.saveGame.addEventListener(MouseEvent.CLICK,this.onSaveGameHandler);
         }
         else
         {
            this.saveGame.mouseEnabled = false;
            this.saveTimer.visible = true;
            this.saveTimer.text = String(ThreeKingdoms._instance._saveCount);
            this.addEventListener(Event.ENTER_FRAME,this.setCountText);
         }
         this.initSound();
      }
      
      private function onSaveGameHandler(param1:MouseEvent) : void
      {
         this.tk._memory._neeUI = false;
         this.tk._memory.setMemory();
         ThreeKingdoms._gameWorld._eventManager.dispatchEvent(new GameEvent(GameEvent.GAME_SAVED));
         this.saveGame.removeEventListener(MouseEvent.CLICK,this.onSaveGameHandler);
         this.saveGame.mouseEnabled = false;
         this.saveTimer.visible = true;
         this.saveTimer.text = String(ThreeKingdoms._instance._saveCount);
         this.addEventListener(Event.ENTER_FRAME,this.setCountText);
      }
      
      private function setCountText(param1:Event) : void
      {
         this.saveTimer.text = String(ThreeKingdoms._instance._saveCount);
         if(ThreeKingdoms._instance._saveCount <= 0)
         {
            this.removeEventListener(Event.ENTER_FRAME,this.setCountText);
            this.saveGame.mouseEnabled = true;
            this.saveTimer.visible = false;
            this.saveGame.addEventListener(MouseEvent.CLICK,this.onSaveGameHandler);
         }
      }
      
      private function initSound() : void
      {
         if(SoundManager.soundStay)
         {
            this.mcCloseSound.visible = true;
            this.mcOpenSound.visible = false;
         }
         else
         {
            this.mcCloseSound.visible = false;
            this.mcOpenSound.visible = true;
         }
      }
      
      private function onBtnBackHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         this.removeChild(this.continueGame);
         stage.focus = null;
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         ThreeKingdoms._gameWorld.start();
         if(ThreeKingdoms._instance._gameQuality)
         {
            ThreeKingdoms._instance._gameQuality.visible = true;
         }
      }
      
      private function onGameHelpHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         var _loc2_:* = GameUtility.getObject("actors.menu.GameHelp") as GameHelp;
         addChild(_loc2_);
      }
      
      private function onBackMenuHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         if(this.tk._gameInfo)
         {
            this.tk.removeChild(this.tk._gameInfo);
            this.tk._gameInfo = null;
         }
         if(this.tk._game)
         {
            this.tk._game.detory();
            this.tk._game = null;
         }
         if(this.tk._town)
         {
            this.tk.removeChild(this.tk._town);
            this.tk._town = null;
         }
         this.tk._role1 = null;
         this.tk._role2 = null;
         this.tk._role3 = null;
         this.tk._user1 = new User();
         this.tk._user1._controlPlayer = 0;
         this.tk._user2 = new User();
         this.tk._user2._controlPlayer = 1;
         this.tk._memory = new Memory();
         ThreeKingdoms._gameWorld.destroy();
         this.tk.showGameMenu();
      }
      
      private function onBackTownHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         if(this.tk._town)
         {
            this.removeChild(this.backTown);
            stage.focus = null;
            if(this.parent)
            {
               this.parent.removeChild(this);
            }
            if(ThreeKingdoms._instance._gameQuality)
            {
               ThreeKingdoms._instance._gameQuality.visible = true;
            }
         }
         else
         {
            this.removeChild(this.backTown);
            stage.focus = null;
            this.tk._memory._neeUI = false;
            this.tk._memory.setMemory();
            if(this.parent)
            {
               this.parent.removeChild(this);
            }
            if(this.tk._longshot)
            {
               this.tk.removeChild(this.tk._longshot);
               this.tk._longshot = null;
            }
            if(this.tk._game)
            {
               this.tk._game.detory();
               this.tk._game = null;
            }
            if(this.tk._gameInfo)
            {
               this.tk.removeChild(this.tk._gameInfo);
               this.tk._gameInfo = null;
            }
            ThreeKingdoms._gameWorld.destroy();
            ThreeKingdoms._gameWorld.stop();
            this.tk.initGame();
         }
      }
      
      private function onControlSoundHandler(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         SoundManager.controlSound();
         if(SoundManager.soundStay)
         {
            this.mcCloseSound.visible = true;
            this.mcOpenSound.visible = false;
         }
         else
         {
            this.mcCloseSound.visible = false;
            this.mcOpenSound.visible = true;
         }
      }
      
      private function onContinueGameHandler(param1:MouseEvent) : void
      {
         this.removeChild(this.continueGame);
         stage.focus = null;
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         if(ThreeKingdoms._instance._gameQuality)
         {
            ThreeKingdoms._instance._gameQuality.visible = true;
         }
         ThreeKingdoms._gameWorld.continueGame();
      }
   }
}

