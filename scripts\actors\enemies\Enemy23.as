package actors.enemies
{
   import actors.Enemy;
   import flash.geom.Point;
   import util.GameUtility;
   
   public class Enemy23 extends Enemy
   {
      
      private var _isSummon:Boolean = false;
      
      private var _counter:int = 0;
      
      public function Enemy23(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this.enemyName = "董卓";
         this._object._maxPatrolView = 900;
         this._object._alertRange = 900;
         this._object._attackRange = 400;
         this._currentHealthPoint = 220000;
         this._totalHealthPoint = 220000;
         this.isBoss = true;
         this._attackProbablity = 100;
         this._resistance = 200;
         this._experience = 20090;
         this._goldPrice = 80000;
         this._walkSpeed = 7;
         this._probability = 0.2;
         this._fallEquipmentsList = [{
            "id":93,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":94,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":68,
            "qualityID":[0,1],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[10,10],
            "attackInterval":4,
            "attackPower":792 + Math.round(Math.random() * 40),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[10,10],
            "attackInterval":4,
            "attackPower":792 + Math.round(Math.random() * 40),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[10,10],
            "attackInterval":4,
            "attackPower":792 + Math.round(Math.random() * 40),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击4"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[10,10],
            "attackInterval":4,
            "attackPower":792 + Math.round(Math.random() * 40),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         if(this._currentHealthPoint <= this._totalHealthPoint / 7 && this._currentHealthPoint > 0 && !this._isSummon)
         {
            if(!this._isSummon)
            {
               this.realseSkill4();
               this._currentHealthPoint = 35000;
               this._isSummon = true;
            }
         }
         super.update();
      }
      
      public function summon() : void
      {
         var _loc1_:Point = this.parent.globalToLocal(new Point(480,300));
         var _loc2_:Enemy17 = _game.addEnemy(17,_loc1_.x,_loc1_.y) as Enemy17;
         _loc2_.isBoss = false;
      }
      
      override public function startAttacking() : void
      {
         if(this._curAttackTarget)
         {
            if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) > 700 && GameUtility.getDistance(this,this._curAttackTarget) < 960)
               {
                  if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 120;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 400 && GameUtility.getDistance(this,this._curAttackTarget) <= 700)
               {
                  if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 120;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 320;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) >= 250 && GameUtility.getDistance(this,this._curAttackTarget) <= 400)
               {
                  if(GameUtility.getRandomNumber(50))
                  {
                     if(_skill1CoolDown == 0)
                     {
                        this.realseSkill1();
                        _skill1CoolDown = 240;
                     }
                     else if(_skill2CoolDown == 0)
                     {
                        this.realseSkill2();
                        _skill2CoolDown = 120;
                     }
                     else
                     {
                        moveTowardHero();
                     }
                  }
                  else if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 240;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 280;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(_skill1CoolDown == 0)
               {
                  this.realseSkill1();
                  _skill1CoolDown = 240;
               }
               else if(_skill2CoolDown == 0)
               {
                  this.realseSkill2();
                  _skill2CoolDown = 120;
               }
               else if(_skill3CoolDown == 0)
               {
                  this.realseSkill3();
                  _skill3CoolDown = 280;
               }
               else
               {
                  this.attack();
               }
            }
            else
            {
               moveTowardHero();
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         setYourDaddysTime(30);
         newAttackID();
      }
      
      override public function realseSkill2() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击3";
         this.gotoAndStop("攻击3");
         setYourDaddysTime(49);
         newAttackID();
      }
      
      override public function realseSkill3() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击4";
         this.gotoAndStop("攻击4");
         setYourDaddysTime(30);
         newAttackID();
      }
      
      override public function realseSkill4() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击5";
         this.gotoAndStop("攻击5");
         setYourDaddysTime(115);
         newAttackID();
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(24);
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4" || this.currentLabel == "攻击5";
      }
   }
}

