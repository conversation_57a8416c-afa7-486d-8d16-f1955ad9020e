package actors.skill
{
   import actors.Hero;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import util.GameUtility;
   
   public class SkillPanel extends MovieClip
   {
      
      private var _roleControl:SkillControl;
      
      public var _hero:Hero;
      
      public var btnRole1:MovieClip;
      
      public var btnRole2:MovieClip;
      
      public var btnRole3:MovieClip;
      
      public var btnClose:SimpleButton;
      
      public function SkillPanel()
      {
         super();
         this.btnRole1.buttonMode = true;
         this.btnRole2.buttonMode = true;
         this.btnRole3.buttonMode = true;
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         this.btnRole1.removeEventListener(MouseEvent.CLICK,this.onClickRoleHandler);
         this.btnRole2.removeEventListener(MouseEvent.CLICK,this.onClickRoleHandler);
         this.btnRole3.removeEventListener(MouseEvent.CLICK,this.onClickRoleHandler);
      }
      
      private function onAddedToStageHandler(param1:Event) : void
      {
         var _loc3_:Hero = null;
         GameUtility.GC();
         removeEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         if(ThreeKingdoms._instance._gameQuality)
         {
            ThreeKingdoms._instance._gameQuality.visible = false;
         }
         this.btnRole1.gotoAndStop(5);
         this.btnRole2.gotoAndStop(5);
         this.btnRole3.gotoAndStop(5);
         this.btnRole1.mouseEnabled = false;
         this.btnRole2.mouseEnabled = false;
         this.btnRole3.mouseEnabled = false;
         this.btnRole1.mouseChildren = false;
         this.btnRole2.mouseChildren = false;
         this.btnRole3.mouseChildren = false;
         this.btnRole1.buttonMode = false;
         this.btnRole2.buttonMode = false;
         this.btnRole3.buttonMode = false;
         var _loc2_:int = 0;
         while(_loc2_ < ThreeKingdoms._gameWorld._heroes.length)
         {
            _loc3_ = ThreeKingdoms._gameWorld._heroes[_loc2_] as Hero;
            switch(_loc3_)
            {
               case ThreeKingdoms._instance._role1:
                  this.btnRole1.mouseEnabled = true;
                  this.btnRole1.buttonMode = true;
                  this.btnRole2.buttonMode = false;
                  this.btnRole3.buttonMode = false;
                  this.btnRole1.gotoAndStop(1);
                  this.btnRole1.addEventListener(MouseEvent.CLICK,this.onClickRoleHandler);
                  break;
               case ThreeKingdoms._instance._role2:
                  this.btnRole2.mouseEnabled = true;
                  this.btnRole2.buttonMode = true;
                  this.btnRole2.gotoAndStop(1);
                  this.btnRole2.addEventListener(MouseEvent.CLICK,this.onClickRoleHandler);
                  break;
               case ThreeKingdoms._instance._role3:
                  this.btnRole3.mouseEnabled = true;
                  this.btnRole3.buttonMode = true;
                  this.btnRole3.gotoAndStop(1);
                  this.btnRole3.addEventListener(MouseEvent.CLICK,this.onClickRoleHandler);
                  break;
            }
            _loc2_++;
         }
         this._roleControl = new SkillControl(this._hero);
         addChild(this._roleControl);
         this.btnClose.addEventListener(MouseEvent.CLICK,this.onCloseHandler);
         this.tabChildren = false;
         ThreeKingdoms._gameWorld.pauseGame();
      }
      
      private function onClickRoleHandler(param1:MouseEvent) : void
      {
         this.removeSkillContent();
         this._hero = this.belongsToWhom(String(param1.currentTarget.name));
         if(this._hero)
         {
            this._roleControl = new SkillControl(this._hero);
            this.addChild(this._roleControl);
         }
      }
      
      private function belongsToWhom(param1:String) : Hero
      {
         var _loc2_:String = "";
         if(param1 == "btnRole1")
         {
            _loc2_ = "LiuBei";
         }
         else if(param1 == "btnRole2")
         {
            _loc2_ = "GuanYu";
         }
         else if(param1 == "btnRole3")
         {
            _loc2_ = "ZhangFei";
         }
         var _loc3_:uint = ThreeKingdoms._gameWorld._heroes.length;
         while(_loc3_-- > 0)
         {
            if(Hero(ThreeKingdoms._gameWorld._heroes[_loc3_])._roleName == _loc2_)
            {
               return ThreeKingdoms._gameWorld._heroes[_loc3_] as Hero;
            }
         }
         return null;
      }
      
      private function removeSkillContent() : void
      {
         if(Boolean(this._roleControl) && contains(this._roleControl))
         {
            this.removeChild(this._roleControl);
            this._roleControl = null;
         }
      }
      
      private function onCloseHandler(param1:MouseEvent) : void
      {
         this.btnClose.parent.removeChild(this.btnClose);
         stage.focus = null;
         this.btnClose.removeEventListener(MouseEvent.CLICK,this.onCloseHandler);
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         if(ThreeKingdoms._instance._gameQuality)
         {
            ThreeKingdoms._instance._gameQuality.visible = true;
         }
         ThreeKingdoms._gameWorld.continueGame();
      }
   }
}

