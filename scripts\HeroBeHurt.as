package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol670")]
   public dynamic class HeroBeHurt extends MovieClip
   {
      
      public function HeroBeHurt()
      {
         super();
         addFrameScript(5,this.frame6);
      }
      
      internal function frame6() : *
      {
         if(parent)
         {
            parent.removeChild(this);
         }
      }
   }
}

