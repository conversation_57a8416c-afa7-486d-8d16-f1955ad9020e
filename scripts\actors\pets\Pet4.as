package actors.pets
{
   import actors.Properties;
   import base.BuffEffect;
   import flash.events.Event;
   import flash.geom.Point;
   import util.GameUtility;
   
   public class Pet4 extends Pet
   {
      
      private var tk:ThreeKingdoms = ThreeKingdoms._instance;
      
      public function Pet4()
      {
         super();
         this.gotoAndStop("休息");
         this._walkSpeed = 4.5;
         this._runSpeed = 10;
         this._properties = new Properties(this);
         this._buffEffect = new BuffEffect(this);
         this._object._maxPatrolView = 500;
         this._object._alertPatrolView = 500;
         this._object._attackRange = 100;
         this._attackProbablity = 50;
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         this._roleName = "土灵";
         this._roleType = "懒惰";
         this._roleFrameID = 4;
         this._description = "    幼灵的成长形态，因幼灵和土元素亲和度较高所化。拥有土属性能力。";
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
      }
      
      override public function onAddToStageHandler(param1:Event) : void
      {
         super.onAddToStageHandler(param1);
      }
      
      override public function onRemovedFromStageHandler(param1:Event) : void
      {
         super.onRemovedFromStageHandler(param1);
      }
      
      override public function initAR() : void
      {
         this._properties.setAttackPower(this._initAttackPower + 61 + (this._properties.getLevel() - 15) * 6);
         this._properties.setResistance(this._initResistance + 49 + (this._properties.getLevel() - 15) * 9);
      }
      
      override public function initProperties() : void
      {
         this.levelUP(_properties.getLevel());
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
      }
      
      override public function levelUP(param1:int = 14) : void
      {
         var _loc2_:uint = 0;
         var _loc3_:* = undefined;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         if(!this._isFirstTimeToInit)
         {
            this._properties.destroy();
         }
         this._isFirstTimeToInit = false;
         this._properties.setTotalHealthPoint(200 + (this._properties.getLevel() - 1) * 60);
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(60 + (this._properties.getLevel() - 1) * 12);
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
         this._properties.setAttackPower(this._initAttackPower + 61 + (this._properties.getLevel() - 15) * 6);
         this._properties.setResistance(this._initResistance + 49 + (this._properties.getLevel() - 15) * 9);
         this._properties.setTotalExperience((this._properties.getLevel() - 1) * (this._properties.getLevel() - 1) * 110 + 50 * this._properties.getLevel());
         if(this._properties.getLevel() >= 10)
         {
            _loc2_ = this._properties.getLevel() / 10;
            this._properties.setHealthRegeneration(_loc2_);
            this._properties.setManaRegeneration(_loc2_);
         }
         this._properties.initAll();
         if(this._properties.getLevel() == 30)
         {
            _game = ThreeKingdoms._instance._game;
            _loc3_ = new Point(this.x,this.y);
            if(this.parent)
            {
               this.parent.removeChild(this);
            }
            _loc4_ = int(ThreeKingdoms._gameWorld._pets.indexOf(this));
            _loc5_ = int(_master.getPlayer()._petsVector.indexOf(this));
            if(_loc5_ != -1)
            {
               _master.getPlayer()._petsVector.splice(_loc5_,1);
            }
            if(_loc4_ != -1)
            {
               ThreeKingdoms._gameWorld._pets.splice(_loc4_,1);
            }
            this.tk._pet5 = GameUtility.getObject("actors.pets.Pet5");
            this.tk._pet5.setPlayer(this.tk._pet5user);
            this.tk._pet5user._roleID = 6;
            this.tk._pet5._initAttackPower = this._initAttackPower;
            this.tk._pet5._initResistance = this._initResistance;
            this.tk._pet5._feedLevel = this._feedLevel;
            this.tk._pet5._properties.setLevel(29);
            this.tk._pet5user.hero = this.tk._pet5;
            this.tk._pet5.setMaster(this._master);
            this.tk._pet5.x = _loc3_.x;
            this.tk._pet5.y = _loc3_.y - 40;
            _game.addChild(this.tk._pet5);
            this.tk._pet5.gotoAndStop("出现");
            ThreeKingdoms._gameWorld.addPet(this.tk._pet5);
            _master.getPlayer()._petsVector.push(this.tk._pet5);
         }
         this._properties.setTotalHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(this._properties.getTotalManaPoint());
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
      }
      
      override public function getSpecificAttackPowerByType(param1:String) : int
      {
         switch(param1)
         {
            case "攻击1":
               return this._properties.getTotalAttackPower();
            case "攻击2":
               return this._properties.getTotalAttackPower() * 1.5;
            default:
               return 0;
         }
      }
      
      override public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(_attackProbablity))
         {
            if(_skill1CoolDown == 0)
            {
               if(GameUtility.getDistance(this,this._currentAttakTarget) <= 380)
               {
                  this.realseSkill1();
                  _skill1CoolDown = 90;
               }
               else
               {
                  followTarget();
               }
            }
            else if(GameUtility.getDistance(this,this._currentAttakTarget) <= 120)
            {
               this.attack();
            }
            else
            {
               followTarget();
            }
         }
         else
         {
            followTarget();
         }
      }
      
      override public function realseSkill1() : void
      {
         if(this._properties.getCurrentManaPoint() > 20)
         {
            steer();
            _vx = 0;
            this._lastHit = "攻击2";
            this.gotoAndStop("攻击2");
            setYourDaddysTime(17);
            newAttackID();
            this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 20);
         }
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         this.setYourDaddysTime(14);
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2";
      }
      
      override public function update() : void
      {
         super.update();
      }
   }
}

