package base
{
   import actors.Hero;
   import actors.enemies.Enemy22;
   import actors.roles.Role1;
   import actors.roles.Role2;
   import actors.roles.Role3;
   
   public class BuffEffect
   {
      
      public static const POSION:String = "posion";
      
      public static const FATHER:String = "father";
      
      public static const ARMOR:String = "armor";
      
      public static const BLOOD:String = "blood";
      
      public static const ICE:String = "ice";
      
      public static const POWER:String = "power";
      
      private var _sourceRole:GameObject;
      
      private var _counter:int = 0;
      
      private var _currentEffect:Array = [];
      
      private var _beenAttackFatherTotalCount:int = 5;
      
      private var _lastBeenAttackTime:int = 0;
      
      public function BuffEffect(param1:GameObject)
      {
         super();
         this._sourceRole = param1;
      }
      
      public function update() : void
      {
         var _loc2_:Object = null;
         var _loc1_:int = 0;
         while(_loc1_ < this._currentEffect.length)
         {
            _loc2_ = this._currentEffect[_loc1_];
            if(_loc2_.isFirstTime)
            {
               _loc2_.startTime = this._counter;
               _loc2_.isFirstTime = false;
               if(_loc2_.name == BuffEffect.POSION)
               {
               }
               if(_loc2_.name == BuffEffect.ICE)
               {
               }
               if(_loc2_.name == BuffEffect.ARMOR)
               {
               }
            }
            if(_loc2_.isForever != 1 && this._counter - _loc2_.startTime >= _loc2_.time)
            {
               this.removeEffect(_loc2_);
            }
            if(_loc2_.name == BuffEffect.POSION)
            {
               if(this._counter % 30 == 0)
               {
                  this._sourceRole.decreaseHealthPoint(_loc2_.power);
                  if(this._sourceRole is Hero)
                  {
                     Hero(this._sourceRole).addHeroGetHurtAnimation(_loc2_.power,_loc2_.power);
                  }
               }
            }
            ++this._counter;
            _loc1_++;
         }
      }
      
      public function removeEffect(param1:Object) : void
      {
         var _loc2_:int = int(this._currentEffect.indexOf(param1));
         if(_loc2_ != -1)
         {
            this._currentEffect.splice(_loc2_,1);
         }
         if(param1.name == BuffEffect.POSION)
         {
         }
         if(param1.name == BuffEffect.ICE)
         {
         }
         if(param1.name == BuffEffect.ARMOR)
         {
            if(this._sourceRole is Role1)
            {
               ThreeKingdoms._instance._role1.mcState.visible = false;
               Role1._isAddedARMOR = false;
               Role1(this._sourceRole)._properties.setResistance(Role1(this._sourceRole)._properties.getResistance() - Role1(this._sourceRole).getSpecificAttackPowerByType("攻击6"));
            }
            else if(this._sourceRole is Enemy22)
            {
               Enemy22(this._sourceRole)._resistance = Enemy22(this._sourceRole)._resistance - 80;
            }
         }
         if(param1.name == BuffEffect.POWER)
         {
            ThreeKingdoms._instance._role2.mcState.visible = false;
            Role2._isAddedPower = false;
            Role2(this._sourceRole)._properties.setAttackPower(Role2(this._sourceRole)._properties.getAttackPower() - Role2(this._sourceRole).getSpecificAttackPowerByType("加攻"));
         }
         if(param1.name == BuffEffect.BLOOD)
         {
            ThreeKingdoms._instance._role3.mcState.visible = false;
            Role3._isBloodThirsty = false;
         }
      }
      
      private function showArmor() : void
      {
         if(this._counter % 30 == 0)
         {
            if(this._sourceRole is Enemy22)
            {
               Enemy22(this._sourceRole)._resistance = Enemy22(this._sourceRole)._resistance + 80;
            }
         }
      }
      
      public function add(param1:Array) : void
      {
         var _loc3_:Object = null;
         var _loc4_:Object = null;
         var _loc2_:int = 0;
         while(_loc2_ < param1.length)
         {
            _loc3_ = param1[_loc2_];
            if(this._currentEffect.indexOf(_loc3_) == -1)
            {
               _loc3_.isFirstTime = true;
               this._currentEffect.push(_loc3_);
            }
            else
            {
               _loc4_ = this._currentEffect[this._currentEffect.indexOf(_loc3_)];
               _loc4_.time = _loc3_.time;
            }
            if(_loc3_.name == BuffEffect.POSION)
            {
            }
            _loc2_++;
         }
      }
      
      public function init() : void
      {
         if(this._sourceRole is Role1)
         {
            if(Role1._isAddedARMOR)
            {
               Role1._isAddedARMOR = false;
               ThreeKingdoms._instance._role1.mcState.visible = false;
               Role1(this._sourceRole)._properties.setResistance(Role1(this._sourceRole)._properties.getResistance() - Role1(this._sourceRole).getSpecificAttackPowerByType("攻击6"));
            }
         }
         if(this._sourceRole is Role2)
         {
            if(Role2._isAddedPower)
            {
               ThreeKingdoms._instance._role2.mcState.visible = false;
               Role2._isAddedPower = false;
               Role2(this._sourceRole)._properties.setAttackPower(Role2(this._sourceRole)._properties.getAttackPower() - Role2(this._sourceRole).getSpecificAttackPowerByType("加攻"));
            }
         }
         if(this._sourceRole is Role3)
         {
            if(Role3._isBloodThirsty)
            {
               ThreeKingdoms._instance._role3.mcState.visible = false;
               Role3._isBloodThirsty = false;
            }
         }
         this._currentEffect = [];
         this._counter = 0;
         this._beenAttackFatherTotalCount = 0;
         this.hidePoision();
         this.hideIce();
      }
      
      private function hideIce() : void
      {
      }
      
      private function hidePoision() : void
      {
      }
   }
}

