package actors.map
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol686")]
   public dynamic class GoGoGo extends MovieClip
   {
      
      public function GoGoGo()
      {
         super();
         addFrameScript(65,this.frame66);
      }
      
      internal function frame66() : *
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         stop();
      }
   }
}

