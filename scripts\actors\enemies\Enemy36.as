package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   
   public class Enemy36 extends Enemy
   {
      
      public function Enemy36(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 450;
         this._object._alertRange = 450;
         this._object._attackRange = 100;
         this._currentHealthPoint = 30000;
         this._totalHealthPoint = 30000;
         this._resistance = 200;
         this._experience = 650;
         this._attackProbablity = 65;
         this._goldPrice = 600;
         this._walkSpeed = 3.2;
         this._probability = 0.05;
         this._attackBackInfomationDictionary["攻击"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,3],
            "attackInterval":4,
            "attackPower":362 + Math.round(Math.random() * 20),
            "attackType":"physical"
         };
         this._fallEquipmentsList = [{
            "id":93,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":68,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":94,
            "qualityID":[0],
            "type":"equipment"
         }];
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击";
      }
      
      override public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(10))
         {
            wait();
         }
         else if(GameUtility.getRandomNumber(_attackProbablity))
         {
            this.attack();
         }
         else
         {
            moveTowardHero();
         }
      }
      
      override public function attack() : void
      {
         _vx = 0;
         steer();
         this._lastHit = "攻击";
         this.gotoAndStop("攻击");
         setYourDaddysTime(16);
         newAttackID();
      }
   }
}

