package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   
   public class Enemy18 extends Enemy
   {
      
      private var count:int = 300;
      
      public function Enemy18(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 1900;
         this._object._alertRange = 1900;
         this._object._attackRange = 900;
         this._currentHealthPoint = 38000;
         this._totalHealthPoint = 38000;
         this._resistance = 130;
         this._experience = 5000;
         this._attackProbablity = 80;
         this._goldPrice = 5000;
         this._walkSpeed = 7;
         this.isBoss = true;
         this._probability = 0.4;
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[5,5],
            "attackInterval":4,
            "attackPower":(212 + Math.round(Math.random() * 70)) * 1.5,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[5,5],
            "attackInterval":4,
            "attackPower":(212 + Math.round(Math.random() * 70)) * 1.5,
            "attackType":"physical"
         };
         if(ThreeKingdoms._instance._currentLevel == 9)
         {
            isBoss = false;
            this._experience = 5000;
            this._goldPrice = 5000;
            this._attackBackInfomationDictionary["攻击1"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[5,5],
               "attackInterval":4,
               "attackPower":(390 + Math.round(Math.random() * 62)) * 1.5,
               "attackType":"physical"
            };
            this._attackBackInfomationDictionary["攻击2"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[5,5],
               "attackInterval":4,
               "attackPower":(390 + Math.round(Math.random() * 62)) * 1.5,
               "attackType":"physical"
            };
         }
         this.enemyName = "吕布";
         this._fallEquipmentsList = [{
            "id":4,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":82,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":83,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":84,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":65,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":85,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":30,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":19,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":20,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":21,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":22,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":45,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":46,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":47,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":48,
            "qualityID":[0],
            "type":"equipment"
         }];
      }
      
      override public function update() : void
      {
         var _loc1_:Enemy17 = null;
         if(this.count > 0)
         {
            --this.count;
         }
         if(this.count == 0)
         {
            _loc1_ = _game.addEnemy(17,this.x,this.y) as Enemy17;
            _loc1_.setCurrentHealthPoint(_currentHealthPoint);
            this.removeThis();
            return;
         }
         super.update();
      }
      
      public function setCurrentHealthPoint(param1:int) : void
      {
         this._currentHealthPoint = param1;
      }
      
      override public function startAttacking() : void
      {
         if(this._curAttackTarget)
         {
            if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) > 270 && GameUtility.getDistance(this,this._curAttackTarget) < 950)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 180;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) <= 270)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 180;
                  }
                  else
                  {
                     this.attack();
                  }
               }
            }
            else
            {
               moveTowardHero();
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         setYourDaddysTime(77);
         newAttackID();
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(16);
         newAttackID();
      }
      
      override public function isUnderAttack() : Boolean
      {
         return this.currentLabel == "被攻击";
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2";
      }
      
      override public function destroy() : void
      {
         super.destroy();
      }
   }
}

