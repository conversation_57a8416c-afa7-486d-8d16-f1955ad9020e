package actors.menu
{
   import actors.SoundManager;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import util.GameUtility;
   
   public class GameOver extends MovieClip
   {
      
      private var tk:ThreeKingdoms;
      
      public var btnRestart:SimpleButton;
      
      public var btnBackTown:SimpleButton;
      
      public var mcRole:MovieClip;
      
      public function GameOver()
      {
         super();
         this.tk = ThreeKingdoms._instance;
         this.addEventListener(Event.ADDED_TO_STAGE,this._add,false,0,true);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this._remove,false,0,true);
      }
      
      private function _remove(param1:Event) : void
      {
         this.btnRestart.removeEventListener(MouseEvent.CLICK,this.reStart);
         this.btnBackTown.removeEventListener(MouseEvent.CLICK,this.backTown);
      }
      
      private function backTown(param1:MouseEvent) : void
      {
         SoundManager.play("select");
         this.removeChild(this.btnBackTown);
         stage.focus = null;
         if(this.tk._longshot)
         {
            this.tk.removeChild(this.tk._longshot);
            this.tk._longshot = null;
         }
         if(this.tk._game)
         {
            this.tk._game.detory();
            this.tk._game = null;
         }
         if(this.tk._gameInfo)
         {
            this.tk.removeChild(this.tk._gameInfo);
            this.tk._gameInfo = null;
         }
         ThreeKingdoms._gameWorld.destroy();
         this.tk._memory._neeUI = false;
         this.tk._memory.setMemory();
         this.destory();
         this.tk.initGame();
      }
      
      private function reStart(param1:MouseEvent) : void
      {
         ThreeKingdoms._gameWorld.start();
         SoundManager.play("select");
         this.removeChild(this.btnRestart);
         stage.focus = null;
         this.destory();
         ThreeKingdoms._gameWorld.destroy();
         this.tk.startGame(this.tk._currentLevel);
      }
      
      private function destory() : void
      {
         GameUtility.clearDisplayList(this);
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
      
      private function _add(param1:Event) : void
      {
         GameUtility.GC();
         ThreeKingdoms._gameWorld.stop();
         if(this.tk._gameQuality)
         {
            this.tk._gameQuality.visible = false;
         }
         if(this.tk._role1)
         {
            this.mcRole.gotoAndStop("p1");
         }
         if(this.tk._role2)
         {
            this.mcRole.gotoAndStop("p2");
         }
         if(this.tk._role3)
         {
            this.mcRole.gotoAndStop("p3");
         }
         if(Boolean(this.tk._role1) && Boolean(this.tk._role2))
         {
            this.mcRole.gotoAndStop("p4");
         }
         if(Boolean(this.tk._role1) && Boolean(this.tk._role3))
         {
            this.mcRole.gotoAndStop("p5");
         }
         if(Boolean(this.tk._role3) && Boolean(this.tk._role2))
         {
            this.mcRole.gotoAndStop("p6");
         }
         this.btnRestart.addEventListener(MouseEvent.CLICK,this.reStart);
         this.btnBackTown.addEventListener(MouseEvent.CLICK,this.backTown);
      }
   }
}

