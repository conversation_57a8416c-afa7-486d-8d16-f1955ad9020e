package actors.pack
{
   import actors.equipments.Equipment;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import util.GameUtility;
   
   public class ShowObj extends Sprite
   {
      
      private var _icon:MovieClip;
      
      private var _equipment:Equipment;
      
      private var _panel:PropertyInstructionPanel;
      
      public function ShowObj(param1:Equipment)
      {
         super();
         this.name = "item";
         this._equipment = param1;
         this._icon = GameUtility.getLibraryObjectFromSWF("newfallEquipment12.swf","fall_" + param1._id);
         this.addChild(this._icon);
         this._icon.x = 2;
         this._icon.y = 1;
         this._icon.width = 44;
         this._icon.height = 44;
         this._icon.gotoAndStop(1);
         this.graphics.beginFill(16711680,0);
         this.graphics.drawRect(-20,-20,42,42);
         this.graphics.endFill();
         this.buttonMode = true;
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         this.removeEventListener(MouseEvent.ROLL_OUT,this.onRollOutHandler);
         this.removeEventListener(MouseEvent.ROLL_OVER,this.onRollOverHandler);
      }
      
      private function onAddedToStageHandler(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.addEventListener(MouseEvent.ROLL_OVER,this.onRollOverHandler);
         this.addEventListener(MouseEvent.ROLL_OUT,this.onRollOutHandler);
      }
      
      private function onRollOutHandler(param1:MouseEvent) : void
      {
         this.removePanel();
      }
      
      public function removePanel() : void
      {
         if(this.parent.parent.parent && this._panel && this.parent.parent.parent.contains(this._panel))
         {
            this.parent.parent.parent.removeChild(this._panel);
            this._panel = null;
         }
      }
      
      private function onRollOverHandler(param1:MouseEvent) : void
      {
         var _loc2_:Point = null;
         this._panel = new PropertyInstructionPanel(this._equipment);
         if(this.parent.parent.parent)
         {
            _loc2_ = this.localToGlobal(new Point(this.x,this.y));
            this._panel.x = 25 + _loc2_.x;
            this._panel.y = 30 + _loc2_.y;
            this.parent.parent.parent.addChild(this._panel);
         }
         if(this._panel.x + this._panel.width > 960)
         {
            this._panel.x = _loc2_.x - 25 - this._panel.width;
         }
         if(this._panel.x < 0)
         {
            this._panel.x = 10;
         }
         if(this._panel.y + this._panel.height > 600)
         {
            this._panel.y = _loc2_.y - 60 - this._panel.height * 0.5;
         }
         if(this._panel.y < 0)
         {
            this._panel.y = 30;
         }
      }
      
      public function getEquipmentObject() : Equipment
      {
         return this._equipment;
      }
      
      public function getPanel() : PropertyInstructionPanel
      {
         return this._panel;
      }
   }
}

