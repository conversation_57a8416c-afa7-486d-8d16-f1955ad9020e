package
{
   import actors.Hero;
   import adobe.utils.*;
   import flash.accessibility.*;
   import flash.desktop.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.globalization.*;
   import flash.media.*;
   import flash.net.*;
   import flash.net.drm.*;
   import flash.printing.*;
   import flash.profiler.*;
   import flash.sampler.*;
   import flash.sensors.*;
   import flash.system.*;
   import flash.text.*;
   import flash.text.engine.*;
   import flash.text.ime.*;
   import flash.ui.*;
   import flash.utils.*;
   import flash.xml.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol879")]
   public dynamic class Lantern extends MovieClip
   {
      
      public var mcOk:MovieClip;
      
      public var c1:MovieClip;
      
      public var c2:MovieClip;
      
      public var c3:MovieClip;
      
      public var c4:MovieClip;
      
      public var c5:MovieClip;
      
      public var c6:MovieClip;
      
      public var c7:MovieClip;
      
      public var i:int;
      
      public function Lantern()
      {
         super();
         addFrameScript(0,this.frame1);
      }
      
      public function onClick(param1:MouseEvent) : void
      {
         Hero(ThreeKingdoms._gameWorld._heroes[0]).getPlayer()._lanterns[ThreeKingdoms._instance._laFrame - 1] = 1;
         ThreeKingdoms._instance.getDress();
      }
      
      internal function frame1() : *
      {
         this.mcOk["txt"].text = "补领时装";
         this.mcOk.mouseChildren = false;
         this.mcOk.buttonMode = true;
         this.mcOk.addEventListener(MouseEvent.CLICK,this.onClick);
         this.i = 1;
         while(this.i <= 7)
         {
            this["c" + this.i].gotoAndStop(2);
            ++this.i;
         }
      }
   }
}

