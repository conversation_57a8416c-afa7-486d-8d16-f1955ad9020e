package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy3 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy3(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this.isBoss = true;
         this.enemyName = "张梁";
         this._goldPrice = 6 + Math.round(Math.random() * 10);
         this._walkSpeed = 2.5;
         this._object._maxPatrolView = 650;
         this._object._alertRange = 650;
         this._object._attackRange = 300;
         this._currentHealthPoint = 1000;
         this._totalHealthPoint = 1000;
         this._probability = 0.5;
         this._experience = 21;
         this._resistance = 15;
         this._goldPrice = 6 + Math.round(Math.random() * 10);
         this._fallEquipmentsList = [{
            "id":1,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":27,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":7,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":8,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":9,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":10,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":33,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":34,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":35,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":36,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":62,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":70,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":71,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":72,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":73,
            "qualityID":[2],
            "type":"equipment"
         }];
         this._attackProbablity = 50;
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[6,-5],
            "attackInterval":4,
            "attackPower":30,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[15,0],
            "attackInterval":4,
            "attackPower":30,
            "attackType":"magic"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,-7],
            "attackInterval":4,
            "attackPower":30,
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(_skill1CoolDown == 0)
         {
            this.realseSkill1();
            _skill1CoolDown = 150;
         }
         else if(GameUtility.getRandomNumber(10))
         {
            wait();
         }
         else if(GameUtility.getRandomNumber(_attackProbablity))
         {
            if(GameUtility.getDistance(this,this._curAttackTarget) <= 180)
            {
               steer();
               _vx = 0;
               this._lastHit = "攻击3";
               this.gotoAndStop("攻击3");
               newAttackID();
            }
            else
            {
               steer();
               _vx = 0;
               this.gotoAndStop("攻击2");
               this._lastHit = "攻击2";
               this.newAttackID();
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3";
      }
      
      override public function destroy() : void
      {
         super.destroy();
      }
   }
}

