package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol362")]
   public dynamic class Hint extends MovieClip
   {
      
      public function Hint()
      {
         super();
         addFrameScript(29,this.frame30);
      }
      
      internal function frame30() : *
      {
         if(this.parent)
         {
            MovieClip(this.parent).removeChild(this);
         }
      }
   }
}

