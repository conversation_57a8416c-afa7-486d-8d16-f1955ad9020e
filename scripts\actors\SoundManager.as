package actors
{
   import flash.media.Sound;
   import flash.media.SoundChannel;
   import flash.media.SoundMixer;
   import flash.media.SoundTransform;
   import util.GameUtility;
   
   public class SoundManager
   {
      
      private static var loopChannel:SoundChannel;
      
      private static var begin:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","start");
      
      private static var stage1:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","bg1");
      
      private static var stage2:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","bg2");
      
      private static var stage3:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","bg3");
      
      private static var stage4:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","bg4");
      
      private static var stage5:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","bg5");
      
      private static var over:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","over");
      
      private static var town:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","bgTown");
      
      private static var BeattackByRole1:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","BeattackByRole1");
      
      private static var BeattackByRole2:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","BeattackByRole2");
      
      private static var Role1_hit1:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit1");
      
      private static var Role1_hit2:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit2");
      
      private static var Role1_hit3:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit3");
      
      private static var Role1_hit4:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit4");
      
      private static var Role1_hit5:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit5");
      
      private static var Role1_hit6:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit6");
      
      private static var Role1_hit7:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit7");
      
      private static var Role1_hit8:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit8");
      
      private static var Role1_hit9_1:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit9_1");
      
      private static var Role1_hit9_2:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit9_2");
      
      private static var Role1_hit9_3:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role1_hit9_3");
      
      private static var Role2_hit1:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit1");
      
      private static var Role2_hit0:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit0");
      
      private static var Role2_hit2:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit2");
      
      private static var Role2_hit3:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit3");
      
      private static var Role2_hit4:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit4");
      
      private static var Role2_hit5:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit5");
      
      private static var Role2_hit6:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit6");
      
      private static var Role2_hit7:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit7");
      
      private static var Role2_hit8:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit8");
      
      private static var Role3_hit1:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role3_hit1");
      
      private static var Role3_hit0:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role3_hit1");
      
      private static var Role3_hit2:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role3_hit1");
      
      private static var Role3_hit3:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role3_hit3");
      
      private static var Role3_hit4:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role3_hit4");
      
      private static var Role3_hit5:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role3_hit4");
      
      private static var Role3_hit6:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role3_hit6");
      
      private static var Role3_hit7:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role3_hit7");
      
      private static var Role3_hit8:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role3_hit8");
      
      private static var Role3_hit9:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role3_hit9");
      
      private static var Role2_hit9_1:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit9_1");
      
      private static var Role2_hit9_2:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit9_2");
      
      private static var Role2_hit9_3:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit9_3");
      
      private static var Role2_hit9_4:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","Role2_hit9_4");
      
      private static var Role1BeHit:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","role1BeHit");
      
      private static var Role2BeHit:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","role2BeHit");
      
      private static var select:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","select");
      
      private static var levelUp:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","levelUp");
      
      private static var learnSkill:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","learnSkill");
      
      private static var gogogo:Sound = GameUtility.getLibraryObjectFromSWF("newmusic.swf","gogogo");
      
      private static var playing:String = "";
      
      public static var soundStay:Boolean = true;
      
      public function SoundManager()
      {
         super();
      }
      
      public static function play(param1:String) : void
      {
         switch(param1)
         {
            case "select":
               select.play();
               break;
            case "levelUp":
               levelUp.play();
               break;
            case "learnSkill":
               learnSkill.play();
               break;
            case "gogogo":
               gogogo.play();
               break;
            case "Role1_hit1":
               Role1_hit1.play();
               break;
            case "Role1_hit2":
               Role1_hit2.play();
               break;
            case "Role1_hit3":
               Role1_hit3.play();
               break;
            case "Role1_hit4":
               Role1_hit4.play();
               break;
            case "Role1_hit6":
               Role1_hit6.play();
               break;
            case "Role1_hit7":
               Role1_hit7.play();
               break;
            case "Role1_hit5":
               Role1_hit5.play();
               break;
            case "Role1_hit8":
               Role1_hit8.play();
               break;
            case "Role1_hit9_1":
               Role1_hit9_1.play();
               break;
            case "Role1_hit9_2":
               Role1_hit9_2.play();
               break;
            case "Role1_hit9_3":
               Role1_hit9_3.play();
               break;
            case "Role2_hit1":
               Role2_hit1.play();
               break;
            case "Role2_hit0":
               Role2_hit0.play();
               break;
            case "Role2_hit2":
               Role2_hit2.play();
               break;
            case "Role2_hit3":
               Role2_hit3.play();
               break;
            case "Role2_hit4":
               Role2_hit4.play();
               break;
            case "Role2_hit6":
               Role2_hit6.play();
               break;
            case "Role2_hit7":
               Role2_hit7.play();
               break;
            case "Role2_hit5":
               Role2_hit5.play();
               break;
            case "Role2_hit8":
               Role2_hit8.play();
               break;
            case "Role2_hit9_1":
               Role2_hit9_1.play();
               break;
            case "Role2_hit9_2":
               Role2_hit9_2.play();
               break;
            case "Role2_hit9_3":
               Role2_hit9_3.play();
               break;
            case "Role2_hit9_4":
               Role2_hit9_4.play();
               break;
            case "Role1BeHit":
               Role1BeHit.play();
               break;
            case "Role2BeHit":
               Role2BeHit.play();
               break;
            case "Role3_hit1":
               Role3_hit1.play();
               break;
            case "Role3_hit0":
               Role3_hit0.play();
               break;
            case "Role3_hit2":
               Role3_hit2.play();
               break;
            case "Role3_hit3":
               Role3_hit3.play();
               break;
            case "Role3_hit4":
               Role3_hit4.play();
               break;
            case "Role3_hit6":
               Role3_hit6.play();
               break;
            case "Role3_hit7":
               Role3_hit7.play();
               break;
            case "Role3_hit5":
               Role3_hit5.play();
               break;
            case "Role3_hit8":
               Role3_hit8.play();
               break;
            case "Role3_hit9":
               Role3_hit9.play();
               break;
            case "BeattackByRole1":
               BeattackByRole1.play();
               break;
            case "BeattackByRole2":
               BeattackByRole2.play();
               break;
            case "begin":
               if(playing != "begin")
               {
                  playing = "begin";
                  if(loopChannel)
                  {
                     loopChannel.stop();
                  }
                  loopChannel = begin.play(0,9999);
                  setVom(loopChannel);
               }
               break;
            case "over":
               if(playing != "over")
               {
                  playing = "over";
                  if(loopChannel)
                  {
                     loopChannel.stop();
                  }
                  loopChannel = over.play(0,1);
                  setVom(loopChannel);
               }
               break;
            case "stage1":
               if(playing != "stage1")
               {
                  playing = "stage1";
                  if(loopChannel)
                  {
                     loopChannel.stop();
                  }
                  loopChannel = stage1.play(0,9999);
                  setVom(loopChannel);
               }
               break;
            case "stage2":
               if(playing != "stage2")
               {
                  playing = "stage2";
                  if(loopChannel)
                  {
                     loopChannel.stop();
                  }
                  loopChannel = stage2.play(0,9999);
                  setVom(loopChannel);
               }
               break;
            case "stage3":
               if(playing != "stage3")
               {
                  playing = "stage3";
                  if(loopChannel)
                  {
                     loopChannel.stop();
                  }
                  loopChannel = stage3.play(0,9999);
                  setVom(loopChannel);
               }
               break;
            case "stage4":
               if(playing != "stage4")
               {
                  playing = "stage4";
                  if(loopChannel)
                  {
                     loopChannel.stop();
                  }
                  loopChannel = stage4.play(0,9999);
                  setVom(loopChannel);
               }
               break;
            case "stage5":
               if(playing != "stage5")
               {
                  playing = "stage5";
                  if(loopChannel)
                  {
                     loopChannel.stop();
                  }
                  loopChannel = stage5.play(0,9999);
                  setVom(loopChannel);
               }
               break;
            case "town":
               if(playing != "town")
               {
                  playing = "town";
                  if(loopChannel)
                  {
                     loopChannel.stop();
                  }
                  loopChannel = town.play(0,9999);
                  setVom(loopChannel);
               }
         }
      }
      
      public static function setVom(param1:SoundChannel) : void
      {
         var _loc2_:SoundTransform = param1.soundTransform;
         _loc2_.volume = 0.5;
         param1.soundTransform = _loc2_;
      }
      
      public static function controlSound() : *
      {
         if(soundStay)
         {
            soundStay = false;
            SoundMixer.soundTransform = new SoundTransform(0);
         }
         else
         {
            soundStay = true;
            SoundMixer.soundTransform = new SoundTransform(1);
         }
      }
   }
}

