package actors.holidays
{
   import actors.Hero;
   import actors.equipments.Equipment;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import flash.text.TextField;
   import util.GameUtility;
   
   public dynamic class GetChildrenGift extends MovieClip
   {
      
      private var urlloader:URLLoader;
      
      public var exCodeTxt:TextField;
      
      public var btnExchange:SimpleButton;
      
      public var btnClose:SimpleButton;
      
      public var mcShow:MovieClip;
      
      public var mcShow2:MovieClip;
      
      public function GetChildrenGift()
      {
         super();
         addFrameScript(0,this.frame1);
         if(ThreeKingdoms._instance._gameMode == 2)
         {
            this.gotoAndStop(2);
         }
         else
         {
            this.gotoAndStop(1);
         }
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         this.btnClose.addEventListener(MouseEvent.CLICK,this.onCloseHandler);
         this.btnExchange.addEventListener(MouseEvent.CLICK,this.onExchangeHandler);
      }
      
      private function onExchangeHandler(param1:MouseEvent) : void
      {
         var _loc4_:MovieClip = null;
         if(_loc4_ == null)
         {
            _loc4_ = GameUtility.getObject("getGift");
            _loc4_.x = 488.3;
            _loc4_.y = 298;
         }
         if(Hero(ThreeKingdoms._gameWorld._heroes[0]).getPlayer()._equipmentsVector.length >= 30)
         {
            _loc4_["mc"]["txt"].text = "玩家1背包已满,请卖出部分装备。";
            ThreeKingdoms._instance._town.addChild(_loc4_);
            return;
         }
         if(ThreeKingdoms._instance._gameMode == 2)
         {
            if(Hero(ThreeKingdoms._gameWorld._heroes[1]).getPlayer()._equipmentsVector.length >= 30)
            {
               _loc4_["mc"]["txt"].text = "玩家2背包已满,请卖出部分装备。";
               ThreeKingdoms._instance._town.addChild(_loc4_);
               return;
            }
         }
         this.urlloader = new URLLoader();
         var _loc2_:URLRequest = new URLRequest("http://huodong2.4399.com/2013/ertongjie/api.php?");
         var _loc3_:URLVariables = new URLVariables();
         _loc3_.game = "sgxz";
         _loc3_.code = String(this.exCodeTxt.text);
         _loc3_.randomNum = String(Math.random() * 100000);
         _loc2_.data = _loc3_;
         _loc2_.method = URLRequestMethod.GET;
         this.urlloader.addEventListener(Event.COMPLETE,this.onLoadDataCompleteHandler);
         this.urlloader.load(_loc2_);
      }
      
      private function onLoadDataCompleteHandler(param1:Event) : void
      {
         var _loc4_:Equipment = null;
         this.urlloader.removeEventListener(Event.COMPLETE,this.onLoadDataCompleteHandler);
         var _loc2_:String = param1.target.data;
         var _loc3_:MovieClip = GameUtility.getObject("getGift");
         switch(_loc2_)
         {
            case "005":
               _loc3_["mc"]["txt"].text = "传递参数不合法";
               if(!this.urlloader.hasEventListener(Event.COMPLETE))
               {
                  this.urlloader.addEventListener(Event.COMPLETE,this.onLoadDataCompleteHandler);
               }
               break;
            case "004":
               _loc3_["mc"]["txt"].text = "激活码格式有误";
               if(!this.urlloader.hasEventListener(Event.COMPLETE))
               {
                  this.urlloader.addEventListener(Event.COMPLETE,this.onLoadDataCompleteHandler);
               }
               break;
            case "003":
               _loc3_["mc"]["txt"].text = "未登陆，请先登录";
               if(!this.urlloader.hasEventListener(Event.COMPLETE))
               {
                  this.urlloader.addEventListener(Event.COMPLETE,this.onLoadDataCompleteHandler);
               }
               break;
            case "002":
               _loc3_["mc"]["txt"].text = "输入的激活码不正确";
               if(!this.urlloader.hasEventListener(Event.COMPLETE))
               {
                  this.urlloader.addEventListener(Event.COMPLETE,this.onLoadDataCompleteHandler);
               }
               break;
            case "1":
               if(ThreeKingdoms._instance._user1._isGet2013ChildrenGift)
               {
                  break;
               }
               _loc4_ = ThreeKingdoms._instance._equipments.getEquipmentByID(200) as Equipment;
               Hero(ThreeKingdoms._gameWorld._heroes[0]).getPlayer()._equipmentsVector.push(_loc4_);
               if(ThreeKingdoms._instance._gameMode == 2)
               {
                  Hero(ThreeKingdoms._gameWorld._heroes[1]).getPlayer()._equipmentsVector.push(_loc4_);
               }
               ThreeKingdoms._instance._user1._isGet2013ChildrenGift = true;
               ThreeKingdoms._instance._memory._neeUI = false;
               ThreeKingdoms._instance._memory.setMemory();
               HolidayManager.getInstance().destory();
               if(this.parent)
               {
                  this.parent.removeChild(this);
               }
               _loc3_["mc"]["txt"].text = "领取成功,请到背包查看";
               break;
         }
         _loc3_.x = 488.3;
         _loc3_.y = 298;
         ThreeKingdoms._instance._town.addChild(_loc3_);
      }
      
      private function onCloseHandler(param1:MouseEvent) : void
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         var _loc2_:DisplayObject = null;
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         if(this.urlloader.hasEventListener(Event.COMPLETE))
         {
            this.urlloader.removeEventListener(Event.COMPLETE,this.onLoadDataCompleteHandler);
         }
         this.stop();
         this.btnClose.removeEventListener(MouseEvent.CLICK,this.onCloseHandler);
         this.btnExchange.removeEventListener(MouseEvent.CLICK,this.onExchangeHandler);
         while(this.numChildren > 0)
         {
            _loc2_ = this.removeChildAt(0);
            _loc2_ = null;
         }
      }
      
      internal function frame1() : *
      {
         stop();
      }
   }
}

