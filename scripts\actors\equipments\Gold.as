package actors.equipments
{
   import actors.equipments.pharmaceutical.LesserHealingSalve;
   
   public class Gold extends LesserHealingSalve
   {
      
      private var _price:Number = 10;
      
      public function Gold()
      {
         super();
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override protected function cureHero() : void
      {
         _owner._properties.setGold(this.price);
      }
      
      override public function remove() : void
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         var _loc1_:int = int(ThreeKingdoms._gameWorld._golds.indexOf(this));
         if(_loc1_ != -1)
         {
            ThreeKingdoms._gameWorld._golds.splice(_loc1_,1);
            delete global[this];
         }
      }
      
      public function get price() : Number
      {
         return this._price;
      }
      
      public function set price(param1:Number) : void
      {
         this._price = param1;
      }
   }
}

