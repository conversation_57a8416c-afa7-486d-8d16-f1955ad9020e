package actors.missles
{
   import actors.Enemy;
   import actors.Hero;
   import actors.Missle;
   import flash.display.MovieClip;
   import util.HitTest;
   
   public class Enemy22M<PERSON>le extends Missle
   {
      
      public var sword:MovieClip;
      
      public function Enemy22Missle(param1:Enemy = null, param2:Hero = null)
      {
         super(param1,param2);
      }
      
      override public function detectCollision() : void
      {
         var _loc1_:Object = null;
         if(this._hero.isDead())
         {
            return;
         }
         if(ThreeKingdoms._instance._protectedProperty.getProperty(_hero,"_whosYourDaddy"))
         {
            return;
         }
         if(HitTest.complexHitTestObject(this,this._hero))
         {
            this.gotoAndPlay(81);
            _loc1_ = this._enemy._attackBackInfomationDictionary[_enemy._lastHit];
            _hero.afterAttack(this._enemy,_loc1_);
            this.destroy();
         }
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function destroy() : void
      {
         super.destroy();
      }
   }
}

