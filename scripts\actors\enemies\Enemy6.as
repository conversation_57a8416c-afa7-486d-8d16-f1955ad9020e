package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy6 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy6(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._walkSpeed = 3;
         this._object._maxPatrolView = 450;
         this._object._alertRange = 450;
         this._object._attackRange = 200;
         this._currentHealthPoint = 980;
         this._totalHealthPoint = 980;
         this._resistance = 15;
         this._experience = 97;
         this._probability = 0.3;
         this._goldPrice = 8 + Math.round(Math.random() * 12);
         this._attackProbablity = 30;
         if(ThreeKingdoms._instance._currentLevel == 2)
         {
            this._currentHealthPoint = 1500;
            this._totalHealthPoint = 1500;
            this._probability = 0.6;
            this._experience = 80;
            this._attackProbablity = 20;
            this._resistance = 15;
            this._goldPrice = 8 + Math.round(Math.random() * 12);
            this._fallEquipmentsList = [{
               "id":7,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":8,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":9,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":10,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":33,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":34,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":35,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":36,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":1,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":70,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":71,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":72,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":73,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":62,
               "qualityID":[1],
               "type":"equipment"
            }];
         }
         else if(ThreeKingdoms._instance._currentLevel == 3)
         {
            this._fallEquipmentsList = [{
               "id":11,
               "qualityID":[0,1],
               "type":"equipment"
            },{
               "id":14,
               "qualityID":[0,1],
               "type":"equipment"
            },{
               "id":37,
               "qualityID":[0,1],
               "type":"equipment"
            },{
               "id":40,
               "qualityID":[0,1],
               "type":"equipment"
            },{
               "id":74,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":77,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":27,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":1,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":62,
               "qualityID":[1],
               "type":"equipment"
            }];
         }
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,2],
            "attackInterval":4,
            "attackPower":46 + Math.random() * 10,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,3],
            "attackInterval":4,
            "attackPower":46 + Math.random() * 10,
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(10))
         {
            wait();
         }
         else if(GameUtility.getRandomNumber(_attackProbablity))
         {
            if(GameUtility.getDistance(this,this._curAttackTarget) <= 130)
            {
               steer();
               _vx = 0;
               setYourDaddysTime(12);
               this._lastHit = "攻击1";
               this.gotoAndStop("攻击1");
               newAttackID();
            }
            else
            {
               steer();
               _vx = 0;
               setYourDaddysTime(12);
               this._lastHit = "攻击2";
               this.gotoAndStop("攻击2");
               this.newAttackID();
            }
         }
         else
         {
            moveTowardHero();
         }
      }
   }
}

