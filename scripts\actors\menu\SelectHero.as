package actors.menu
{
   import actors.SoundManager;
   import actors.user.User;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import util.GameUtility;
   import util.KeyList;
   
   public class SelectHero extends MovieClip
   {
      
      public var liuBeiMc:MovieClip;
      
      public var guanYuMc:MovieClip;
      
      public var zhangFeiMc:MovieClip;
      
      public var button1:MovieClip;
      
      public var button2:MovieClip;
      
      public var button3:MovieClip;
      
      public var btnBack:SimpleButton;
      
      public var tk:ThreeKingdoms;
      
      public var _currentSelection:int = 1;
      
      public var _SaveMc:MovieClip;
      
      private var _afterClick:Boolean = false;
      
      private var _gameMenu:GameMenu;
      
      public function SelectHero()
      {
         super();
         this.tk = ThreeKingdoms._instance;
         this.liuBeiMc.gotoAndStop(1);
         this.guanYuMc.gotoAndStop(1);
         this.zhangFeiMc.gotoAndStop(1);
         this.button1.gotoAndStop(1);
         this.button2.gotoAndStop(1);
         this.button3.gotoAndStop(1);
         this.guanYuMc.mouseEnabled = false;
         this.guanYuMc.mouseChildren = false;
         this.liuBeiMc.mouseChildren = false;
         this.liuBeiMc.mouseEnabled = false;
         this.zhangFeiMc.mouseEnabled = false;
         this.zhangFeiMc.mouseChildren = false;
         this.button1.buttonMode = true;
         this.button2.buttonMode = true;
         this.button3.buttonMode = true;
         this.button1.addEventListener(MouseEvent.CLICK,this.onClickHandler);
         this.button1.addEventListener(MouseEvent.MOUSE_OVER,this.onMouseOverHandler);
         this.button1.addEventListener(MouseEvent.MOUSE_OUT,this.onMouseOutHandler);
         this.button2.addEventListener(MouseEvent.CLICK,this.onClickHandler);
         this.button2.addEventListener(MouseEvent.MOUSE_OVER,this.onMouseOverHandler);
         this.button2.addEventListener(MouseEvent.MOUSE_OUT,this.onMouseOutHandler);
         this.button3.addEventListener(MouseEvent.CLICK,this.onClickHandler);
         this.button3.addEventListener(MouseEvent.MOUSE_OVER,this.onMouseOverHandler);
         this.button3.addEventListener(MouseEvent.MOUSE_OUT,this.onMouseOutHandler);
         this.btnBack.addEventListener(MouseEvent.CLICK,this.onBtnBackClickHandler);
      }
      
      private function onMouseOutHandler(param1:MouseEvent) : void
      {
         if(param1.currentTarget.name == "button1")
         {
            param1.currentTarget.gotoAndStop(1);
            this.liuBeiMc.gotoAndStop(1);
            this.zhangFeiMc.gotoAndStop(1);
         }
         else if(param1.currentTarget.name == "button2")
         {
            param1.currentTarget.gotoAndStop(1);
            this.guanYuMc.gotoAndStop(1);
            this.zhangFeiMc.gotoAndStop(1);
         }
         else if(param1.currentTarget.name == "button3")
         {
            param1.currentTarget.gotoAndStop(1);
            this.guanYuMc.gotoAndStop(1);
            this.liuBeiMc.gotoAndStop(1);
         }
         if(this.getChildByName("index" + this._currentSelection))
         {
            this.removeChild(this.getChildByName("index" + this._currentSelection));
         }
      }
      
      private function onMouseOverHandler(param1:MouseEvent) : void
      {
         if(param1.currentTarget.name == "button1")
         {
            param1.currentTarget.gotoAndStop(2);
            this.liuBeiMc.play();
         }
         else if(param1.currentTarget.name == "button2")
         {
            param1.currentTarget.gotoAndStop(2);
            this.guanYuMc.play();
         }
         else if(param1.currentTarget.name == "button3")
         {
            param1.currentTarget.gotoAndStop(2);
            this.zhangFeiMc.play();
         }
         var _loc2_:* = GameUtility.getImageObject(this._currentSelection + "P");
         _loc2_.name = "index" + this._currentSelection;
         _loc2_.x = param1.currentTarget.x + 120;
         _loc2_.y = 100;
         this.addChild(_loc2_);
      }
      
      private function savePrompt() : void
      {
         this._SaveMc = GameUtility.getObject("SaveIt") as MovieClip;
         this._SaveMc.x = 350;
         this._SaveMc.y = 80;
         this._SaveMc.name = "SaveMc";
         this.tk.addChild(this._SaveMc);
      }
      
      private function onClickHandler(param1:MouseEvent) : void
      {
         param1.currentTarget.removeEventListener(MouseEvent.MOUSE_OVER,this.onMouseOverHandler);
         param1.currentTarget.removeEventListener(MouseEvent.MOUSE_OUT,this.onMouseOutHandler);
         param1.currentTarget.removeEventListener(MouseEvent.CLICK,this.onClickHandler);
         SoundManager.play("select");
         var _loc2_:uint = uint(String(param1.currentTarget.name).substr(6,1));
         if(this.tk._gameMode == 1)
         {
            this.newHero(_loc2_);
            this.parent.removeChild(this);
            this.tk._memory._neeUI = true;
            this.tk._memory.setMemory();
            this.tk.initGame();
            this.savePrompt();
            ThreeKingdoms._gameWorld.pauseGame();
         }
         else if(this.tk._gameMode == 2)
         {
            if(param1.currentTarget.name == "button1")
            {
               param1.currentTarget.gotoAndStop(2);
               this.newHero(1);
            }
            else if(param1.currentTarget.name == "button2")
            {
               param1.currentTarget.gotoAndStop(2);
               this.guanYuMc.gotoAndStop(1);
               this.newHero(2);
            }
            else if(param1.currentTarget.name == "button3")
            {
               param1.currentTarget.gotoAndStop(2);
               this.newHero(3);
            }
            if(this._currentSelection == 2)
            {
               if(Boolean(this.parent) && this._currentSelection == 2)
               {
                  this.parent.removeChild(this);
               }
               this.tk._memory._neeUI = true;
               this.tk._memory.setMemory();
               this.tk.initGame();
               this.savePrompt();
               ThreeKingdoms._gameWorld.pauseGame();
            }
         }
         ++this._currentSelection;
      }
      
      private function onBtnBackClickHandler(param1:MouseEvent) : void
      {
         ThreeKingdoms._gameWorld.destroy();
         this.tk._role1 = null;
         this.tk._role2 = null;
         this.tk._role3 = null;
         this.tk._user1 = new User();
         this.tk._user2 = new User();
         this.parent.removeChild(this);
      }
      
      public function newHero(param1:int) : void
      {
         if(param1 == 1)
         {
            this.tk._role1 = GameUtility.getObject("actors.roles.Role" + 1);
            ThreeKingdoms._gameWorld.addHero(this.tk._role1);
            this.tk._role1.x = 300;
            this.tk._role1.y = 100;
            if(this._currentSelection == 1)
            {
               this.tk._role1.setPlayer(this.tk._user1);
               this.tk._user1._roleID = 1;
               this.tk._user1.hero = this.tk._role1;
               this.tk._role1.setKeyList(KeyList._keyList1);
            }
            else
            {
               this.tk._role1.setPlayer(this.tk._user2);
               this.tk._user2._roleID = 1;
               this.tk._user2.hero = this.tk._role1;
               this.tk._role1.setKeyList(KeyList._keyList2);
            }
         }
         else if(param1 == 2)
         {
            this.tk._role2 = GameUtility.getObject("actors.roles.Role" + 2);
            ThreeKingdoms._gameWorld.addHero(this.tk._role2);
            this.tk._role2.x = 400;
            this.tk._role2.y = 100;
            if(this._currentSelection == 1)
            {
               this.tk._role2.setPlayer(this.tk._user1);
               this.tk._user1.hero = this.tk._role2;
               this.tk._user1._roleID = 2;
               this.tk._role2.setKeyList(KeyList._keyList1);
            }
            else
            {
               this.tk._role2.setPlayer(this.tk._user2);
               this.tk._user2.hero = this.tk._role2;
               this.tk._user2._roleID = 2;
               this.tk._role2.setKeyList(KeyList._keyList2);
            }
         }
         else if(param1 == 3)
         {
            this.tk._role3 = GameUtility.getObject("actors.roles.Role" + 3);
            ThreeKingdoms._gameWorld.addHero(this.tk._role3);
            this.tk._role3.x = 400;
            this.tk._role3.y = 100;
            if(this._currentSelection == 1)
            {
               this.tk._role3.setPlayer(this.tk._user1);
               this.tk._user1.hero = this.tk._role3;
               this.tk._user1._roleID = 3;
               this.tk._role3.setKeyList(KeyList._keyList1);
            }
            else
            {
               this.tk._role3.setPlayer(this.tk._user2);
               this.tk._user2.hero = this.tk._role3;
               this.tk._user2._roleID = 3;
               this.tk._role3.setKeyList(KeyList._keyList2);
            }
         }
      }
   }
}

