package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy16 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy16(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 400;
         this._object._alertPatrolView = 400;
         this._object._attackRange = 160;
         this._currentHealthPoint = 18000;
         this._totalHealthPoint = 18000;
         this._goldPrice = 450;
         this._experience = 800;
         this._attackProbablity = 40;
         this._resistance = 82;
         this._probability = 0.5;
         this._fallEquipmentsList = [{
            "id":78,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":79,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":80,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":81,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":65,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":15,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":16,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":17,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":18,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":41,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":42,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":43,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":44,
            "qualityID":[1],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2.5,-3],
            "attackInterval":4,
            "attackPower":160 + int(Math.random() * 30),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,-3],
            "attackInterval":4,
            "attackPower":160 + int(Math.random() * 30),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(30))
         {
            wait();
         }
         else if(GameUtility.getRandomNumber(_attackProbablity) && GameUtility.getDistance(this,this._curAttackTarget) <= this._object._attackRange)
         {
            if(_skill1CoolDown == 0 && GameUtility.getDistance(this,this._curAttackTarget) <= 120)
            {
               this.realseSkill1();
               _skill1CoolDown = 120;
            }
            else
            {
               this.attack();
            }
         }
         else
         {
            moveTowardHero();
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(26);
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         newAttackID();
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(29);
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2";
      }
   }
}

