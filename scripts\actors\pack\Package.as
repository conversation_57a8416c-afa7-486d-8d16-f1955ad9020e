package actors.pack
{
   import actors.Hero;
   import actors.equipments.Equipment;
   import actors.pack.petinventory.PetInventory;
   import actors.roles.Role1;
   import actors.roles.Role2;
   import actors.roles.Role3;
   import actors.user.User;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import util.GameUtility;
   
   public class Package extends MovieClip
   {
      
      public var mcRole:MovieClip;
      
      public var txtRoleName:TextField;
      
      public var txtLevel:TextField;
      
      public var txtManaPoint:TextField;
      
      public var txtAttackPower:TextField;
      
      public var txtAddAttackPower:TextField;
      
      public var txtMiss:TextField;
      
      public var txtAddMiss:TextField;
      
      public var txtResistance:TextField;
      
      public var txtAddResistance:TextField;
      
      public var txtCrit:TextField;
      
      public var txtAddCrit:TextField;
      
      public var txtMoveSpeed:TextField;
      
      public var txtGloryValue:TextField;
      
      public var txtGold:TextField;
      
      public var txtHealthPoint:TextField;
      
      public var btnRefine:SimpleButton;
      
      public var btnClose:SimpleButton;
      
      public var btnPet:SimpleButton;
      
      public var mcHideSuit:MovieClip;
      
      public var btnEquipment:SimpleButton;
      
      public var btnSpecial:SimpleButton;
      
      public var btnRepository:SimpleButton;
      
      public var currentEquipmentsPanel:CurrentEquipmentsPanel;
      
      public var btnSellAll:SimpleButton;
      
      public var mcSellAll:MovieClip;
      
      public var mcChange:MovieClip;
      
      private var _packageIndex:uint = 1;
      
      private var _role:Hero;
      
      private var _packageContentArray:Array = [];
      
      private var _panel:Sprite;
      
      public var _isEquipmentsPanel:Boolean = false;
      
      public var _isRepositoryPanel:Boolean = false;
      
      public var _isSpecialPanel:Boolean = false;
      
      public var _petPack:PetInventory;
      
      private var _isSuit:Boolean = false;
      
      public function Package()
      {
         super();
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         setChildIndex(this.currentEquipmentsPanel,numChildren - 1);
         addChild(this.mcRole);
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      private function onAddedToStageHandler(param1:Event) : void
      {
         var _loc4_:Equipment = null;
         GameUtility.GC();
         if(ThreeKingdoms._instance._gameQuality)
         {
            ThreeKingdoms._instance._gameQuality.visible = false;
         }
         if(this._role.getPlayer()._isHideSuit)
         {
            this.mcHideSuit.gotoAndStop(2);
         }
         else
         {
            removeEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         }
         this.mcHideSuit.buttonMode = true;
         this.mcSellAll.visible = false;
         this.mcChange.visible = false;
         this.txtRoleName.text = ThreeKingdoms._instance._userName;
         this.mcRole.gotoAndStop(this._packageIndex);
         this.showCurrentEquipment();
         this.setInformationText();
         this.mcHideSuit.addEventListener(MouseEvent.CLICK,this.onHideSuitHandler);
         this.btnSellAll.addEventListener(MouseEvent.CLICK,this.onSellAllHandler);
         this.btnClose.addEventListener(MouseEvent.CLICK,this.onCloseHandler);
         this.btnEquipment.addEventListener(MouseEvent.CLICK,this.onSelectTypeHandler);
         this.btnRepository.addEventListener(MouseEvent.CLICK,this.onSelectTypeHandler);
         this.btnSpecial.addEventListener(MouseEvent.CLICK,this.onSelectTypeHandler);
         this.btnPet.addEventListener(MouseEvent.CLICK,this.openPetPackHandler);
         if(Boolean(this._panel) && contains(this._panel))
         {
            this.removeChild(this._panel);
            this._panel = null;
         }
         this._panel = new Sprite();
         this._panel.x = 455;
         this._panel.y = 140;
         this._panel.name = "equipments";
         this.addChild(this._panel);
         this.drawGrid(this._role.getPlayer()._equipmentsVector);
         var _loc2_:Vector.<Object> = User(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer())._specialVector;
         User(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer())._petFoodVector = new Vector.<Object>();
         User(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer())._roseVector = new Vector.<Object>();
         User(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer())._chocolVector = new Vector.<Object>();
         var _loc3_:* = 0;
         while(_loc3_ < _loc2_.length)
         {
            _loc4_ = _loc2_[_loc3_] as Equipment;
            if(_loc4_._type == "special")
            {
               User(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer())._petFoodVector.push(_loc4_);
            }
            if(_loc4_._id == 97)
            {
               User(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer())._roseVector.push(_loc4_);
            }
            if(_loc4_._id == 96)
            {
               User(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer())._chocolVector.push(_loc4_);
            }
            _loc3_++;
         }
         ThreeKingdoms._gameWorld.pauseGame();
      }
      
      private function onHideSuitHandler(param1:MouseEvent) : void
      {
         if(this.mcHideSuit.currentFrame == 1)
         {
            this.mcHideSuit.gotoAndStop(2);
            this._role.getPlayer()._isHideSuit = true;
         }
         else
         {
            this.mcHideSuit.gotoAndStop(1);
            this._role.getPlayer()._isHideSuit = false;
         }
         var _loc2_:Object = this._role.getPlayer().getEquipmentID();
         this._role.changeEquipment(_loc2_);
      }
      
      private function openPetPackHandler(param1:MouseEvent) : void
      {
         var _loc2_:* = new PetInventory(this._role);
         this.addChild(_loc2_);
      }
      
      private function onSellAllHandler(param1:MouseEvent) : void
      {
         this.mcSellAll.visible = true;
         this.addChild(this.mcSellAll);
         this.mcSellAll["btnOk"].addEventListener(MouseEvent.CLICK,this.sellAll);
         this.mcSellAll["btnNo"].addEventListener(MouseEvent.CLICK,this.cancelSellAllHandler);
      }
      
      private function sellAll(param1:MouseEvent) : void
      {
         var _loc2_:Equipment = null;
         var _loc3_:Vector.<Object> = null;
         var _loc4_:int = 0;
         var _loc5_:Vector.<Object> = null;
         var _loc6_:Vector.<Object> = null;
         this.mcSellAll["btnOk"].removeEventListener(MouseEvent.CLICK,this.onSellAllHandler);
         this.mcSellAll["btnNo"].removeEventListener(MouseEvent.CLICK,this.cancelSellAllHandler);
         this.mcSellAll.visible = false;
         if(this._panel.name == "equipments")
         {
            _loc3_ = ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer()._equipmentsVector;
            _loc4_ = 0;
            while(_loc4_ < _loc3_.length)
            {
               _loc2_ = _loc3_[_loc4_] as Equipment;
               if(_loc2_._worth[_loc2_.index] == null || _loc2_._worth[_loc2_.index] == undefined)
               {
                  Hero(ThreeKingdoms._instance["_role" + this._packageIndex])._properties.setGold(0);
               }
               else if(Boolean(_loc2_._suitProperty) && Boolean(_loc2_._suitProperty.isNew))
               {
                  Hero(ThreeKingdoms._instance["_role" + this._packageIndex])._properties.setGold(Math.round((_loc2_._worth[_loc2_.index] - 0.1) * 10));
               }
               else
               {
                  Hero(ThreeKingdoms._instance["_role" + this._packageIndex])._properties.setGold(_loc2_._worth[_loc2_.index]);
               }
               _loc4_++;
            }
            ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer()._equipmentsVector = new Vector.<Object>();
            this.reDrawGrid(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer()._equipmentsVector,"equipments");
            this.setInformationText();
            this._packageContentArray = [];
         }
         else if(this._panel.name == "repository")
         {
            _loc5_ = User(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer())._repositoryVector;
            _loc4_ = 0;
            while(_loc4_ < _loc5_.length)
            {
               _loc2_ = _loc5_[_loc4_] as Equipment;
               if(_loc2_._worth[_loc2_.index] == null || _loc2_._worth[_loc2_.index] == undefined)
               {
                  Hero(ThreeKingdoms._instance["_role" + this._packageIndex])._properties.setGold(0);
               }
               else if(Boolean(_loc2_._suitProperty) && Boolean(_loc2_._suitProperty.isNew))
               {
                  Hero(ThreeKingdoms._instance["_role" + this._packageIndex])._properties.setGold(Math.round((_loc2_._worth[_loc2_.index] - 0.1) * 10));
               }
               else
               {
                  Hero(ThreeKingdoms._instance["_role" + this._packageIndex])._properties.setGold(_loc2_._worth[_loc2_.index]);
               }
               _loc4_++;
            }
            ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer()._repositoryVector = new Vector.<Object>();
            this.reDrawGrid(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer()._repositoryVector,"equipments");
            this.setInformationText();
            this._packageContentArray = [];
         }
         else if(this._panel.name == "special")
         {
            _loc6_ = User(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer())._specialVector;
            _loc4_ = 0;
            while(_loc4_ < _loc6_.length)
            {
               _loc2_ = _loc6_[_loc4_] as Equipment;
               if(_loc2_._worth[_loc2_.index] == null || _loc2_._worth[_loc2_.index] == undefined)
               {
                  Hero(ThreeKingdoms._instance["_role" + this._packageIndex])._properties.setGold(0);
               }
               else if(_loc2_._suitProperty.isNew)
               {
                  Hero(ThreeKingdoms._instance["_role" + this._packageIndex])._properties.setGold(Math.round((_loc2_._worth[_loc2_.index] - 0.1) * 10));
               }
               else
               {
                  Hero(ThreeKingdoms._instance["_role" + this._packageIndex])._properties.setGold(_loc2_._worth[_loc2_.index]);
               }
               _loc4_++;
            }
            ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer()._specialVector = new Vector.<Object>();
            ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer()._chocolVector = new Vector.<Object>();
            ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer()._roseVector = new Vector.<Object>();
            ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer()._petFoodVector = new Vector.<Object>();
            this.reDrawGrid(ThreeKingdoms._instance["_role" + this._packageIndex].getPlayer()._specialVector,"special");
            this.setInformationText();
            this._packageContentArray = [];
         }
      }
      
      private function cancelSellAllHandler(param1:MouseEvent) : void
      {
         this.mcSellAll["btnOk"].removeEventListener(MouseEvent.CLICK,this.onSellAllHandler);
         this.mcSellAll["btnNo"].removeEventListener(MouseEvent.CLICK,this.cancelSellAllHandler);
         this.mcSellAll.visible = false;
      }
      
      private function onSelectTypeHandler(param1:MouseEvent) : void
      {
         switch(param1.currentTarget.name)
         {
            case "btnEquipment":
               this._isEquipmentsPanel = true;
               this._isRepositoryPanel = false;
               this._isSpecialPanel = false;
               this.reDrawGrid(this._role.getPlayer()._equipmentsVector,"equipments");
               break;
            case "btnRepository":
               this._isEquipmentsPanel = false;
               this._isSpecialPanel = false;
               this._isRepositoryPanel = true;
               this.reDrawGrid(this._role.getPlayer()._repositoryVector,"repository");
               break;
            case "btnSpecial":
               this._isSpecialPanel = true;
               this._isEquipmentsPanel = false;
               this._isRepositoryPanel = false;
               this.reDrawGrid(this._role.getPlayer()._specialVector,"special");
         }
      }
      
      private function drawGrid(param1:Vector.<Object>) : void
      {
         var _loc2_:PackageContent = null;
         var _loc3_:uint = 0;
         var _loc4_:uint = 0;
         var _loc5_:uint = 0;
         if(param1.length != 0)
         {
            _loc3_ = 0;
            _loc4_ = 0;
            while(_loc4_ < 5)
            {
               _loc5_ = 0;
               while(_loc5_ < 6)
               {
                  _loc2_ = GameUtility.getObject("actors.pack.PackageContent") as PackageContent;
                  if(_loc3_ < param1.length)
                  {
                     if(param1[_loc3_] != null && param1[_loc3_] != undefined)
                     {
                        _loc2_.setShowObj(param1[_loc3_],this._packageIndex);
                        _loc2_.x = _loc5_ * (50 + 20) + 50 / 2;
                        _loc2_.y = _loc4_ * (48 + 20) + 48 / 2;
                        this._panel.addChild(_loc2_);
                        _loc3_++;
                     }
                     this._packageContentArray.push(_loc2_);
                  }
                  _loc5_++;
               }
               _loc4_++;
            }
         }
      }
      
      public function getPackageContentArray() : Array
      {
         return this._packageContentArray;
      }
      
      private function onCloseHandler(param1:MouseEvent) : void
      {
         this.removeChild(this.btnClose);
         stage.focus = null;
         if(this.mcSellAll.visible)
         {
            this.mcSellAll["btnOk"].removeEventListener(MouseEvent.CLICK,this.sellAll);
            this.mcSellAll["btnNo"].removeEventListener(MouseEvent.CLICK,this.cancelSellAllHandler);
            this.mcSellAll.visible = false;
         }
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         if(ThreeKingdoms._instance._gameQuality)
         {
            ThreeKingdoms._instance._gameQuality.visible = true;
         }
         ThreeKingdoms._gameWorld.continueGame();
      }
      
      private function onClickHandler(param1:MouseEvent) : void
      {
      }
      
      public function setPackage(param1:int) : void
      {
         this._packageIndex = param1;
         this._role = ThreeKingdoms._instance["_role" + param1] as Hero;
      }
      
      public function setInformationText() : void
      {
         this.mcRole.gotoAndStop(this._packageIndex);
         this.txtHealthPoint.text = this._role._properties.getCurrentHealthPoint() + "/" + this._role._properties.getTotalHealthPoint();
         this.txtManaPoint.text = this._role._properties.getCurrentManaPoint() + "/" + this._role._properties.getTotalManaPoint();
         this.txtLevel.text = this._role._properties.getLevel().toString();
         this.txtCrit.text = Math.round((this._role._properties.getCrit() - this._role._properties.getForeverCrit()) * 100) + " %";
         if(this._role._properties.getForeverCrit() > 0)
         {
            this.txtAddCrit.text = "+" + Math.round(this._role._properties.getForeverCrit() * 100) + " %";
         }
         this.txtMiss.text = Math.round((this._role._properties.getMiss() - this._role._properties.getForeverMiss()) * 100) + " %";
         if(this._role._properties.getForeverMiss() > 0)
         {
            this.txtAddMiss.text = "+" + Math.round(this._role._properties.getForeverMiss() * 100) + " %";
         }
         this.txtResistance.text = (this._role._properties.getResistance() - this._role._properties.getForeverResistance()).toString();
         if(this._role._properties.getForeverResistance() > 0)
         {
            this.txtAddResistance.text = "+" + this._role._properties.getForeverResistance().toString();
         }
         this.txtMoveSpeed.text = this._role._properties.getMoveSpeed().toString();
         if(this._role._properties.getForeverAttackPower() > 0)
         {
            this.txtAddAttackPower.text = "+" + this._role._properties.getForeverAttackPower().toString();
         }
         this.txtAttackPower.text = (this._role._properties.getAttackPower() - this._role._properties.getForeverAttackPower()).toString();
         this.txtGold.text = int(this._role._properties.getGold()) + "";
      }
      
      public function update() : void
      {
         if(this._role is Role1)
         {
            this.showRole1sEquipment();
         }
         else if(this._role is Role2)
         {
            this.showRole2sEquipment();
         }
         else if(this._role is Role3)
         {
            this.showRole3sEquipment();
         }
      }
      
      public function equip(param1:uint, param2:Equipment) : void
      {
         var _loc3_:String = null;
         var _loc7_:Equipment = null;
         var _loc8_:Vector.<Object> = null;
         var _loc9_:* = undefined;
         var _loc10_:int = 0;
         switch(param2._category)
         {
            case "helmet":
               _loc3_ = "mcHelmetIcon";
               break;
            case "sword":
               _loc3_ = "mcSwordIcon";
               break;
            case "coat":
               _loc3_ = "mcCoatIcon";
               break;
            case "trousers":
               _loc3_ = "mcTrousersIcon";
               break;
            case "shoes":
               _loc3_ = "mcShoesIcon";
               break;
            case "ring":
               _loc3_ = "mcRingIcon";
               break;
            case "necklace":
               _loc3_ = "mcNecklaceIcon";
               break;
            case "amulet":
               _loc3_ = "mcAmuletIcon";
               break;
            case "suit":
               _loc3_ = "mcSuitIcon";
               this._isSuit = true;
         }
         var _loc4_:ShowObj = this.currentEquipmentsPanel[_loc3_].getChildByName("item") as ShowObj;
         if(_loc4_)
         {
            this._role._properties.removeEquipment(_loc4_.getEquipmentObject());
            if(_loc3_ != "mcSuitIcon")
            {
            }
            if(_loc4_.parent)
            {
               _loc4_.parent.removeChild(_loc4_);
            }
            _loc7_ = _loc4_.getEquipmentObject();
            _loc8_ = this._role.getPlayer()._currentEquipments;
            for each(_loc9_ in _loc8_)
            {
               if(_loc9_["_category"] == _loc7_._category)
               {
                  _loc10_ = int(_loc8_.indexOf(_loc9_));
                  this._role.getPlayer()._currentEquipments.splice(_loc10_,1);
               }
            }
            this._role.getPlayer()._equipmentsVector.push(_loc4_.getEquipmentObject());
         }
         this._role.getPlayer()._currentEquipments.push(param2);
         var _loc5_:Vector.<Object> = this._role.getPlayer()._currentEquipments;
         var _loc6_:Object = this._role.getPlayer().getEquipmentID();
         if(_loc3_ != "mcSuitIcon")
         {
            this._role._properties.jugeSuit();
         }
         this._role._properties.addEquipment(param2);
         this._role.changeEquipment(_loc6_);
         this.showCurrentEquipment();
         this.setInformationText();
         this.reDrawGrid(this._role.getPlayer()._equipmentsVector,"equipments");
      }
      
      private function showRole2sEquipment() : void
      {
         var _loc1_:Object = this._role.getPlayer().getEquipmentID();
         this.mcRole["body"]["sword"].gotoAndStop(_loc1_.sword);
         this.mcRole["body"]["helmet"].gotoAndStop(_loc1_.helmet);
         this.mcRole["body"]["coat1"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["coat2"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["coat3"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["coat4"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["coat5"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["coat6"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["shoe1"].gotoAndStop(_loc1_.shoes);
         this.mcRole["body"]["shoe2"].gotoAndStop(_loc1_.shoes);
      }
      
      private function showRole3sEquipment() : void
      {
         var _loc1_:Object = this._role.getPlayer().getEquipmentID();
         this.mcRole["body"]["sword"].gotoAndStop(_loc1_.sword);
         this.mcRole["body"]["helmet"].gotoAndStop(_loc1_.helmet);
         this.mcRole["body"]["coat1"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["coat2"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["coat3"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["coat4"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["shoe1"].gotoAndStop(_loc1_.shoes);
         this.mcRole["body"]["shoe2"].gotoAndStop(_loc1_.shoes);
      }
      
      private function showRole1sEquipment() : void
      {
         var _loc1_:Object = this._role.getPlayer().getEquipmentID();
         this.mcRole["body"]["sword"].gotoAndStop(_loc1_.sword);
         this.mcRole["body"]["helmet"].gotoAndStop(_loc1_.helmet);
         this.mcRole["body"]["coat"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["trousers"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["trousers1"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["trousers2"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["belt"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["cloak"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["cloak2"].gotoAndStop(_loc1_.coat);
         this.mcRole["body"]["shoe1"].gotoAndStop(_loc1_.shoes);
         this.mcRole["body"]["shoe2"].gotoAndStop(_loc1_.shoes);
      }
      
      private function showCurrentEquipment() : void
      {
         var _loc1_:String = null;
         var _loc3_:Equipment = null;
         var _loc4_:ShowObj = null;
         var _loc2_:uint = this._role.getPlayer()._currentEquipments.length;
         while(_loc2_-- > 0)
         {
            _loc3_ = this._role.getPlayer()._currentEquipments[_loc2_] as Equipment;
            _loc4_ = new ShowObj(_loc3_);
            switch(_loc3_._category)
            {
               case "helmet":
                  _loc1_ = "mcHelmetIcon";
                  break;
               case "sword":
                  _loc1_ = "mcSwordIcon";
                  break;
               case "coat":
                  _loc1_ = "mcCoatIcon";
                  break;
               case "trousers":
                  _loc1_ = "mcTrousersIcon";
                  break;
               case "shoes":
                  _loc1_ = "mcShoesIcon";
                  break;
               case "ring":
                  _loc1_ = "mcRingIcon";
                  break;
               case "necklace":
                  _loc1_ = "mcNecklaceIcon";
                  break;
               case "amulet":
                  _loc1_ = "mcAmuletIcon";
                  break;
               case "suit":
                  _loc1_ = "mcSuitIcon";
                  this._isSuit = true;
            }
            if(MovieClip(this.currentEquipmentsPanel[_loc1_]).getChildByName("item"))
            {
               MovieClip(this.currentEquipmentsPanel[_loc1_]).removeChild(MovieClip(this.currentEquipmentsPanel[_loc1_]).getChildByName("item"));
            }
            MovieClip(this.currentEquipmentsPanel[_loc1_]).addChild(_loc4_);
            _loc4_.x = 1;
            _loc4_.y = 0;
            _loc4_.scaleY = 0.65;
            _loc4_.scaleX = 0.65;
            _loc4_.mouseChildren = true;
            _loc4_.mouseEnabled = true;
         }
      }
      
      public function reDrawGrid(param1:Vector.<Object>, param2:String) : void
      {
         if(Boolean(this._panel) && contains(this._panel))
         {
            this.removeChild(this._panel);
            this._panel = null;
         }
         this._panel = new Sprite();
         this._panel.name = param2;
         this._panel.x = 455;
         this._panel.y = 140;
         this.addChild(this._panel);
         this.drawGrid(param1);
      }
      
      public function get theRole() : Hero
      {
         return this._role;
      }
   }
}

