package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy26 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy26(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 450;
         this._object._alertRange = 450;
         this._object._attackRange = 100;
         this._currentHealthPoint = 4900;
         this._totalHealthPoint = 4900;
         this._resistance = 82;
         this._experience = 250;
         this._attackProbablity = 62;
         this._goldPrice = 200;
         this._walkSpeed = 3.2;
         this._probability = 0.15;
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,3],
            "attackInterval":4,
            "attackPower":253 + Math.round(Math.random() * 79),
            "attackType":"physical"
         };
         if(ThreeKingdoms._instance._currentLevel == 8)
         {
            this._currentHealthPoint = 8200;
            this._totalHealthPoint = 8200;
            this._experience = 300;
            this._attackBackInfomationDictionary["攻击1"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[2,3],
               "attackInterval":4,
               "attackPower":208 + Math.round(Math.random() * 54),
               "attackType":"physical"
            };
         }
         this._fallEquipmentsList = [{
            "id":65,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":78,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":79,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":80,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":81,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":15,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":16,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":17,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":18,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":41,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":42,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":43,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":44,
            "qualityID":[1],
            "type":"equipment"
         }];
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1";
      }
      
      override public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(10))
         {
            wait();
         }
         else if(GameUtility.getRandomNumber(_attackProbablity))
         {
            this.attack();
         }
         else
         {
            moveTowardHero();
         }
      }
      
      override public function attack() : void
      {
         _vx = 0;
         steer();
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(16);
         newAttackID();
      }
   }
}

