package actors.enemies
{
   import actors.Enemy;
   import actors.missles.Enemy22Missle;
   import base.BuffEffect;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy22 extends Enemy
   {
      
      private var _buff:BuffEffect;
      
      private var _missle:Enemy22Missle;
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy22(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._buff = new BuffEffect(this);
         this._object._maxPatrolView = 1800;
         this._object._alertRange = 1800;
         this._object._attackRange = 900;
         this._currentHealthPoint = 66000;
         this._totalHealthPoint = 66000;
         this._attackProbablity = 70;
         this._resistance = 162;
         this._experience = 7400;
         this._goldPrice = 4500;
         this._walkSpeed = 6;
         this.isBoss = true;
         this.enemyName = "张辽";
         this._probability = 0.35;
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[4,5],
            "attackInterval":4,
            "attackPower":288 + int(Math.random() * 94),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[4,5],
            "attackInterval":4,
            "attackPower":288 + int(Math.random() * 94),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[4,5],
            "attackInterval":4,
            "attackPower":200 + int(Math.random() * 20),
            "attackType":"magic"
         };
         this._attackBackInfomationDictionary["攻击4"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[4,5],
            "attackInterval":4,
            "attackPower":288 + int(Math.random() * 94),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击5"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[4,5],
            "attackInterval":4,
            "attackPower":288 + int(Math.random() * 94),
            "attackType":"physical"
         };
         if(ThreeKingdoms._instance._currentLevel == 9)
         {
            this._currentHealthPoint = 26000;
            this._totalHealthPoint = 26000;
            isBoss = false;
            this._resistance = 150;
            this._experience = 1500;
            this._attackBackInfomationDictionary["攻击1"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[4,5],
               "attackInterval":4,
               "attackPower":328 + int(Math.random() * 50),
               "attackType":"physical"
            };
            this._attackBackInfomationDictionary["攻击2"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[4,5],
               "attackInterval":4,
               "attackPower":328 + int(Math.random() * 50),
               "attackType":"physical"
            };
            this._attackBackInfomationDictionary["攻击3"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[4,5],
               "attackInterval":4,
               "attackPower":200 + int(Math.random() * 50),
               "attackType":"magic"
            };
            this._attackBackInfomationDictionary["攻击4"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[4,5],
               "attackInterval":4,
               "attackPower":328 + int(Math.random() * 50),
               "attackType":"physical"
            };
            this._attackBackInfomationDictionary["攻击5"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[4,5],
               "attackInterval":4,
               "attackPower":328 + int(Math.random() * 50),
               "attackType":"physical"
            };
         }
         this._fallEquipmentsList = [{
            "id":90,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":91,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":92,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":21,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":22,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":47,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":48,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":84,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":85,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":5,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":31,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":66,
            "qualityID":[0,1],
            "type":"equipment"
         }];
      }
      
      override public function update() : void
      {
         this._buff.update();
         super.update();
      }
      
      public function addMissle() : void
      {
         this._missle = new Enemy22Missle(this,this._curAttackTarget);
         this._missle.x = this.x;
         this._missle.y = this.y;
         this.parent.addChild(this._missle);
         ThreeKingdoms._gameWorld.addMissle(this._missle);
      }
      
      override public function startAttacking() : void
      {
         if(_curAttackTarget)
         {
            if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) >= 400 && GameUtility.getDistance(this,this._curAttackTarget) <= 900)
               {
                  if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 250;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 250 && GameUtility.getDistance(this,this._curAttackTarget) < 400)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 1200;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 250;
                  }
                  else if(_skill4CoolDown == 0)
                  {
                     this.realseSkill4();
                     _skill4CoolDown = 420;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 200 && GameUtility.getDistance(this,this._curAttackTarget) < 250)
               {
                  if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 360;
                  }
                  else if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 1200;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 250;
                  }
                  else if(_skill4CoolDown == 0)
                  {
                     this.realseSkill4();
                     _skill4CoolDown = 420;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) <= 200)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 1200;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 250;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 360;
                  }
                  else if(_skill4CoolDown == 0)
                  {
                     this.realseSkill4();
                     _skill4CoolDown = 420;
                  }
                  else
                  {
                     this.attack();
                  }
               }
            }
            else
            {
               moveTowardHero();
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         this._buff.add([{
            "name":"armor",
            "time":30 * 15
         }]);
         this._resistance += 80;
         setYourDaddysTime(54);
         newAttackID();
      }
      
      override public function realseSkill2() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击3";
         this.gotoAndStop("攻击3");
         setYourDaddysTime(68);
         newAttackID();
      }
      
      override public function realseSkill3() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击4";
         this.gotoAndStop("攻击4");
         setYourDaddysTime(44);
         newAttackID();
      }
      
      override public function realseSkill4() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(29);
         newAttackID();
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击5";
         this.gotoAndStop("攻击5");
         setYourDaddysTime(20);
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4";
      }
   }
}

