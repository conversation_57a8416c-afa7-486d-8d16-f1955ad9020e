package actors.equipments
{
   public class Suit
   {
      
      private var _equepment:Equipment;
      
      public var _suitID:int;
      
      public var _suitName:String;
      
      public var _suitCount:int;
      
      public var _suitProperty:Array;
      
      public var _suits:Suits;
      
      public function Suit(param1:int = 0, param2:String = "", param3:int = 0, param4:Array = null)
      {
         super();
         this._suitID = param1;
         this._suitName = param2;
         this._suitCount = param3;
         this._suitProperty = param4;
      }
   }
}

