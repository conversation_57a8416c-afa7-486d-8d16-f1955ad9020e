package actors.roles
{
   import actors.Hero;
   import actors.SoundManager;
   import actors.info.RoleInfo;
   import flash.events.Event;
   import flash.utils.getTimer;
   
   public class Role1 extends Hero
   {
      
      public static var _isAddedARMOR:Boolean = false;
      
      public var _fallDown:Boolean = false;
      
      public function Role1()
      {
         super();
         _roleName = "LiuBei";
         _roleType = "Warrior";
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[1,-3],
            "attackInterval":4,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击4"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-3],
            "attackInterval":4,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击5"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,-4],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击6"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击7"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击8"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击9"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["跳攻"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["跑攻"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
      }
      
      override public function onAddToStageHandler(param1:Event) : void
      {
         this.initProperties();
      }
      
      override public function update() : void
      {
         super.update();
         if(!this.isDead() && this.currentLabel == "跳攻" && !isInSky())
         {
            this._vx = 0;
         }
      }
      
      override public function getSpecificAttackPowerByType(param1:String) : int
      {
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc7_:Number = NaN;
         var _loc8_:Number = NaN;
         var _loc2_:Number = 0;
         switch(param1)
         {
            case "攻击1":
               return Math.round(this._properties.getTotalAttackPower() + Math.random() * 10);
            case "攻击2":
               return Math.round(this._properties.getTotalAttackPower() + Math.random() * 10);
            case "攻击3":
               return Math.round(this._properties.getTotalAttackPower() * 1.2 + Math.random() * 10);
            case "攻击4":
               return Math.round(this._properties.getTotalAttackPower() * 1.3 + Math.random() * 10);
            case "跑攻":
               return Math.round(this._properties.getTotalAttackPower() * 1.4 + Math.random() * 10);
            case "跳攻":
               return Math.round(this._properties.getTotalAttackPower() * 1.5 + Math.random() * 10);
            case "攻击5":
               return _player.getAttackValueByActionType(param1);
            case "攻击6":
               return _player.getAttackValueByActionType(param1);
            case "攻击7":
               _loc2_ = _player.getAttackValueByActionType(param1);
               if(_properties.getSkillPoint() > 0)
               {
                  _loc2_ += _properties.getSkillPoint();
               }
               return _loc2_;
            case "攻击8":
               _loc2_ = _player.getAttackValueByActionType(param1);
               return (_loc2_ * 0.01 + 1) * _properties.getTotalAttackPower();
            case "攻击9":
               _loc2_ = _player.getAttackValueByActionType(param1);
               return (_loc2_ * 0.01 + 1) * _properties.getTotalAttackPower();
            default:
               return 0;
         }
      }
      
      override public function runAttack() : void
      {
         if(!this.isInSky())
         {
            if(this._properties.getCurrentManaPoint() > 0)
            {
               this._hitTimes = 1;
               this._lastHit = "跑攻";
               this.gotoAndStop("跑攻");
               this._isRunning = false;
               SoundManager.play("Role1_hit2");
               this.newAttackID();
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 0);
            }
            else
            {
               this.gotoAndStop("攻击1");
               SoundManager.play("Role1_hit1");
               this._lastHit = "攻击1";
               this.newAttackID();
            }
         }
      }
      
      override public function levelUP(param1:int = 1) : void
      {
         if(!this._isFirstTimeToInit)
         {
            this._properties.destroy();
         }
         this._isFirstTimeToInit = false;
         this._properties.setTotalHealthPoint(110 + 50 * (this._properties.getLevel() - 1));
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(90 + 40 * (this._properties.getLevel() - 1));
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
         this._properties.setAttackPower(10 + Math.round(Math.random() * 4) + 5 * (_properties.getLevel() - 1));
         this._properties.setTotalExperience(((_properties.getLevel() - 1) * (_properties.getLevel() - 1) * 100 + 50 * _properties.getLevel()) * 1.5);
         if(this._isAddCirt)
         {
            if(this._addCirt > 0)
            {
               this._properties.setCrit(this._properties.getCrit() - this._addCirt);
               this._addCirt = 0;
               this._cirtTimer = 0;
               this._isAddCirt = false;
               if(this.mcCrazy.visible)
               {
                  this.mcCrazy.visible = false;
               }
            }
         }
         this._properties.setCrit(0.01 + 0.001 * this._properties.getLevel());
         this._addCirt = 0;
         _properties.initAll();
         this._properties.setTotalHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(this._properties.getTotalManaPoint());
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
      }
      
      override public function realseSkill1() : void
      {
         if(this.getPlayer()._learnSkill[0] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= 25)
         {
            if(this.currentLabel != "攻击5")
            {
               this.gotoAndStop("攻击5");
               this._lastHit = "攻击5";
               setYourDaddysTime(13);
               SoundManager.play("Role1_hit5");
               this._hitTimes = 0;
               this._times = 12;
               this.newAttackID();
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 25);
            }
         }
      }
      
      override public function realseSkill2() : void
      {
         if(this.getPlayer()._learnSkill[1] == 0)
         {
            return;
         }
         if(_isAddedARMOR || this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= 15)
         {
            if(this.currentLabel != "攻击6")
            {
               _vx = 0;
               this.gotoAndStop("攻击6");
               SoundManager.play("Role1_hit7");
               setYourDaddysTime(30);
               this._buffEffect.add([{
                  "name":"armor",
                  "time":30 * 15,
                  "power":0
               }]);
               Role1._isAddedARMOR = true;
               this._hitTimes = 0;
               this._times = 10;
               this.newAttackID();
               this._properties.setResistance(this._properties.getResistance() + this.getSpecificAttackPowerByType("攻击6"));
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 15);
            }
         }
      }
      
      override public function realseSkill3() : void
      {
         if(this.getPlayer()._learnSkill[2] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= 18)
         {
            if(this.currentLabel != "攻击7")
            {
               _vx = 0;
               this.gotoAndStop("攻击7");
               SoundManager.play("Role1_hit6");
               setYourDaddysTime(20);
               this._hitTimes = 0;
               this._times = 8;
               this.newAttackID();
               this.healing(Number(this.getSpecificAttackPowerByType("攻击7")));
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 18);
            }
         }
      }
      
      override public function realseSkill4() : void
      {
         if(this.getPlayer()._learnSkill[3] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= 30)
         {
            if(this.currentLabel != "攻击8")
            {
               if(this.isInSky())
               {
                  this._fallDown = true;
               }
               this.gotoAndStop("攻击8");
               setYourDaddysTime(34);
               this._lastHit = "攻击8";
               this._hitTimes = 0;
               this._times = 12;
               SoundManager.play("Role1_hit8");
               this.newAttackID();
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 30);
            }
         }
      }
      
      override public function realseSkill5() : void
      {
         if(this.getPlayer()._learnSkill[4] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         var _loc1_:RoleInfo = ThreeKingdoms._instance._gameInfo.getRoleInfoByPlayer(this._player) as RoleInfo;
         if(_loc1_.isUniqueReady())
         {
            if(this.currentLabel != "攻击9")
            {
               _vx = 0;
               this.gotoAndStop("攻击9");
               this._lastHit = "攻击9";
               SoundManager.play("Role1_hit9_1");
               setYourDaddysTime(130);
               _loc1_._uniquePointObject.uniquePoint = 0;
            }
         }
      }
      
      override public function attack() : void
      {
         this._timer = getTimer();
         if(_times <= 0)
         {
            if(!this.isInSky())
            {
               if(!this.isRunning() && (!this.isAttacking() || this.isNormalHit()))
               {
                  if(this._hitTimes == 4)
                  {
                     this._times = 18;
                  }
                  else if(_hitTimes == 3)
                  {
                     this._times = 15;
                  }
                  else
                  {
                     this._times = 10;
                  }
                  if(this._timer - _lastTime > 1000)
                  {
                     this._hitTimes = 1;
                  }
                  else if(++_hitTimes > 4)
                  {
                     this._hitTimes = 1;
                  }
                  if(this._hitTimes > 2)
                  {
                     if(this._hitTimes <= 4 && this._hitTimes >= 2)
                     {
                     }
                  }
                  _vx = 0;
                  this.gotoAndStop("攻击" + this._hitTimes);
                  this._lastHit = "攻击" + this._hitTimes;
                  SoundManager.play("Role1_hit" + this._hitTimes);
                  this.newAttackID();
               }
               else if(this.isRunning() && !this.isAttacking())
               {
                  this.gotoAndStop("跑攻");
                  this._lastHit = "跑攻";
                  SoundManager.play("Role1_hit3");
                  this._isjumpAttack = true;
                  this.newAttackID();
                  this._hitTimes = 0;
                  this._isRunning = false;
               }
            }
            else if(isInSky() && !this.isAttacking() && !_isjumpAttack)
            {
               this._times = 28;
               this._lastHit = "跳攻";
               this.gotoAndStop("跳攻");
               SoundManager.play("Role1_hit3");
               this.newAttackID();
               this._hitTimes = 0;
            }
         }
         this._lastTime = this._timer;
      }
      
      override public function isRunning() : Boolean
      {
         return this.currentLabel == "跑";
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4" || this.currentLabel == "攻击5" || this.currentLabel == "攻击6" || this.currentLabel == "攻击7" || this.currentLabel == "攻击8" || this.currentLabel == "攻击9" || this.currentLabel == "跑攻" || this.currentLabel == "跳攻";
      }
      
      override public function isNormalHit() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4";
      }
      
      override public function isJumping() : Boolean
      {
         return this.currentLabel == "跳" || this.currentLabel == "二级跳" || this.currentLabel == "落地";
      }
   }
}

