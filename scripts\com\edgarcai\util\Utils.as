package com.edgarcai.util
{
   import flash.utils.ByteArray;
   
   public final class Utils
   {
      
      public function Utils()
      {
         super();
      }
      
      public static function equal(param1:*, param2:*) : Boolean
      {
         var _loc3_:ByteArray = new ByteArray();
         _loc3_.writeObject(param1);
         _loc3_.position = 0;
         var _loc4_:ByteArray = new ByteArray();
         _loc4_.writeObject(param2);
         _loc4_.position = 0;
         if(_loc3_.length == _loc4_.length)
         {
            while(_loc3_.bytesAvailable)
            {
               if(_loc3_.readByte() != _loc4_.readByte())
               {
                  return false;
               }
            }
            return true;
         }
         return false;
      }
   }
}

