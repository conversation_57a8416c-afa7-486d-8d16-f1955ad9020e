package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy4 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy4(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._walkSpeed = 3;
         this._object._maxPatrolView = 400;
         this._object._alertRange = 400;
         this._object._attackRange = 100;
         this._currentHealthPoint = 450;
         this._totalHealthPoint = 450;
         this._probability = 0.3;
         this._resistance = 6;
         this._experience = 30;
         this._goldPrice = 3 + Math.round(Math.random() * 5);
         this._attackProbablity = 38;
         this._fallEquipmentsList = [{
            "id":1,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":62,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":27,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":8,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":9,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":72,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":73,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":34,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":35,
            "qualityID":[0,1,2],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,3],
            "attackInterval":4,
            "attackPower":30 + Math.random() * 10,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,3],
            "attackInterval":4,
            "attackPower":30 + Math.random() * 10,
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(10))
         {
            wait();
         }
         else if(GameUtility.getRandomNumber(_attackProbablity))
         {
            if(GameUtility.getRandomNumber(50))
            {
               steer();
               _vx = 0;
               setYourDaddysTime(11);
               this.gotoAndStop("攻击1");
               this._lastHit = "攻击1";
               newAttackID();
            }
            else
            {
               steer();
               _vx = 0;
               setYourDaddysTime(7);
               this.gotoAndStop("攻击2");
               this._lastHit = "攻击2";
               newAttackID();
            }
         }
      }
   }
}

