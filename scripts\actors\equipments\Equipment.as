package actors.equipments
{
   public class Equipment
   {
      
      private var _objectToSaveEquipment:Object = {};
      
      private var _stringToSaveEquipment:String = "";
      
      public var _frame:uint = 0;
      
      public var _id:uint = 0;
      
      public var _name:String;
      
      public var _type:String;
      
      public var _user:String;
      
      public var _color:uint;
      
      public var _quality:Array = [];
      
      public var _property:Array;
      
      public var _level:Array = [];
      
      public var _additionalProperty:Array = [];
      
      public var _suitProperty:Object = null;
      
      private var _index:uint = 0;
      
      public var _instruction:String;
      
      public var _worth:Array = [];
      
      public var _category:String;
      
      public function Equipment(param1:uint = 0, param2:uint = 1, param3:String = "", param4:String = "", param5:String = "", param6:uint = 0, param7:Array = null, param8:Array = null, param9:Array = null, param10:Array = null, param11:Object = null, param12:uint = 0, param13:Array = null, param14:String = "", param15:String = "")
      {
         super();
         this._id = param1;
         this._frame = param2;
         this._name = param3;
         this._type = param4;
         this._user = param5;
         this._color = param6;
         this._quality = param7;
         this._property = param8;
         this._additionalProperty = param9;
         this._level = param10;
         this._suitProperty = param11;
         this._index = param12;
         this._instruction = param14;
         this._category = param15;
         this._worth = param13;
      }
      
      public function getEquipmentSaveString() : String
      {
         this._stringToSaveEquipment = this._id + this._frame + "|" + this._name + "|" + this._type + "|" + this._user + "|" + this._quality.join(",") + "|" + this._color + "|" + this._level + "|" + this._instruction;
         return this._stringToSaveEquipment;
      }
      
      public function getAdditionalPropertyByID(param1:int) : Object
      {
         if(param1 == this._id)
         {
            return this._additionalProperty[this._index];
         }
         return null;
      }
      
      public function getEquipmentQualityByIndex(param1:int) : String
      {
         return this._quality[param1];
      }
      
      public function getEquipmentBasePropertyByIndex(param1:int) : String
      {
         if(this._suitProperty.isNew)
         {
            return String(Math.round((this._property[param1] - 0.1) * 10));
         }
         return String(this._property[param1]);
      }
      
      public function setEquipmentObject(param1:Object) : void
      {
         this._id = param1._id;
         this._frame = param1._frame;
         this._name = param1._name;
         this._type = param1._type;
         this._user = param1._user;
         this._color = param1._color;
         this._quality = param1._quality;
         this._property = param1._property;
         this._additionalProperty = param1._additionalProperty;
         this._level = param1._level;
         this._suitProperty = param1._suitProperty;
         this._index = param1._index;
         this._instruction = param1._instruction;
         this._category = param1._category;
         this._worth = param1._worth;
      }
      
      public function setOldEquipmentObject(param1:String) : void
      {
      }
      
      public function getGoldByEquipment(param1:uint) : void
      {
      }
      
      public function get index() : uint
      {
         return this._index;
      }
      
      public function set index(param1:uint) : void
      {
         this._index = param1;
      }
   }
}

