package actors.roles
{
   import actors.Hero;
   import actors.SoundManager;
   import actors.info.RoleInfo;
   import flash.events.Event;
   import flash.utils.getTimer;
   
   public class Role2 extends Hero
   {
      
      public static var _isAddedPower:Boolean = false;
      
      public function Role2()
      {
         super();
         _roleName = "GuanYu";
         _roleType = "Warrior";
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0,-3],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-3],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[1,-3],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击4"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[-1.5,-6],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击5"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,-4],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击6"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[5,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["跳攻"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["跑攻"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击7"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击8"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-2],
            "attackInterval":999,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击9"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
      }
      
      override public function onAddToStageHandler(param1:Event) : void
      {
         this.initProperties();
      }
      
      override public function getSpecificAttackPowerByType(param1:String) : int
      {
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc7_:Number = NaN;
         var _loc8_:Number = NaN;
         var _loc2_:Number = 0;
         switch(param1)
         {
            case "攻击1":
               return Math.round(this._properties.getTotalAttackPower() + Math.random() * 10);
            case "攻击2":
               return Math.round(this._properties.getTotalAttackPower() + Math.random() * 10);
            case "攻击3":
               return Math.round(this._properties.getTotalAttackPower() * 1.2 + Math.random() * 10);
            case "攻击4":
               return Math.round(this._properties.getTotalAttackPower() * 1.3 + Math.random() * 10);
            case "跑攻":
               return Math.round(this._properties.getTotalAttackPower() * 1.4 + Math.random() * 10);
            case "攻击5":
               return Math.round(this._properties.getTotalAttackPower() * 1.5 + Math.random() * 10);
            case "攻击6":
               return _player.getAttackValueByActionType("攻击6");
            case "加攻":
               return _player.getAttackValueByActionType("加攻7");
            case "攻击7":
               _loc2_ = _player.getAttackValueByActionType("技能8");
               return (_loc2_ * 0.01 + 1) * _properties.getTotalAttackPower();
            case "攻击8":
               _loc2_ = _player.getAttackValueByActionType("技能5");
               return (_loc2_ * 0.01 + 1) * _properties.getTotalAttackPower();
            case "攻击9":
               _loc2_ = _player.getAttackValueByActionType(param1);
               return (_loc2_ * 0.01 + 1) * _properties.getTotalAttackPower();
            default:
               return 0;
         }
      }
      
      override public function runAttack() : void
      {
         if(!this.isInSky())
         {
            if(this._properties.getCurrentManaPoint() > 0)
            {
               this._hitTimes = 1;
               this._lastHit = "跑攻";
               this.gotoAndStop("跑攻");
               this.newAttackID();
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 0);
            }
            else
            {
               this.gotoAndStop("攻击1");
               this._lastHit = "攻击1";
               this.newAttackID();
            }
         }
      }
      
      override public function levelUP(param1:int = 1) : void
      {
         if(!this._isFirstTimeToInit)
         {
            this._properties.destroy();
         }
         this._isFirstTimeToInit = false;
         this._properties.setTotalHealthPoint(160 + 80 * (this._properties.getLevel() - 1));
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(90 + 35 * (this._properties.getLevel() - 1));
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
         this._properties.setAttackPower(14 + Math.round(Math.random() * 4) + 6 * (_properties.getLevel() - 1));
         this._properties.setTotalExperience(((_properties.getLevel() - 1) * (_properties.getLevel() - 1) * 100 + 50 * _properties.getLevel()) * 1.5);
         this._properties.setCrit(0.01 + 0.001 * this._properties.getLevel());
         _properties.initAll();
         this._properties.setTotalHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(this._properties.getTotalManaPoint());
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
      }
      
      override public function realseSkill1() : void
      {
         if(this.getPlayer()._learnSkill[0] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= 20)
         {
            if(this.currentLabel != "攻击8")
            {
               this.gotoAndStop("攻击8");
               this._lastHit = "攻击8";
               this._hitTimes = 0;
               this._times = 12;
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 20);
            }
         }
      }
      
      override public function realseSkill2() : void
      {
         if(this.getPlayer()._learnSkill[1] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= 30)
         {
            if(this.currentLabel != "攻击6")
            {
               this.gotoAndStop("攻击6");
               this._lastHit = "攻击6";
               this._hitTimes = 0;
               this._times = 10;
               this.newAttackID();
               setYourDaddysTime(18);
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 30);
            }
         }
      }
      
      override public function realseSkill3() : void
      {
         if(this.getPlayer()._learnSkill[2] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack() || _isAddedPower)
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= 50)
         {
            if(this.currentLabel != "加攻")
            {
               _vx = 0;
               this.gotoAndStop("加攻");
               this._hitTimes = 0;
               this._times = 8;
               this._lastHit = "加攻";
               this.newAttackID();
               this._buffEffect.add([{
                  "name":"power",
                  "time":30 * 10
               }]);
               this.mcState.visible = true;
               Role2._isAddedPower = true;
               setYourDaddysTime(39);
               this._properties.setAttackPower(this._properties.getAttackPower() + this.getSpecificAttackPowerByType("加攻"));
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 50);
            }
         }
      }
      
      override public function realseSkill4() : void
      {
         if(this.getPlayer()._learnSkill[3] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= 80)
         {
            if(this.currentLabel != "攻击7")
            {
               _vx = 0;
               this.gotoAndStop("攻击7");
               this._lastHit = "攻击7";
               this._hitTimes = 0;
               this._times = 12;
               this.newAttackID();
               setYourDaddysTime(33);
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 80);
            }
         }
      }
      
      override public function realseSkill5() : void
      {
         var _loc1_:RoleInfo = null;
         if(this.getPlayer()._learnSkill[4] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         _loc1_ = ThreeKingdoms._instance._gameInfo.getRoleInfoByPlayer(this._player) as RoleInfo;
         if(_loc1_.isUniqueReady())
         {
            if(this.currentLabel != "攻击9")
            {
               _vx = 0;
               _vy = 0;
               this.gotoAndStop("攻击9");
               this._lastHit = "攻击9";
               this._hitTimes = 0;
               this._times = 12;
               setYourDaddysTime(100);
               SoundManager.play("Role2_hit9_1");
               _loc1_._uniquePointObject.uniquePoint = 0;
            }
         }
      }
      
      override public function attack() : void
      {
         this._timer = getTimer();
         if(_times <= 0)
         {
            if(!this.isInSky())
            {
               if(!this.isRunning() && (!this.isAttacking() || this.isNormalHit()))
               {
                  if(this._hitTimes == 4)
                  {
                     this._times = 18;
                  }
                  else if(_hitTimes == 3)
                  {
                     this._times = 15;
                  }
                  else
                  {
                     this._times = 8;
                  }
                  if(this._timer - _lastTime > 1000)
                  {
                     this._hitTimes = 1;
                  }
                  else if(++_hitTimes > 4)
                  {
                     this._hitTimes = 1;
                  }
                  if(this._hitTimes > 2)
                  {
                     if(this._hitTimes <= 4 && this._hitTimes >= 2)
                     {
                     }
                  }
                  _vx = 0;
                  this.gotoAndStop("攻击" + this._hitTimes);
                  this._lastHit = "攻击" + this._hitTimes;
                  this.newAttackID();
               }
               else if(this.isRunning() && !this.isAttacking())
               {
                  this.gotoAndStop("跑攻");
                  this._lastHit = "跑攻";
                  this.newAttackID();
                  this._hitTimes = 0;
               }
            }
            else
            {
               this._times = 15;
               this._lastHit = "攻击5";
               this.gotoAndStop("攻击5");
               this.newAttackID();
               this._hitTimes = 0;
            }
         }
         this._lastTime = this._timer;
      }
      
      override public function update() : void
      {
         super.update();
         if(!this.isDead() && this.currentLabel == "攻击5" && !isInSky())
         {
            this._vx = 0;
         }
      }
      
      override public function isRunning() : Boolean
      {
         return this.currentLabel == "跑";
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4" || this.currentLabel == "攻击5" || this.currentLabel == "攻击6" || this.currentLabel == "攻击7" || this.currentLabel == "攻击8" || this.currentLabel == "攻击9" || this.currentLabel == "跑攻" || this.currentLabel == "跳攻" || this.currentLabel == "加攻";
      }
      
      override public function isNormalHit() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4";
      }
      
      override public function isJumping() : Boolean
      {
         return this.currentLabel == "跳" || this.currentLabel == "二级跳" || this.currentLabel == "落地";
      }
   }
}

