package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy28 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy28(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._walkSpeed = 5;
         this.isBoss = false;
         this.enemyName = "张宝";
         this._totalHealthPoint = 66000;
         this._currentHealthPoint = 66000;
         this._attackProbablity = 70;
         this._goldPrice = 400;
         this._experience = 3000;
         this._resistance = 182;
         this._probability = 0.1;
         this._object._maxPatrolView = 750;
         this._object._alertRange = 750;
         this._object._attackRange = 180;
         this._fallEquipmentsList = [{
            "id":54,
            "qualityID":[0],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,2],
            "attackInterval":4,
            "attackPower":358 + int(Math.random() * 24),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,4],
            "attackInterval":4,
            "attackPower":358 + int(Math.random() * 24),
            "attackType":"magic"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(_skill1CoolDown == 0)
         {
            this.realseSkill1();
            _skill1CoolDown = 120;
         }
         else if(GameUtility.getDistance(this,this._curAttackTarget) < 130)
         {
            if(GameUtility.getRandomNumber(_attackProbablity))
            {
               this.attack();
            }
         }
         else
         {
            moveTowardHero();
         }
      }
      
      override public function attack() : void
      {
         _vx = 0;
         steer();
         setYourDaddysTime(15);
         this.gotoAndStop("攻击1");
         this._lastHit = "攻击1";
         newAttackID();
      }
      
      override public function realseSkill1() : void
      {
         _vx = 0;
         steer();
         setYourDaddysTime(45);
         this.gotoAndStop("攻击2");
         this._lastHit = "攻击2";
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2";
      }
   }
}

