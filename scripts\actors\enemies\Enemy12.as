package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy12 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      private var _totalMoveTime:int = 90;
      
      private var _currentMoveTime:int = 0;
      
      public function Enemy12(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 1650;
         this._object._alertRange = 1650;
         this._object._attackRange = 250;
         this._currentHealthPoint = 2000;
         this._totalHealthPoint = 2000;
         this._experience = 200;
         this._resistance = 40;
         this._isFlying = true;
         this._attackProbablity = 55;
         this._walkSpeed = 3;
         this._probability = 0.3;
         this._goldPrice = 30 + Math.round(Math.random() * 20);
         this._ay = 0;
         this._totalMoveTime = (1 + 2 * Math.random()) * 30;
         this._fallEquipmentsList = [{
            "id":15,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":16,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":17,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":18,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":78,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":79,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":80,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":81,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":41,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":42,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":43,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":44,
            "qualityID":[0],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[6,-5],
            "attackInterval":4,
            "attackPower":88 + Math.round(Math.random() * 30),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override protected function myIntelligence() : void
      {
         if(!this.isUnderAttack() && !this.isAttacking())
         {
            super.myIntelligence();
         }
      }
      
      override public function startAttacking() : void
      {
         if(_curAttackTarget)
         {
            if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) < 200)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 90 + Math.round(Math.random() * 60);
                  }
                  else
                  {
                     this.moveTowardHero();
                  }
               }
               else
               {
                  this.moveTowardHero();
               }
            }
            else
            {
               this.moveTowardHero();
            }
         }
         else
         {
            this.patrol();
         }
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1";
      }
      
      override public function setWrapTime() : void
      {
         this._wrapTime = WRAP_TIME;
      }
      
      override public function setStandStill() : void
      {
         this._isLeft = false;
         this._isRight = false;
         this._vx = 0;
         this._vy = 0;
      }
      
      override protected function moveTowardHero() : void
      {
         if(this._curAttackTarget)
         {
            if(_count % (24 + int(Math.random() * 6)) == 0)
            {
               if(this.x > this._curAttackTarget.x)
               {
                  this.steer();
                  _vx = -_walkSpeed;
               }
               else
               {
                  this.steer();
                  _vx = _walkSpeed;
               }
               if(this.y < this._curAttackTarget.y - 200)
               {
                  if(this.y < 100)
                  {
                     _vy = 1;
                  }
                  else if(this.y > 580)
                  {
                     _vy = -1;
                  }
                  if(this._vy < 4)
                  {
                     this._vy += 0.5;
                  }
               }
               else
               {
                  if(this.y < 100)
                  {
                     _vy = 1;
                  }
                  else if(this.y > 580)
                  {
                     _vy = -1;
                  }
                  if(this._vy > -4)
                  {
                     this._vy -= 0.5;
                  }
               }
            }
         }
         else
         {
            this.patrol();
         }
         if(this.y < 100)
         {
            this.y = 100;
         }
         else if(this.y > 500)
         {
            this.y = 500;
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(22);
         newAttackID();
      }
      
      override public function isSeek() : Boolean
      {
         return Math.abs(this.x - this._curAttackTarget.x) > 150 || Math.abs(this.y - this._curAttackTarget.y) > 150;
      }
      
      override protected function patrol() : void
      {
         if(this.currentFrameLabel != "行走")
         {
            this.gotoAndStop("行走");
         }
         if(this.x <= _addPoint.x - _object._maxPatrolView)
         {
            _isPatrolToLeftEdge = true;
            _isPatrolToRightEdge = false;
         }
         else if(this.x >= _addPoint.x + _object._maxPatrolView)
         {
            _isPatrolToRightEdge = true;
            _isPatrolToLeftEdge = false;
         }
         if(_isPatrolToLeftEdge)
         {
            _object._direction = RIGHT;
         }
         else if(_isPatrolToRightEdge)
         {
            _object._direction = LEFT;
         }
         if(this._object._direction == LEFT)
         {
            _vx = -_walkSpeed;
            GameUtility.flipHorizontal(this,1);
         }
         else
         {
            _vx = _walkSpeed;
            GameUtility.flipHorizontal(this,-1);
         }
         if(!this.isAttacking() && !this.isUnderAttack())
         {
            if(this._curAttackTarget)
            {
               this.moveTowardHero();
            }
            else if(_count % 30 == 0)
            {
               if(GameUtility.getRandomNumber(50))
               {
                  _object._direction = LEFT;
                  GameUtility.flipHorizontal(this,1);
                  _vx = -_walkSpeed;
               }
               else
               {
                  _object._direction = RIGHT;
                  GameUtility.flipHorizontal(this,-1);
                  _vx = _walkSpeed;
               }
            }
         }
         else if(this.y < 0)
         {
            _vy = 1;
         }
         else if(this.y > 580)
         {
            _vy = -1;
         }
         if(this.y < 100)
         {
            this.y = 100;
         }
         else if(this.y > 500)
         {
            this.y = 500;
         }
      }
   }
}

