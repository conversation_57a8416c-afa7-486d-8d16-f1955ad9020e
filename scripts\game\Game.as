package game
{
   import actors.Enemy;
   import actors.Hero;
   import actors.enemies.*;
   import actors.roles.*;
   import actors.user.User;
   import base.EnemyAppearPoint;
   import base.GameWorld;
   import event.GameEvent;
   import flash.display.MovieClip;
   import flash.utils.Dictionary;
   import util.GameUtility;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol331")]
   public class Game extends MovieClip
   {
      
      public var __id11_:EnemyAppearPoint;
      
      public var __id26_:EnemyAppearPoint;
      
      public var __id37_:EnemyAppearPoint;
      
      public var __id62_:EnemyAppearPoint;
      
      public var __id16_:EnemyAppearPoint;
      
      public var __id27_:EnemyAppearPoint;
      
      public var __id34_:EnemyAppearPoint;
      
      public var __id52_:EnemyAppearPoint;
      
      public var __id63_:EnemyAppearPoint;
      
      public var __id17_:EnemyAppearPoint;
      
      public var __id24_:EnemyAppearPoint;
      
      public var __id35_:EnemyAppearPoint;
      
      public var __id42_:EnemyAppearPoint;
      
      public var __id53_:EnemyAppearPoint;
      
      public var __id60_:EnemyAppearPoint;
      
      public var __id14_:EnemyAppearPoint;
      
      public var __id25_:EnemyAppearPoint;
      
      public var __id43_:EnemyAppearPoint;
      
      public var __id50_:EnemyAppearPoint;
      
      public var __id61_:EnemyAppearPoint;
      
      public var __id2_:EnemyAppearPoint;
      
      public var __id15_:EnemyAppearPoint;
      
      public var __id40_:EnemyAppearPoint;
      
      public var __id51_:EnemyAppearPoint;
      
      public var __id66_:EnemyAppearPoint;
      
      public var __id3_:EnemyAppearPoint;
      
      public var __id41_:EnemyAppearPoint;
      
      public var __id56_:EnemyAppearPoint;
      
      public var __id67_:EnemyAppearPoint;
      
      public var __id0_:EnemyAppearPoint;
      
      public var __id38_:EnemyAppearPoint;
      
      public var __id46_:EnemyAppearPoint;
      
      public var __id57_:EnemyAppearPoint;
      
      public var __id64_:EnemyAppearPoint;
      
      public var __id1_:EnemyAppearPoint;
      
      public var __id28_:EnemyAppearPoint;
      
      public var __id39_:EnemyAppearPoint;
      
      public var __id47_:EnemyAppearPoint;
      
      public var __id54_:EnemyAppearPoint;
      
      public var __id65_:EnemyAppearPoint;
      
      public var __id6_:EnemyAppearPoint;
      
      public var __id18_:EnemyAppearPoint;
      
      public var __id29_:EnemyAppearPoint;
      
      public var __id44_:EnemyAppearPoint;
      
      public var __id55_:EnemyAppearPoint;
      
      public var __id7_:EnemyAppearPoint;
      
      public var __id19_:EnemyAppearPoint;
      
      public var __id45_:EnemyAppearPoint;
      
      public var __id4_:EnemyAppearPoint;
      
      public var __id5_:EnemyAppearPoint;
      
      public var __id32_:EnemyAppearPoint;
      
      public var __id58_:EnemyAppearPoint;
      
      public var __id22_:EnemyAppearPoint;
      
      public var __id33_:EnemyAppearPoint;
      
      public var __id48_:EnemyAppearPoint;
      
      public var __id59_:EnemyAppearPoint;
      
      public var __id12_:EnemyAppearPoint;
      
      public var __id23_:EnemyAppearPoint;
      
      public var __id30_:EnemyAppearPoint;
      
      public var __id49_:EnemyAppearPoint;
      
      public var __id8_:EnemyAppearPoint;
      
      public var __id13_:EnemyAppearPoint;
      
      public var __id20_:EnemyAppearPoint;
      
      public var __id31_:EnemyAppearPoint;
      
      public var __id9_:EnemyAppearPoint;
      
      public var __id10_:EnemyAppearPoint;
      
      public var __id21_:EnemyAppearPoint;
      
      public var __id36_:EnemyAppearPoint;
      
      public var __setPropDict:Dictionary;
      
      private var _world:GameWorld;
      
      private var _enemy1:Enemy1;
      
      private var _enemy2:Enemy2;
      
      private var _enemy3:Enemy3;
      
      private var _enemy4:Enemy4;
      
      private var _enemy5:Enemy5;
      
      private var _enemy6:Enemy6;
      
      private var _enemy7:Enemy7;
      
      private var _enemy8:Enemy8;
      
      private var _enemy9:Enemy9;
      
      private var _enemy10:Enemy10;
      
      private var _enemy11:Enemy11;
      
      private var _enemy12:Enemy12;
      
      private var _enemy13:Enemy13;
      
      private var _enemy14:Enemy14;
      
      private var _enemy15:Enemy15;
      
      private var _enemy16:Enemy16;
      
      private var _enemy17:Enemy17;
      
      private var _enemy18:Enemy18;
      
      private var _enemy19:Enemy19;
      
      private var _enemy20:Enemy20;
      
      private var _enemy21:Enemy21;
      
      private var _enemy22:Enemy22;
      
      private var _enemy23:Enemy23;
      
      private var _enemy24:Enemy24;
      
      private var _enemy25:Enemy25;
      
      private var _enemy26:Enemy26;
      
      private var _enemy27:Enemy27;
      
      private var _enemy28:Enemy28;
      
      private var _enemy29:Enemy29;
      
      private var _enemy30:Enemy30;
      
      private var _enemy31:Enemy31;
      
      private var _enemy32:Enemy32;
      
      private var _enemy33:Enemy33;
      
      private var _enemy34:Enemy34;
      
      private var _enemy35:Enemy35;
      
      private var _enemy36:Enemy36;
      
      private var _enemy37:Enemy37;
      
      private var _user1:User;
      
      private var _user2:User;
      
      private var _role1:Role1;
      
      private var _role2:Role2;
      
      public var transferDoor:MovieClip;
      
      public var floor:MovieClip;
      
      private var tk:ThreeKingdoms;
      
      public function Game()
      {
         var _loc2_:* = undefined;
         this.__setPropDict = new Dictionary(true);
         super();
         addFrameScript(0,this.frame1,1,this.frame2,2,this.frame3,3,this.frame4,4,this.frame5,5,this.frame6,6,this.frame7,7,this.frame8,8,this.frame9,9,this.frame10,10,this.frame11,11,this.frame12,12,this.frame13);
         this._world = ThreeKingdoms._gameWorld;
         this.gotoAndStop(ThreeKingdoms._instance._currentLevel);
         this.transferDoor.visible = false;
         var _loc1_:* = 0;
         while(_loc1_ < this.numChildren)
         {
            _loc2_ = this.getChildAt(_loc1_);
            if(_loc2_ is MovieClip)
            {
               if(MovieClip(_loc2_).getChildByName("isWall"))
               {
                  _loc2_.visible = false;
                  this._world.addWall(_loc2_);
               }
               if(MovieClip(_loc2_).getChildByName("enemyDisappearPoint"))
               {
                  _loc2_.visible = false;
                  this._world.addSendEnemyPoint(_loc2_);
               }
            }
            _loc1_++;
         }
         this._world.start();
         this._world._eventManager.addEventListener(GameEvent.ENEMY_IS_ATTACK_BY_BULLET,this.onEnemyIsAttackByBulletHandler);
         this._world._eventManager.addEventListener(GameEvent.HERO_IS_UNDER_ATTACK_BY_BULLET,this.onHeroIsUnderAttackByBulletHandler);
      }
      
      private function onHeroIsUnderAttackByBulletHandler(param1:GameEvent) : void
      {
         var _loc2_:Enemy = Object(param1._data).role as Enemy;
         var _loc3_:Hero = Object(param1._data).enemy as Hero;
         var _loc4_:Object = _loc2_._attackBackInfomationDictionary[_loc2_._lastHit];
         if(ThreeKingdoms._instance._protectedProperty.getProperty(_loc3_,"_whosYourDaddy"))
         {
            return;
         }
         if(_loc3_.isDead())
         {
            return;
         }
         _loc3_.afterAttack(_loc2_,_loc4_);
      }
      
      private function onEnemyIsAttackByBulletHandler(param1:GameEvent) : void
      {
         var _loc2_:Hero = Object(param1._data).role as Hero;
         var _loc3_:Enemy = Object(param1._data).enemy as Enemy;
         var _loc4_:Object = _loc2_._attackBackInfomationDictionary[_loc2_._lastHit];
         if(ThreeKingdoms._instance._protectedProperty.getProperty(_loc3_,"_whosYourDaddy"))
         {
            return;
         }
         if(_loc3_.isDead())
         {
            return;
         }
         _loc3_.afterAttack(_loc2_,_loc4_);
      }
      
      public function addEnemy(param1:*, param2:Number, param3:Number, param4:int = -1) : Enemy
      {
         var _loc6_:* = undefined;
         var _loc7_:* = undefined;
         var _loc8_:* = undefined;
         var _loc5_:Enemy = GameUtility.getObjectOfEnemy("actors.enemies.Enemy" + param1,param2,param3);
         _loc5_.x = param2;
         _loc5_.y = param3;
         this._world._enemies.push(_loc5_);
         if(_loc5_ is Enemy24)
         {
            this.addChild(_loc5_);
         }
         else
         {
            _loc6_ = Boolean(ThreeKingdoms._gameWorld._heroes[0]) && !ThreeKingdoms._gameWorld._heroes[0].isDead() ? this.getChildIndex(Hero(ThreeKingdoms._gameWorld._heroes[0])) : this.numChildren;
            _loc7_ = this.numChildren;
            if(ThreeKingdoms._gameWorld._heroes.length == 2)
            {
               if(Boolean(ThreeKingdoms._gameWorld._heroes[1]) && !ThreeKingdoms._gameWorld._heroes[1].isDead())
               {
                  _loc7_ = this.getChildIndex(Hero(ThreeKingdoms._gameWorld._heroes[1]));
               }
            }
            _loc8_ = Math.min(_loc6_,_loc7_);
            this.addChildAt(_loc5_,_loc8_);
         }
         _loc5_._patrolPoint = 680 + 960 * (ThreeKingdoms._instance._viewController.getCurrentScreen() - 1) + Math.round(Math.random() * 100);
         return _loc5_;
      }
      
      public function detory() : void
      {
         GameUtility.clearDisplayList(this);
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         this._world._eventManager.removeEventListener(GameEvent.ENEMY_IS_ATTACK_BY_BULLET,this.onEnemyIsAttackByBulletHandler);
         this._world._eventManager.removeEventListener(GameEvent.HERO_IS_UNDER_ATTACK_BY_BULLET,this.onHeroIsUnderAttackByBulletHandler);
         this._world.stop();
         this._world.destroy();
      }
      
      public function gameOver() : void
      {
         var _loc1_:uint = this._world._heroes.length;
         if(_loc1_ == 1)
         {
            if(this._role1 && this._role1.isDead() || this._role2 && this._role2.isDead())
            {
               this._world._eventManager.dispatchEvent(new GameEvent(GameEvent.HERO_DEAD));
            }
         }
         else if(this._role1.isDead() && this._role2.isDead())
         {
            this._world._eventManager.dispatchEvent(new GameEvent(GameEvent.HERO_DEAD));
         }
      }
      
      internal function __setProp___id0__() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 1)
         {
            this.__setPropDict[this.__id0_] = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 0;
            this.__id0_.enemyType = 1;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___1() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 2)
         {
            this.__setPropDict[this.__id0_] = 2;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 0;
            this.__id0_.enemyType = 2;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___2() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 3)
         {
            this.__setPropDict[this.__id0_] = 3;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 0;
            this.__id0_.enemyType = 6;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___3() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 4)
         {
            this.__setPropDict[this.__id0_] = 4;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 0;
            this.__id0_.enemyType = 8;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___4() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 5)
         {
            this.__setPropDict[this.__id0_] = 5;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 0;
            this.__id0_.enemyType = 10;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___5() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 6)
         {
            this.__setPropDict[this.__id0_] = 6;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 0;
            this.__id0_.enemyType = 15;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___6() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 7)
         {
            this.__setPropDict[this.__id0_] = 7;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 0;
            this.__id0_.enemyType = 19;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___7() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 8)
         {
            this.__setPropDict[this.__id0_] = 8;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 0;
            this.__id0_.enemyType = 26;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___8() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 9)
         {
            this.__setPropDict[this.__id0_] = 9;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 2;
            this.__id0_.enemyType = 21;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 1;
            this.__id0_.totalNum = 3;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___9() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 10)
         {
            this.__setPropDict[this.__id0_] = 10;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 2;
            this.__id0_.enemyType = 34;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 1;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___10() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 11)
         {
            this.__setPropDict[this.__id0_] = 11;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 1;
            this.__id0_.enemyType = 27;
            this.__id0_.interval = 2;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___11() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 12)
         {
            this.__setPropDict[this.__id0_] = 12;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 0;
            this.__id0_.enemyType = 30;
            this.__id0_.interval = 2;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0___12() : *
      {
         if(this.__setPropDict[this.__id0_] == undefined || int(this.__setPropDict[this.__id0_]) != 13)
         {
            this.__setPropDict[this.__id0_] = 13;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.delay = 0;
            this.__id0_.enemyType = 35;
            this.__id0_.interval = 1;
            this.__id0_.isRandom = false;
            this.__id0_.stopPointIdx = 0;
            this.__id0_.totalNum = 1;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1__() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 1)
         {
            this.__setPropDict[this.__id1_] = 1;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 1;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___13() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 2)
         {
            this.__setPropDict[this.__id1_] = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 2;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___14() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 3)
         {
            this.__setPropDict[this.__id1_] = 3;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 6;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___15() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 4)
         {
            this.__setPropDict[this.__id1_] = 4;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 8;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___16() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 5)
         {
            this.__setPropDict[this.__id1_] = 5;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 10;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___17() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 6)
         {
            this.__setPropDict[this.__id1_] = 6;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 15;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___18() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 7)
         {
            this.__setPropDict[this.__id1_] = 7;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 19;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___19() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 8)
         {
            this.__setPropDict[this.__id1_] = 8;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 26;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___20() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 9)
         {
            this.__setPropDict[this.__id1_] = 9;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 9;
            this.__id1_.enemyType = 22;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 3;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___21() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 10)
         {
            this.__setPropDict[this.__id1_] = 10;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 0;
            this.__id1_.enemyType = 13;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___22() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 11)
         {
            this.__setPropDict[this.__id1_] = 11;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 1;
            this.__id1_.enemyType = 28;
            this.__id1_.interval = 2;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 1;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___23() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 12)
         {
            this.__setPropDict[this.__id1_] = 12;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 0;
            this.__id1_.enemyType = 31;
            this.__id1_.interval = 2;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 1;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1___24() : *
      {
         if(this.__setPropDict[this.__id1_] == undefined || int(this.__setPropDict[this.__id1_]) != 13)
         {
            this.__setPropDict[this.__id1_] = 13;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 35;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 3;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2__() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 1)
         {
            this.__setPropDict[this.__id2_] = 1;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 2;
            this.__id2_.enemyType = 1;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 1;
            this.__id2_.totalNum = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2___25() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 2)
         {
            this.__setPropDict[this.__id2_] = 2;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 2;
            this.__id2_.enemyType = 2;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 1;
            this.__id2_.totalNum = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2___26() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 3)
         {
            this.__setPropDict[this.__id2_] = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 2;
            this.__id2_.enemyType = 6;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 1;
            this.__id2_.totalNum = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2___27() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 4)
         {
            this.__setPropDict[this.__id2_] = 4;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 2;
            this.__id2_.enemyType = 8;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 1;
            this.__id2_.totalNum = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2___28() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 5)
         {
            this.__setPropDict[this.__id2_] = 5;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 2;
            this.__id2_.enemyType = 10;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 1;
            this.__id2_.totalNum = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2___29() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 6)
         {
            this.__setPropDict[this.__id2_] = 6;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 2;
            this.__id2_.enemyType = 15;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 1;
            this.__id2_.totalNum = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2___30() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 7)
         {
            this.__setPropDict[this.__id2_] = 7;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 2;
            this.__id2_.enemyType = 19;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 1;
            this.__id2_.totalNum = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2___31() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 8)
         {
            this.__setPropDict[this.__id2_] = 8;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 2;
            this.__id2_.enemyType = 26;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 1;
            this.__id2_.totalNum = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2___32() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 9)
         {
            this.__setPropDict[this.__id2_] = 9;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 0;
            this.__id2_.enemyType = 21;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 2;
            this.__id2_.totalNum = 1;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2___33() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 10)
         {
            this.__setPropDict[this.__id2_] = 10;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 0;
            this.__id2_.enemyType = 10;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 0;
            this.__id2_.totalNum = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2___34() : *
      {
         if(this.__setPropDict[this.__id2_] == undefined || int(this.__setPropDict[this.__id2_]) != 11)
         {
            this.__setPropDict[this.__id2_] = 11;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 1;
            this.__id2_.enemyType = 29;
            this.__id2_.interval = 2;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 2;
            this.__id2_.totalNum = 1;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3__() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 1)
         {
            this.__setPropDict[this.__id3_] = 1;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 1;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 1;
            this.__id3_.totalNum = 1;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3___35() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 2)
         {
            this.__setPropDict[this.__id3_] = 2;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 2;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 1;
            this.__id3_.totalNum = 1;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3___36() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 3)
         {
            this.__setPropDict[this.__id3_] = 3;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 6;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 1;
            this.__id3_.totalNum = 1;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3___37() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 4)
         {
            this.__setPropDict[this.__id3_] = 4;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 8;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 1;
            this.__id3_.totalNum = 1;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3___38() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 5)
         {
            this.__setPropDict[this.__id3_] = 5;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 12;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 1;
            this.__id3_.totalNum = 1;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3___39() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 6)
         {
            this.__setPropDict[this.__id3_] = 6;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 15;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 1;
            this.__id3_.totalNum = 1;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3___40() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 7)
         {
            this.__setPropDict[this.__id3_] = 7;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 19;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 1;
            this.__id3_.totalNum = 1;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3___41() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 8)
         {
            this.__setPropDict[this.__id3_] = 8;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 26;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 1;
            this.__id3_.totalNum = 1;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3___42() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 9)
         {
            this.__setPropDict[this.__id3_] = 9;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 21;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 2;
            this.__id3_.totalNum = 3;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3___43() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 10)
         {
            this.__setPropDict[this.__id3_] = 10;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 10;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 0;
            this.__id3_.totalNum = 2;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3___44() : *
      {
         if(this.__setPropDict[this.__id3_] == undefined || int(this.__setPropDict[this.__id3_]) != 11)
         {
            this.__setPropDict[this.__id3_] = 11;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 1;
            this.__id3_.enemyType = 15;
            this.__id3_.interval = 2;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 0;
            this.__id3_.totalNum = 3;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4__() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 1)
         {
            this.__setPropDict[this.__id4_] = 1;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 9;
            this.__id4_.enemyType = 2;
            this.__id4_.interval = 1;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 1;
            this.__id4_.totalNum = 3;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4___45() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 2)
         {
            this.__setPropDict[this.__id4_] = 2;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 9;
            this.__id4_.enemyType = 4;
            this.__id4_.interval = 1;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 1;
            this.__id4_.totalNum = 3;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4___46() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 3)
         {
            this.__setPropDict[this.__id4_] = 3;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 9;
            this.__id4_.enemyType = 7;
            this.__id4_.interval = 1;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 1;
            this.__id4_.totalNum = 3;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4___47() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 4)
         {
            this.__setPropDict[this.__id4_] = 4;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 9;
            this.__id4_.enemyType = 25;
            this.__id4_.interval = 1;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 1;
            this.__id4_.totalNum = 3;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4___48() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 5)
         {
            this.__setPropDict[this.__id4_] = 5;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 9;
            this.__id4_.enemyType = 13;
            this.__id4_.interval = 1;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 1;
            this.__id4_.totalNum = 3;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4___49() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 6)
         {
            this.__setPropDict[this.__id4_] = 6;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 9;
            this.__id4_.enemyType = 16;
            this.__id4_.interval = 1;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 1;
            this.__id4_.totalNum = 3;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4___50() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 7)
         {
            this.__setPropDict[this.__id4_] = 7;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 9;
            this.__id4_.enemyType = 26;
            this.__id4_.interval = 1;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 1;
            this.__id4_.totalNum = 3;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4___51() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 8)
         {
            this.__setPropDict[this.__id4_] = 8;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 9;
            this.__id4_.enemyType = 21;
            this.__id4_.interval = 1;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 1;
            this.__id4_.totalNum = 2;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4___52() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 9)
         {
            this.__setPropDict[this.__id4_] = 9;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 0;
            this.__id4_.enemyType = 21;
            this.__id4_.interval = 1;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 1;
            this.__id4_.totalNum = 1;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4___53() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 10)
         {
            this.__setPropDict[this.__id4_] = 10;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 3;
            this.__id4_.enemyType = 10;
            this.__id4_.interval = 1;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 0;
            this.__id4_.totalNum = 1;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4___54() : *
      {
         if(this.__setPropDict[this.__id4_] == undefined || int(this.__setPropDict[this.__id4_]) != 11)
         {
            this.__setPropDict[this.__id4_] = 11;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.delay = 1;
            this.__id4_.enemyType = 15;
            this.__id4_.interval = 2;
            this.__id4_.isRandom = false;
            this.__id4_.stopPointIdx = 0;
            this.__id4_.totalNum = 3;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5__() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 1)
         {
            this.__setPropDict[this.__id5_] = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 0;
            this.__id5_.enemyType = 2;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 2;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5___55() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 2)
         {
            this.__setPropDict[this.__id5_] = 2;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 0;
            this.__id5_.enemyType = 4;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 2;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5___56() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 3)
         {
            this.__setPropDict[this.__id5_] = 3;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 0;
            this.__id5_.enemyType = 7;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 2;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5___57() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 4)
         {
            this.__setPropDict[this.__id5_] = 4;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 0;
            this.__id5_.enemyType = 25;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 2;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5___58() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 5)
         {
            this.__setPropDict[this.__id5_] = 5;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 0;
            this.__id5_.enemyType = 12;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 2;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5___59() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 6)
         {
            this.__setPropDict[this.__id5_] = 6;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 0;
            this.__id5_.enemyType = 16;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 2;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5___60() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 7)
         {
            this.__setPropDict[this.__id5_] = 7;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 0;
            this.__id5_.enemyType = 26;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 2;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5___61() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 8)
         {
            this.__setPropDict[this.__id5_] = 8;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 0;
            this.__id5_.enemyType = 20;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 2;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5___62() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 9)
         {
            this.__setPropDict[this.__id5_] = 9;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 2;
            this.__id5_.enemyType = 21;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 4;
            this.__id5_.totalNum = 2;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5___63() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 10)
         {
            this.__setPropDict[this.__id5_] = 10;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 0;
            this.__id5_.enemyType = 10;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 0;
            this.__id5_.totalNum = 2;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5___64() : *
      {
         if(this.__setPropDict[this.__id5_] == undefined || int(this.__setPropDict[this.__id5_]) != 11)
         {
            this.__setPropDict[this.__id5_] = 11;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 1;
            this.__id5_.enemyType = 16;
            this.__id5_.interval = 2;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 1;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6__() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 1)
         {
            this.__setPropDict[this.__id6_] = 1;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 2;
            this.__id6_.enemyType = 2;
            this.__id6_.interval = 1;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 2;
            this.__id6_.totalNum = 3;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6___65() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 2)
         {
            this.__setPropDict[this.__id6_] = 2;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 2;
            this.__id6_.enemyType = 4;
            this.__id6_.interval = 1;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 2;
            this.__id6_.totalNum = 3;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6___66() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 3)
         {
            this.__setPropDict[this.__id6_] = 3;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 2;
            this.__id6_.enemyType = 7;
            this.__id6_.interval = 1;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 2;
            this.__id6_.totalNum = 3;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6___67() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 4)
         {
            this.__setPropDict[this.__id6_] = 4;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 2;
            this.__id6_.enemyType = 25;
            this.__id6_.interval = 1;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 2;
            this.__id6_.totalNum = 3;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6___68() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 5)
         {
            this.__setPropDict[this.__id6_] = 5;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 2;
            this.__id6_.enemyType = 10;
            this.__id6_.interval = 1;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 2;
            this.__id6_.totalNum = 3;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6___69() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 6)
         {
            this.__setPropDict[this.__id6_] = 6;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 2;
            this.__id6_.enemyType = 16;
            this.__id6_.interval = 1;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 2;
            this.__id6_.totalNum = 3;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6___70() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 7)
         {
            this.__setPropDict[this.__id6_] = 7;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 2;
            this.__id6_.enemyType = 26;
            this.__id6_.interval = 1;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 2;
            this.__id6_.totalNum = 3;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6___71() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 8)
         {
            this.__setPropDict[this.__id6_] = 8;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 2;
            this.__id6_.enemyType = 20;
            this.__id6_.interval = 1;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 2;
            this.__id6_.totalNum = 3;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6___72() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 9)
         {
            this.__setPropDict[this.__id6_] = 9;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 2;
            this.__id6_.enemyType = 21;
            this.__id6_.interval = 1;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 3;
            this.__id6_.totalNum = 2;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6___73() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 10)
         {
            this.__setPropDict[this.__id6_] = 10;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 2;
            this.__id6_.enemyType = 10;
            this.__id6_.interval = 1;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 0;
            this.__id6_.totalNum = 5;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6___74() : *
      {
         if(this.__setPropDict[this.__id6_] == undefined || int(this.__setPropDict[this.__id6_]) != 11)
         {
            this.__setPropDict[this.__id6_] = 11;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.delay = 1;
            this.__id6_.enemyType = 16;
            this.__id6_.interval = 2;
            this.__id6_.isRandom = false;
            this.__id6_.stopPointIdx = 1;
            this.__id6_.totalNum = 2;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7__() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 1)
         {
            this.__setPropDict[this.__id7_] = 1;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 0;
            this.__id7_.enemyType = 1;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 1;
            this.__id7_.totalNum = 1;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7___75() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 2)
         {
            this.__setPropDict[this.__id7_] = 2;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 0;
            this.__id7_.enemyType = 2;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 1;
            this.__id7_.totalNum = 1;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7___76() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 3)
         {
            this.__setPropDict[this.__id7_] = 3;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 0;
            this.__id7_.enemyType = 6;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 1;
            this.__id7_.totalNum = 1;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7___77() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 4)
         {
            this.__setPropDict[this.__id7_] = 4;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 0;
            this.__id7_.enemyType = 8;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 1;
            this.__id7_.totalNum = 1;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7___78() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 5)
         {
            this.__setPropDict[this.__id7_] = 5;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 0;
            this.__id7_.enemyType = 10;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 1;
            this.__id7_.totalNum = 1;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7___79() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 6)
         {
            this.__setPropDict[this.__id7_] = 6;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 0;
            this.__id7_.enemyType = 15;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 1;
            this.__id7_.totalNum = 1;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7___80() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 7)
         {
            this.__setPropDict[this.__id7_] = 7;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 0;
            this.__id7_.enemyType = 19;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 1;
            this.__id7_.totalNum = 1;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7___81() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 8)
         {
            this.__setPropDict[this.__id7_] = 8;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 0;
            this.__id7_.enemyType = 26;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 1;
            this.__id7_.totalNum = 1;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7___82() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 9)
         {
            this.__setPropDict[this.__id7_] = 9;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 0;
            this.__id7_.enemyType = 22;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 3;
            this.__id7_.totalNum = 3;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7___83() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 10)
         {
            this.__setPropDict[this.__id7_] = 10;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 3;
            this.__id7_.enemyType = 10;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 0;
            this.__id7_.totalNum = 3;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7___84() : *
      {
         if(this.__setPropDict[this.__id7_] == undefined || int(this.__setPropDict[this.__id7_]) != 11)
         {
            this.__setPropDict[this.__id7_] = 11;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 3;
            this.__id7_.enemyType = 19;
            this.__id7_.interval = 2;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 2;
            this.__id7_.totalNum = 3;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8__() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 1)
         {
            this.__setPropDict[this.__id8_] = 1;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 2;
            this.__id8_.enemyType = 2;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 4;
            this.__id8_.totalNum = 2;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8___85() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 2)
         {
            this.__setPropDict[this.__id8_] = 2;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 2;
            this.__id8_.enemyType = 4;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 4;
            this.__id8_.totalNum = 2;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8___86() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 3)
         {
            this.__setPropDict[this.__id8_] = 3;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 2;
            this.__id8_.enemyType = 7;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 4;
            this.__id8_.totalNum = 2;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8___87() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 4)
         {
            this.__setPropDict[this.__id8_] = 4;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 2;
            this.__id8_.enemyType = 10;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 4;
            this.__id8_.totalNum = 2;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8___88() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 5)
         {
            this.__setPropDict[this.__id8_] = 5;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 2;
            this.__id8_.enemyType = 12;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 4;
            this.__id8_.totalNum = 2;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8___89() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 6)
         {
            this.__setPropDict[this.__id8_] = 6;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 2;
            this.__id8_.enemyType = 16;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 4;
            this.__id8_.totalNum = 2;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8___90() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 7)
         {
            this.__setPropDict[this.__id8_] = 7;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 2;
            this.__id8_.enemyType = 26;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 4;
            this.__id8_.totalNum = 2;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8___91() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 8)
         {
            this.__setPropDict[this.__id8_] = 8;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 2;
            this.__id8_.enemyType = 20;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 4;
            this.__id8_.totalNum = 2;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8___92() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 9)
         {
            this.__setPropDict[this.__id8_] = 9;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 0;
            this.__id8_.enemyType = 21;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 4;
            this.__id8_.totalNum = 2;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8___93() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 10)
         {
            this.__setPropDict[this.__id8_] = 10;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 2;
            this.__id8_.enemyType = 13;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 1;
            this.__id8_.totalNum = 1;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8___94() : *
      {
         if(this.__setPropDict[this.__id8_] == undefined || int(this.__setPropDict[this.__id8_]) != 11)
         {
            this.__setPropDict[this.__id8_] = 11;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 2;
            this.__id8_.enemyType = 19;
            this.__id8_.interval = 2;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 2;
            this.__id8_.totalNum = 3;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9__() : *
      {
         if(this.__setPropDict[this.__id9_] == undefined || int(this.__setPropDict[this.__id9_]) != 1)
         {
            this.__setPropDict[this.__id9_] = 1;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 2;
            this.__id9_.enemyType = 2;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 3;
            this.__id9_.totalNum = 2;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9___95() : *
      {
         if(this.__setPropDict[this.__id9_] == undefined || int(this.__setPropDict[this.__id9_]) != 2)
         {
            this.__setPropDict[this.__id9_] = 2;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 2;
            this.__id9_.enemyType = 4;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 3;
            this.__id9_.totalNum = 2;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9___96() : *
      {
         if(this.__setPropDict[this.__id9_] == undefined || int(this.__setPropDict[this.__id9_]) != 3)
         {
            this.__setPropDict[this.__id9_] = 3;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 2;
            this.__id9_.enemyType = 7;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 3;
            this.__id9_.totalNum = 2;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9___97() : *
      {
         if(this.__setPropDict[this.__id9_] == undefined || int(this.__setPropDict[this.__id9_]) != 4)
         {
            this.__setPropDict[this.__id9_] = 4;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 2;
            this.__id9_.enemyType = 25;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 3;
            this.__id9_.totalNum = 2;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9___98() : *
      {
         if(this.__setPropDict[this.__id9_] == undefined || int(this.__setPropDict[this.__id9_]) != 5)
         {
            this.__setPropDict[this.__id9_] = 5;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 2;
            this.__id9_.enemyType = 12;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 3;
            this.__id9_.totalNum = 2;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9___99() : *
      {
         if(this.__setPropDict[this.__id9_] == undefined || int(this.__setPropDict[this.__id9_]) != 6)
         {
            this.__setPropDict[this.__id9_] = 6;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 2;
            this.__id9_.enemyType = 16;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 3;
            this.__id9_.totalNum = 2;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9___100() : *
      {
         if(this.__setPropDict[this.__id9_] == undefined || int(this.__setPropDict[this.__id9_]) != 7)
         {
            this.__setPropDict[this.__id9_] = 7;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 2;
            this.__id9_.enemyType = 26;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 3;
            this.__id9_.totalNum = 2;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9___101() : *
      {
         if(this.__setPropDict[this.__id9_] == undefined || int(this.__setPropDict[this.__id9_]) != 8)
         {
            this.__setPropDict[this.__id9_] = 8;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 2;
            this.__id9_.enemyType = 20;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 3;
            this.__id9_.totalNum = 2;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9___102() : *
      {
         if(this.__setPropDict[this.__id9_] == undefined || int(this.__setPropDict[this.__id9_]) != 9)
         {
            this.__setPropDict[this.__id9_] = 9;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 7;
            this.__id9_.enemyType = 23;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 4;
            this.__id9_.totalNum = 1;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9___103() : *
      {
         if(this.__setPropDict[this.__id9_] == undefined || int(this.__setPropDict[this.__id9_]) != 10)
         {
            this.__setPropDict[this.__id9_] = 10;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 1;
            this.__id9_.enemyType = 13;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 1;
            this.__id9_.totalNum = 4;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10__() : *
      {
         if(this.__setPropDict[this.__id10_] == undefined || int(this.__setPropDict[this.__id10_]) != 1)
         {
            this.__setPropDict[this.__id10_] = 1;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 0;
            this.__id10_.enemyType = 2;
            this.__id10_.interval = 1;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 3;
            this.__id10_.totalNum = 3;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10___104() : *
      {
         if(this.__setPropDict[this.__id10_] == undefined || int(this.__setPropDict[this.__id10_]) != 2)
         {
            this.__setPropDict[this.__id10_] = 2;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 0;
            this.__id10_.enemyType = 4;
            this.__id10_.interval = 1;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 3;
            this.__id10_.totalNum = 3;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10___105() : *
      {
         if(this.__setPropDict[this.__id10_] == undefined || int(this.__setPropDict[this.__id10_]) != 3)
         {
            this.__setPropDict[this.__id10_] = 3;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 0;
            this.__id10_.enemyType = 7;
            this.__id10_.interval = 1;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 3;
            this.__id10_.totalNum = 3;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10___106() : *
      {
         if(this.__setPropDict[this.__id10_] == undefined || int(this.__setPropDict[this.__id10_]) != 4)
         {
            this.__setPropDict[this.__id10_] = 4;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 0;
            this.__id10_.enemyType = 25;
            this.__id10_.interval = 1;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 3;
            this.__id10_.totalNum = 3;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10___107() : *
      {
         if(this.__setPropDict[this.__id10_] == undefined || int(this.__setPropDict[this.__id10_]) != 5)
         {
            this.__setPropDict[this.__id10_] = 5;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 0;
            this.__id10_.enemyType = 12;
            this.__id10_.interval = 1;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 3;
            this.__id10_.totalNum = 3;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10___108() : *
      {
         if(this.__setPropDict[this.__id10_] == undefined || int(this.__setPropDict[this.__id10_]) != 6)
         {
            this.__setPropDict[this.__id10_] = 6;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 0;
            this.__id10_.enemyType = 16;
            this.__id10_.interval = 1;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 3;
            this.__id10_.totalNum = 3;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10___109() : *
      {
         if(this.__setPropDict[this.__id10_] == undefined || int(this.__setPropDict[this.__id10_]) != 7)
         {
            this.__setPropDict[this.__id10_] = 7;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 0;
            this.__id10_.enemyType = 26;
            this.__id10_.interval = 1;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 3;
            this.__id10_.totalNum = 3;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10___110() : *
      {
         if(this.__setPropDict[this.__id10_] == undefined || int(this.__setPropDict[this.__id10_]) != 8)
         {
            this.__setPropDict[this.__id10_] = 8;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 0;
            this.__id10_.enemyType = 20;
            this.__id10_.interval = 1;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 3;
            this.__id10_.totalNum = 3;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10___111() : *
      {
         if(this.__setPropDict[this.__id10_] == undefined || int(this.__setPropDict[this.__id10_]) != 9)
         {
            this.__setPropDict[this.__id10_] = 9;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 3;
            this.__id10_.enemyType = 21;
            this.__id10_.interval = 1;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 2;
            this.__id10_.totalNum = 2;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10___112() : *
      {
         if(this.__setPropDict[this.__id10_] == undefined || int(this.__setPropDict[this.__id10_]) != 10)
         {
            this.__setPropDict[this.__id10_] = 10;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 0;
            this.__id10_.enemyType = 33;
            this.__id10_.interval = 1;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 1;
            this.__id10_.totalNum = 1;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11__() : *
      {
         if(this.__setPropDict[this.__id11_] == undefined || int(this.__setPropDict[this.__id11_]) != 1)
         {
            this.__setPropDict[this.__id11_] = 1;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.delay = 0;
            this.__id11_.enemyType = 2;
            this.__id11_.interval = 1;
            this.__id11_.isRandom = false;
            this.__id11_.stopPointIdx = 4;
            this.__id11_.totalNum = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11___113() : *
      {
         if(this.__setPropDict[this.__id11_] == undefined || int(this.__setPropDict[this.__id11_]) != 2)
         {
            this.__setPropDict[this.__id11_] = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.delay = 0;
            this.__id11_.enemyType = 4;
            this.__id11_.interval = 1;
            this.__id11_.isRandom = false;
            this.__id11_.stopPointIdx = 4;
            this.__id11_.totalNum = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11___114() : *
      {
         if(this.__setPropDict[this.__id11_] == undefined || int(this.__setPropDict[this.__id11_]) != 3)
         {
            this.__setPropDict[this.__id11_] = 3;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.delay = 0;
            this.__id11_.enemyType = 7;
            this.__id11_.interval = 1;
            this.__id11_.isRandom = false;
            this.__id11_.stopPointIdx = 4;
            this.__id11_.totalNum = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11___115() : *
      {
         if(this.__setPropDict[this.__id11_] == undefined || int(this.__setPropDict[this.__id11_]) != 4)
         {
            this.__setPropDict[this.__id11_] = 4;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.delay = 0;
            this.__id11_.enemyType = 10;
            this.__id11_.interval = 1;
            this.__id11_.isRandom = false;
            this.__id11_.stopPointIdx = 4;
            this.__id11_.totalNum = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11___116() : *
      {
         if(this.__setPropDict[this.__id11_] == undefined || int(this.__setPropDict[this.__id11_]) != 5)
         {
            this.__setPropDict[this.__id11_] = 5;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.delay = 0;
            this.__id11_.enemyType = 12;
            this.__id11_.interval = 1;
            this.__id11_.isRandom = false;
            this.__id11_.stopPointIdx = 4;
            this.__id11_.totalNum = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11___117() : *
      {
         if(this.__setPropDict[this.__id11_] == undefined || int(this.__setPropDict[this.__id11_]) != 6)
         {
            this.__setPropDict[this.__id11_] = 6;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.delay = 0;
            this.__id11_.enemyType = 16;
            this.__id11_.interval = 1;
            this.__id11_.isRandom = false;
            this.__id11_.stopPointIdx = 4;
            this.__id11_.totalNum = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11___118() : *
      {
         if(this.__setPropDict[this.__id11_] == undefined || int(this.__setPropDict[this.__id11_]) != 7)
         {
            this.__setPropDict[this.__id11_] = 7;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.delay = 0;
            this.__id11_.enemyType = 26;
            this.__id11_.interval = 1;
            this.__id11_.isRandom = false;
            this.__id11_.stopPointIdx = 4;
            this.__id11_.totalNum = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11___119() : *
      {
         if(this.__setPropDict[this.__id11_] == undefined || int(this.__setPropDict[this.__id11_]) != 8)
         {
            this.__setPropDict[this.__id11_] = 8;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.delay = 0;
            this.__id11_.enemyType = 20;
            this.__id11_.interval = 1;
            this.__id11_.isRandom = false;
            this.__id11_.stopPointIdx = 4;
            this.__id11_.totalNum = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11___120() : *
      {
         if(this.__setPropDict[this.__id11_] == undefined || int(this.__setPropDict[this.__id11_]) != 9)
         {
            this.__setPropDict[this.__id11_] = 9;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.delay = 3;
            this.__id11_.enemyType = 22;
            this.__id11_.interval = 1;
            this.__id11_.isRandom = false;
            this.__id11_.stopPointIdx = 3;
            this.__id11_.totalNum = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11___121() : *
      {
         if(this.__setPropDict[this.__id11_] == undefined || int(this.__setPropDict[this.__id11_]) != 10)
         {
            this.__setPropDict[this.__id11_] = 10;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.delay = 0;
            this.__id11_.enemyType = 13;
            this.__id11_.interval = 1;
            this.__id11_.isRandom = false;
            this.__id11_.stopPointIdx = 1;
            this.__id11_.totalNum = 2;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12__() : *
      {
         if(this.__setPropDict[this.__id12_] == undefined || int(this.__setPropDict[this.__id12_]) != 1)
         {
            this.__setPropDict[this.__id12_] = 1;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 7;
            this.__id12_.enemyType = 3;
            this.__id12_.interval = 1;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 4;
            this.__id12_.totalNum = 1;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12___122() : *
      {
         if(this.__setPropDict[this.__id12_] == undefined || int(this.__setPropDict[this.__id12_]) != 2)
         {
            this.__setPropDict[this.__id12_] = 2;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 7;
            this.__id12_.enemyType = 5;
            this.__id12_.interval = 1;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 4;
            this.__id12_.totalNum = 1;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12___123() : *
      {
         if(this.__setPropDict[this.__id12_] == undefined || int(this.__setPropDict[this.__id12_]) != 3)
         {
            this.__setPropDict[this.__id12_] = 3;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 7;
            this.__id12_.enemyType = 9;
            this.__id12_.interval = 1;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 4;
            this.__id12_.totalNum = 1;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12___124() : *
      {
         if(this.__setPropDict[this.__id12_] == undefined || int(this.__setPropDict[this.__id12_]) != 4)
         {
            this.__setPropDict[this.__id12_] = 4;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 7;
            this.__id12_.enemyType = 11;
            this.__id12_.interval = 1;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 4;
            this.__id12_.totalNum = 1;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12___125() : *
      {
         if(this.__setPropDict[this.__id12_] == undefined || int(this.__setPropDict[this.__id12_]) != 5)
         {
            this.__setPropDict[this.__id12_] = 5;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 7;
            this.__id12_.enemyType = 14;
            this.__id12_.interval = 1;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 4;
            this.__id12_.totalNum = 1;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12___126() : *
      {
         if(this.__setPropDict[this.__id12_] == undefined || int(this.__setPropDict[this.__id12_]) != 6)
         {
            this.__setPropDict[this.__id12_] = 6;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 7;
            this.__id12_.enemyType = 17;
            this.__id12_.interval = 1;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 4;
            this.__id12_.totalNum = 1;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12___127() : *
      {
         if(this.__setPropDict[this.__id12_] == undefined || int(this.__setPropDict[this.__id12_]) != 7)
         {
            this.__setPropDict[this.__id12_] = 7;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 7;
            this.__id12_.enemyType = 22;
            this.__id12_.interval = 1;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 4;
            this.__id12_.totalNum = 1;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12___128() : *
      {
         if(this.__setPropDict[this.__id12_] == undefined || int(this.__setPropDict[this.__id12_]) != 8)
         {
            this.__setPropDict[this.__id12_] = 8;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 7;
            this.__id12_.enemyType = 24;
            this.__id12_.interval = 1;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 4;
            this.__id12_.totalNum = 1;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12___129() : *
      {
         if(this.__setPropDict[this.__id12_] == undefined || int(this.__setPropDict[this.__id12_]) != 9)
         {
            this.__setPropDict[this.__id12_] = 9;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 9;
            this.__id12_.enemyType = 21;
            this.__id12_.interval = 1;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 3;
            this.__id12_.totalNum = 3;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12___130() : *
      {
         if(this.__setPropDict[this.__id12_] == undefined || int(this.__setPropDict[this.__id12_]) != 10)
         {
            this.__setPropDict[this.__id12_] = 10;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 0;
            this.__id12_.enemyType = 13;
            this.__id12_.interval = 1;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 1;
            this.__id12_.totalNum = 4;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13__() : *
      {
         if(this.__setPropDict[this.__id13_] == undefined || int(this.__setPropDict[this.__id13_]) != 1)
         {
            this.__setPropDict[this.__id13_] = 1;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 3;
            this.__id13_.enemyType = 2;
            this.__id13_.interval = 1;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 2;
            this.__id13_.totalNum = 2;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13___131() : *
      {
         if(this.__setPropDict[this.__id13_] == undefined || int(this.__setPropDict[this.__id13_]) != 2)
         {
            this.__setPropDict[this.__id13_] = 2;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 3;
            this.__id13_.enemyType = 4;
            this.__id13_.interval = 1;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 2;
            this.__id13_.totalNum = 2;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13___132() : *
      {
         if(this.__setPropDict[this.__id13_] == undefined || int(this.__setPropDict[this.__id13_]) != 3)
         {
            this.__setPropDict[this.__id13_] = 3;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 3;
            this.__id13_.enemyType = 7;
            this.__id13_.interval = 1;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 2;
            this.__id13_.totalNum = 2;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13___133() : *
      {
         if(this.__setPropDict[this.__id13_] == undefined || int(this.__setPropDict[this.__id13_]) != 4)
         {
            this.__setPropDict[this.__id13_] = 4;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 3;
            this.__id13_.enemyType = 25;
            this.__id13_.interval = 1;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 2;
            this.__id13_.totalNum = 2;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13___134() : *
      {
         if(this.__setPropDict[this.__id13_] == undefined || int(this.__setPropDict[this.__id13_]) != 5)
         {
            this.__setPropDict[this.__id13_] = 5;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 3;
            this.__id13_.enemyType = 12;
            this.__id13_.interval = 1;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 2;
            this.__id13_.totalNum = 2;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13___135() : *
      {
         if(this.__setPropDict[this.__id13_] == undefined || int(this.__setPropDict[this.__id13_]) != 6)
         {
            this.__setPropDict[this.__id13_] = 6;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 3;
            this.__id13_.enemyType = 16;
            this.__id13_.interval = 1;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 2;
            this.__id13_.totalNum = 2;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13___136() : *
      {
         if(this.__setPropDict[this.__id13_] == undefined || int(this.__setPropDict[this.__id13_]) != 7)
         {
            this.__setPropDict[this.__id13_] = 7;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 3;
            this.__id13_.enemyType = 26;
            this.__id13_.interval = 1;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 2;
            this.__id13_.totalNum = 2;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13___137() : *
      {
         if(this.__setPropDict[this.__id13_] == undefined || int(this.__setPropDict[this.__id13_]) != 8)
         {
            this.__setPropDict[this.__id13_] = 8;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 3;
            this.__id13_.enemyType = 20;
            this.__id13_.interval = 1;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 2;
            this.__id13_.totalNum = 2;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13___138() : *
      {
         if(this.__setPropDict[this.__id13_] == undefined || int(this.__setPropDict[this.__id13_]) != 9)
         {
            this.__setPropDict[this.__id13_] = 9;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 3;
            this.__id13_.enemyType = 22;
            this.__id13_.interval = 1;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 0;
            this.__id13_.totalNum = 1;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13___139() : *
      {
         if(this.__setPropDict[this.__id13_] == undefined || int(this.__setPropDict[this.__id13_]) != 10)
         {
            this.__setPropDict[this.__id13_] = 10;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 0;
            this.__id13_.enemyType = 13;
            this.__id13_.interval = 1;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 1;
            this.__id13_.totalNum = 4;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14__() : *
      {
         if(this.__setPropDict[this.__id14_] == undefined || int(this.__setPropDict[this.__id14_]) != 1)
         {
            this.__setPropDict[this.__id14_] = 1;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 3;
            this.__id14_.enemyType = 2;
            this.__id14_.interval = 1;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 3;
            this.__id14_.totalNum = 2;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14___140() : *
      {
         if(this.__setPropDict[this.__id14_] == undefined || int(this.__setPropDict[this.__id14_]) != 2)
         {
            this.__setPropDict[this.__id14_] = 2;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 3;
            this.__id14_.enemyType = 4;
            this.__id14_.interval = 1;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 3;
            this.__id14_.totalNum = 2;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14___141() : *
      {
         if(this.__setPropDict[this.__id14_] == undefined || int(this.__setPropDict[this.__id14_]) != 3)
         {
            this.__setPropDict[this.__id14_] = 3;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 3;
            this.__id14_.enemyType = 7;
            this.__id14_.interval = 1;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 3;
            this.__id14_.totalNum = 2;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14___142() : *
      {
         if(this.__setPropDict[this.__id14_] == undefined || int(this.__setPropDict[this.__id14_]) != 4)
         {
            this.__setPropDict[this.__id14_] = 4;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 3;
            this.__id14_.enemyType = 25;
            this.__id14_.interval = 1;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 3;
            this.__id14_.totalNum = 2;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14___143() : *
      {
         if(this.__setPropDict[this.__id14_] == undefined || int(this.__setPropDict[this.__id14_]) != 5)
         {
            this.__setPropDict[this.__id14_] = 5;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 3;
            this.__id14_.enemyType = 13;
            this.__id14_.interval = 1;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 3;
            this.__id14_.totalNum = 2;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14___144() : *
      {
         if(this.__setPropDict[this.__id14_] == undefined || int(this.__setPropDict[this.__id14_]) != 6)
         {
            this.__setPropDict[this.__id14_] = 6;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 3;
            this.__id14_.enemyType = 16;
            this.__id14_.interval = 1;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 3;
            this.__id14_.totalNum = 2;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14___145() : *
      {
         if(this.__setPropDict[this.__id14_] == undefined || int(this.__setPropDict[this.__id14_]) != 7)
         {
            this.__setPropDict[this.__id14_] = 7;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 3;
            this.__id14_.enemyType = 26;
            this.__id14_.interval = 1;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 3;
            this.__id14_.totalNum = 2;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14___146() : *
      {
         if(this.__setPropDict[this.__id14_] == undefined || int(this.__setPropDict[this.__id14_]) != 8)
         {
            this.__setPropDict[this.__id14_] = 8;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 3;
            this.__id14_.enemyType = 20;
            this.__id14_.interval = 1;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 3;
            this.__id14_.totalNum = 2;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14___147() : *
      {
         if(this.__setPropDict[this.__id14_] == undefined || int(this.__setPropDict[this.__id14_]) != 9)
         {
            this.__setPropDict[this.__id14_] = 9;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 0;
            this.__id14_.enemyType = 21;
            this.__id14_.interval = 1;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 1;
            this.__id14_.totalNum = 3;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14___148() : *
      {
         if(this.__setPropDict[this.__id14_] == undefined || int(this.__setPropDict[this.__id14_]) != 10)
         {
            this.__setPropDict[this.__id14_] = 10;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 0;
            this.__id14_.enemyType = 13;
            this.__id14_.interval = 1;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 1;
            this.__id14_.totalNum = 4;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id15__() : *
      {
         if(this.__setPropDict[this.__id15_] == undefined || int(this.__setPropDict[this.__id15_]) != 1)
         {
            this.__setPropDict[this.__id15_] = 1;
            try
            {
               this.__id15_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id15_.delay = 9;
            this.__id15_.enemyType = 2;
            this.__id15_.interval = 1;
            this.__id15_.isRandom = false;
            this.__id15_.stopPointIdx = 3;
            this.__id15_.totalNum = 3;
            try
            {
               this.__id15_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id15___149() : *
      {
         if(this.__setPropDict[this.__id15_] == undefined || int(this.__setPropDict[this.__id15_]) != 2)
         {
            this.__setPropDict[this.__id15_] = 2;
            try
            {
               this.__id15_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id15_.delay = 9;
            this.__id15_.enemyType = 4;
            this.__id15_.interval = 1;
            this.__id15_.isRandom = false;
            this.__id15_.stopPointIdx = 3;
            this.__id15_.totalNum = 3;
            try
            {
               this.__id15_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id15___150() : *
      {
         if(this.__setPropDict[this.__id15_] == undefined || int(this.__setPropDict[this.__id15_]) != 3)
         {
            this.__setPropDict[this.__id15_] = 3;
            try
            {
               this.__id15_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id15_.delay = 9;
            this.__id15_.enemyType = 7;
            this.__id15_.interval = 1;
            this.__id15_.isRandom = false;
            this.__id15_.stopPointIdx = 3;
            this.__id15_.totalNum = 3;
            try
            {
               this.__id15_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id15___151() : *
      {
         if(this.__setPropDict[this.__id15_] == undefined || int(this.__setPropDict[this.__id15_]) != 4)
         {
            this.__setPropDict[this.__id15_] = 4;
            try
            {
               this.__id15_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id15_.delay = 9;
            this.__id15_.enemyType = 25;
            this.__id15_.interval = 1;
            this.__id15_.isRandom = false;
            this.__id15_.stopPointIdx = 3;
            this.__id15_.totalNum = 3;
            try
            {
               this.__id15_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id15___152() : *
      {
         if(this.__setPropDict[this.__id15_] == undefined || int(this.__setPropDict[this.__id15_]) != 5)
         {
            this.__setPropDict[this.__id15_] = 5;
            try
            {
               this.__id15_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id15_.delay = 9;
            this.__id15_.enemyType = 12;
            this.__id15_.interval = 1;
            this.__id15_.isRandom = false;
            this.__id15_.stopPointIdx = 3;
            this.__id15_.totalNum = 3;
            try
            {
               this.__id15_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id15___153() : *
      {
         if(this.__setPropDict[this.__id15_] == undefined || int(this.__setPropDict[this.__id15_]) != 6)
         {
            this.__setPropDict[this.__id15_] = 6;
            try
            {
               this.__id15_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id15_.delay = 9;
            this.__id15_.enemyType = 16;
            this.__id15_.interval = 1;
            this.__id15_.isRandom = false;
            this.__id15_.stopPointIdx = 3;
            this.__id15_.totalNum = 3;
            try
            {
               this.__id15_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id15___154() : *
      {
         if(this.__setPropDict[this.__id15_] == undefined || int(this.__setPropDict[this.__id15_]) != 7)
         {
            this.__setPropDict[this.__id15_] = 7;
            try
            {
               this.__id15_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id15_.delay = 9;
            this.__id15_.enemyType = 26;
            this.__id15_.interval = 1;
            this.__id15_.isRandom = false;
            this.__id15_.stopPointIdx = 3;
            this.__id15_.totalNum = 3;
            try
            {
               this.__id15_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id15___155() : *
      {
         if(this.__setPropDict[this.__id15_] == undefined || int(this.__setPropDict[this.__id15_]) != 8)
         {
            this.__setPropDict[this.__id15_] = 8;
            try
            {
               this.__id15_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id15_.delay = 9;
            this.__id15_.enemyType = 20;
            this.__id15_.interval = 1;
            this.__id15_.isRandom = false;
            this.__id15_.stopPointIdx = 3;
            this.__id15_.totalNum = 3;
            try
            {
               this.__id15_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id15___156() : *
      {
         if(this.__setPropDict[this.__id15_] == undefined || int(this.__setPropDict[this.__id15_]) != 9)
         {
            this.__setPropDict[this.__id15_] = 9;
            try
            {
               this.__id15_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id15_.delay = 2;
            this.__id15_.enemyType = 22;
            this.__id15_.interval = 1;
            this.__id15_.isRandom = false;
            this.__id15_.stopPointIdx = 1;
            this.__id15_.totalNum = 2;
            try
            {
               this.__id15_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id16__() : *
      {
         if(this.__setPropDict[this.__id16_] == undefined || int(this.__setPropDict[this.__id16_]) != 1)
         {
            this.__setPropDict[this.__id16_] = 1;
            try
            {
               this.__id16_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id16_.delay = 2;
            this.__id16_.enemyType = 1;
            this.__id16_.interval = 1;
            this.__id16_.isRandom = false;
            this.__id16_.stopPointIdx = 0;
            this.__id16_.totalNum = 2;
            try
            {
               this.__id16_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id16___157() : *
      {
         if(this.__setPropDict[this.__id16_] == undefined || int(this.__setPropDict[this.__id16_]) != 2)
         {
            this.__setPropDict[this.__id16_] = 2;
            try
            {
               this.__id16_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id16_.delay = 3;
            this.__id16_.enemyType = 4;
            this.__id16_.interval = 1;
            this.__id16_.isRandom = false;
            this.__id16_.stopPointIdx = 0;
            this.__id16_.totalNum = 1;
            try
            {
               this.__id16_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id16___158() : *
      {
         if(this.__setPropDict[this.__id16_] == undefined || int(this.__setPropDict[this.__id16_]) != 3)
         {
            this.__setPropDict[this.__id16_] = 3;
            try
            {
               this.__id16_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id16_.delay = 2;
            this.__id16_.enemyType = 7;
            this.__id16_.interval = 1;
            this.__id16_.isRandom = false;
            this.__id16_.stopPointIdx = 0;
            this.__id16_.totalNum = 1;
            try
            {
               this.__id16_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id16___159() : *
      {
         if(this.__setPropDict[this.__id16_] == undefined || int(this.__setPropDict[this.__id16_]) != 4)
         {
            this.__setPropDict[this.__id16_] = 4;
            try
            {
               this.__id16_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id16_.delay = 3;
            this.__id16_.enemyType = 25;
            this.__id16_.interval = 1;
            this.__id16_.isRandom = false;
            this.__id16_.stopPointIdx = 0;
            this.__id16_.totalNum = 1;
            try
            {
               this.__id16_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id16___160() : *
      {
         if(this.__setPropDict[this.__id16_] == undefined || int(this.__setPropDict[this.__id16_]) != 5)
         {
            this.__setPropDict[this.__id16_] = 5;
            try
            {
               this.__id16_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id16_.delay = 3;
            this.__id16_.enemyType = 13;
            this.__id16_.interval = 1;
            this.__id16_.isRandom = false;
            this.__id16_.stopPointIdx = 0;
            this.__id16_.totalNum = 1;
            try
            {
               this.__id16_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id16___161() : *
      {
         if(this.__setPropDict[this.__id16_] == undefined || int(this.__setPropDict[this.__id16_]) != 6)
         {
            this.__setPropDict[this.__id16_] = 6;
            try
            {
               this.__id16_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id16_.delay = 3;
            this.__id16_.enemyType = 16;
            this.__id16_.interval = 1;
            this.__id16_.isRandom = false;
            this.__id16_.stopPointIdx = 0;
            this.__id16_.totalNum = 1;
            try
            {
               this.__id16_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id16___162() : *
      {
         if(this.__setPropDict[this.__id16_] == undefined || int(this.__setPropDict[this.__id16_]) != 7)
         {
            this.__setPropDict[this.__id16_] = 7;
            try
            {
               this.__id16_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id16_.delay = 3;
            this.__id16_.enemyType = 26;
            this.__id16_.interval = 1;
            this.__id16_.isRandom = false;
            this.__id16_.stopPointIdx = 0;
            this.__id16_.totalNum = 1;
            try
            {
               this.__id16_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id16___163() : *
      {
         if(this.__setPropDict[this.__id16_] == undefined || int(this.__setPropDict[this.__id16_]) != 8)
         {
            this.__setPropDict[this.__id16_] = 8;
            try
            {
               this.__id16_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id16_.delay = 3;
            this.__id16_.enemyType = 20;
            this.__id16_.interval = 1;
            this.__id16_.isRandom = false;
            this.__id16_.stopPointIdx = 0;
            this.__id16_.totalNum = 1;
            try
            {
               this.__id16_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id16___164() : *
      {
         if(this.__setPropDict[this.__id16_] == undefined || int(this.__setPropDict[this.__id16_]) != 9)
         {
            this.__setPropDict[this.__id16_] = 9;
            try
            {
               this.__id16_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id16_.delay = 2;
            this.__id16_.enemyType = 22;
            this.__id16_.interval = 1;
            this.__id16_.isRandom = false;
            this.__id16_.stopPointIdx = 4;
            this.__id16_.totalNum = 2;
            try
            {
               this.__id16_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id17__() : *
      {
         if(this.__setPropDict[this.__id17_] == undefined || int(this.__setPropDict[this.__id17_]) != 1)
         {
            this.__setPropDict[this.__id17_] = 1;
            try
            {
               this.__id17_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id17_.delay = 0;
            this.__id17_.enemyType = 1;
            this.__id17_.interval = 1;
            this.__id17_.isRandom = false;
            this.__id17_.stopPointIdx = 1;
            this.__id17_.totalNum = 3;
            try
            {
               this.__id17_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id17___165() : *
      {
         if(this.__setPropDict[this.__id17_] == undefined || int(this.__setPropDict[this.__id17_]) != 2)
         {
            this.__setPropDict[this.__id17_] = 2;
            try
            {
               this.__id17_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id17_.delay = 0;
            this.__id17_.enemyType = 2;
            this.__id17_.interval = 1;
            this.__id17_.isRandom = false;
            this.__id17_.stopPointIdx = 1;
            this.__id17_.totalNum = 3;
            try
            {
               this.__id17_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id17___166() : *
      {
         if(this.__setPropDict[this.__id17_] == undefined || int(this.__setPropDict[this.__id17_]) != 3)
         {
            this.__setPropDict[this.__id17_] = 3;
            try
            {
               this.__id17_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id17_.delay = 0;
            this.__id17_.enemyType = 7;
            this.__id17_.interval = 1;
            this.__id17_.isRandom = false;
            this.__id17_.stopPointIdx = 1;
            this.__id17_.totalNum = 3;
            try
            {
               this.__id17_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id17___167() : *
      {
         if(this.__setPropDict[this.__id17_] == undefined || int(this.__setPropDict[this.__id17_]) != 4)
         {
            this.__setPropDict[this.__id17_] = 4;
            try
            {
               this.__id17_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id17_.delay = 0;
            this.__id17_.enemyType = 8;
            this.__id17_.interval = 1;
            this.__id17_.isRandom = false;
            this.__id17_.stopPointIdx = 1;
            this.__id17_.totalNum = 3;
            try
            {
               this.__id17_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id17___168() : *
      {
         if(this.__setPropDict[this.__id17_] == undefined || int(this.__setPropDict[this.__id17_]) != 5)
         {
            this.__setPropDict[this.__id17_] = 5;
            try
            {
               this.__id17_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id17_.delay = 0;
            this.__id17_.enemyType = 10;
            this.__id17_.interval = 1;
            this.__id17_.isRandom = false;
            this.__id17_.stopPointIdx = 1;
            this.__id17_.totalNum = 3;
            try
            {
               this.__id17_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id17___169() : *
      {
         if(this.__setPropDict[this.__id17_] == undefined || int(this.__setPropDict[this.__id17_]) != 6)
         {
            this.__setPropDict[this.__id17_] = 6;
            try
            {
               this.__id17_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id17_.delay = 0;
            this.__id17_.enemyType = 15;
            this.__id17_.interval = 1;
            this.__id17_.isRandom = false;
            this.__id17_.stopPointIdx = 1;
            this.__id17_.totalNum = 3;
            try
            {
               this.__id17_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id17___170() : *
      {
         if(this.__setPropDict[this.__id17_] == undefined || int(this.__setPropDict[this.__id17_]) != 7)
         {
            this.__setPropDict[this.__id17_] = 7;
            try
            {
               this.__id17_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id17_.delay = 0;
            this.__id17_.enemyType = 19;
            this.__id17_.interval = 1;
            this.__id17_.isRandom = false;
            this.__id17_.stopPointIdx = 1;
            this.__id17_.totalNum = 3;
            try
            {
               this.__id17_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id17___171() : *
      {
         if(this.__setPropDict[this.__id17_] == undefined || int(this.__setPropDict[this.__id17_]) != 8)
         {
            this.__setPropDict[this.__id17_] = 8;
            try
            {
               this.__id17_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id17_.delay = 0;
            this.__id17_.enemyType = 26;
            this.__id17_.interval = 1;
            this.__id17_.isRandom = false;
            this.__id17_.stopPointIdx = 1;
            this.__id17_.totalNum = 3;
            try
            {
               this.__id17_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id17___172() : *
      {
         if(this.__setPropDict[this.__id17_] == undefined || int(this.__setPropDict[this.__id17_]) != 9)
         {
            this.__setPropDict[this.__id17_] = 9;
            try
            {
               this.__id17_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id17_.delay = 2;
            this.__id17_.enemyType = 21;
            this.__id17_.interval = 1;
            this.__id17_.isRandom = false;
            this.__id17_.stopPointIdx = 2;
            this.__id17_.totalNum = 3;
            try
            {
               this.__id17_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id18__() : *
      {
         if(this.__setPropDict[this.__id18_] == undefined || int(this.__setPropDict[this.__id18_]) != 1)
         {
            this.__setPropDict[this.__id18_] = 1;
            try
            {
               this.__id18_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id18_.delay = 2;
            this.__id18_.enemyType = 2;
            this.__id18_.interval = 1;
            this.__id18_.isRandom = false;
            this.__id18_.stopPointIdx = 1;
            this.__id18_.totalNum = 2;
            try
            {
               this.__id18_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id18___173() : *
      {
         if(this.__setPropDict[this.__id18_] == undefined || int(this.__setPropDict[this.__id18_]) != 2)
         {
            this.__setPropDict[this.__id18_] = 2;
            try
            {
               this.__id18_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id18_.delay = 2;
            this.__id18_.enemyType = 4;
            this.__id18_.interval = 1;
            this.__id18_.isRandom = false;
            this.__id18_.stopPointIdx = 1;
            this.__id18_.totalNum = 2;
            try
            {
               this.__id18_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id18___174() : *
      {
         if(this.__setPropDict[this.__id18_] == undefined || int(this.__setPropDict[this.__id18_]) != 3)
         {
            this.__setPropDict[this.__id18_] = 3;
            try
            {
               this.__id18_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id18_.delay = 2;
            this.__id18_.enemyType = 7;
            this.__id18_.interval = 1;
            this.__id18_.isRandom = false;
            this.__id18_.stopPointIdx = 1;
            this.__id18_.totalNum = 2;
            try
            {
               this.__id18_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id18___175() : *
      {
         if(this.__setPropDict[this.__id18_] == undefined || int(this.__setPropDict[this.__id18_]) != 4)
         {
            this.__setPropDict[this.__id18_] = 4;
            try
            {
               this.__id18_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id18_.delay = 2;
            this.__id18_.enemyType = 25;
            this.__id18_.interval = 1;
            this.__id18_.isRandom = false;
            this.__id18_.stopPointIdx = 1;
            this.__id18_.totalNum = 2;
            try
            {
               this.__id18_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id18___176() : *
      {
         if(this.__setPropDict[this.__id18_] == undefined || int(this.__setPropDict[this.__id18_]) != 5)
         {
            this.__setPropDict[this.__id18_] = 5;
            try
            {
               this.__id18_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id18_.delay = 2;
            this.__id18_.enemyType = 12;
            this.__id18_.interval = 1;
            this.__id18_.isRandom = false;
            this.__id18_.stopPointIdx = 1;
            this.__id18_.totalNum = 2;
            try
            {
               this.__id18_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id18___177() : *
      {
         if(this.__setPropDict[this.__id18_] == undefined || int(this.__setPropDict[this.__id18_]) != 6)
         {
            this.__setPropDict[this.__id18_] = 6;
            try
            {
               this.__id18_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id18_.delay = 2;
            this.__id18_.enemyType = 16;
            this.__id18_.interval = 1;
            this.__id18_.isRandom = false;
            this.__id18_.stopPointIdx = 1;
            this.__id18_.totalNum = 2;
            try
            {
               this.__id18_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id18___178() : *
      {
         if(this.__setPropDict[this.__id18_] == undefined || int(this.__setPropDict[this.__id18_]) != 7)
         {
            this.__setPropDict[this.__id18_] = 7;
            try
            {
               this.__id18_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id18_.delay = 2;
            this.__id18_.enemyType = 26;
            this.__id18_.interval = 1;
            this.__id18_.isRandom = false;
            this.__id18_.stopPointIdx = 1;
            this.__id18_.totalNum = 2;
            try
            {
               this.__id18_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id18___179() : *
      {
         if(this.__setPropDict[this.__id18_] == undefined || int(this.__setPropDict[this.__id18_]) != 8)
         {
            this.__setPropDict[this.__id18_] = 8;
            try
            {
               this.__id18_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id18_.delay = 2;
            this.__id18_.enemyType = 20;
            this.__id18_.interval = 1;
            this.__id18_.isRandom = false;
            this.__id18_.stopPointIdx = 1;
            this.__id18_.totalNum = 2;
            try
            {
               this.__id18_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id18___180() : *
      {
         if(this.__setPropDict[this.__id18_] == undefined || int(this.__setPropDict[this.__id18_]) != 9)
         {
            this.__setPropDict[this.__id18_] = 9;
            try
            {
               this.__id18_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id18_.delay = 0;
            this.__id18_.enemyType = 22;
            this.__id18_.interval = 1;
            this.__id18_.isRandom = false;
            this.__id18_.stopPointIdx = 2;
            this.__id18_.totalNum = 2;
            try
            {
               this.__id18_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id19__() : *
      {
         if(this.__setPropDict[this.__id19_] == undefined || int(this.__setPropDict[this.__id19_]) != 1)
         {
            this.__setPropDict[this.__id19_] = 1;
            try
            {
               this.__id19_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id19_.delay = 5;
            this.__id19_.enemyType = 2;
            this.__id19_.interval = 1;
            this.__id19_.isRandom = false;
            this.__id19_.stopPointIdx = 0;
            this.__id19_.totalNum = 2;
            try
            {
               this.__id19_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id19___181() : *
      {
         if(this.__setPropDict[this.__id19_] == undefined || int(this.__setPropDict[this.__id19_]) != 2)
         {
            this.__setPropDict[this.__id19_] = 2;
            try
            {
               this.__id19_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id19_.delay = 5;
            this.__id19_.enemyType = 4;
            this.__id19_.interval = 1;
            this.__id19_.isRandom = false;
            this.__id19_.stopPointIdx = 0;
            this.__id19_.totalNum = 1;
            try
            {
               this.__id19_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id19___182() : *
      {
         if(this.__setPropDict[this.__id19_] == undefined || int(this.__setPropDict[this.__id19_]) != 3)
         {
            this.__setPropDict[this.__id19_] = 3;
            try
            {
               this.__id19_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id19_.delay = 5;
            this.__id19_.enemyType = 7;
            this.__id19_.interval = 1;
            this.__id19_.isRandom = false;
            this.__id19_.stopPointIdx = 0;
            this.__id19_.totalNum = 1;
            try
            {
               this.__id19_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id19___183() : *
      {
         if(this.__setPropDict[this.__id19_] == undefined || int(this.__setPropDict[this.__id19_]) != 4)
         {
            this.__setPropDict[this.__id19_] = 4;
            try
            {
               this.__id19_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id19_.delay = 5;
            this.__id19_.enemyType = 25;
            this.__id19_.interval = 1;
            this.__id19_.isRandom = false;
            this.__id19_.stopPointIdx = 0;
            this.__id19_.totalNum = 1;
            try
            {
               this.__id19_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id19___184() : *
      {
         if(this.__setPropDict[this.__id19_] == undefined || int(this.__setPropDict[this.__id19_]) != 5)
         {
            this.__setPropDict[this.__id19_] = 5;
            try
            {
               this.__id19_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id19_.delay = 5;
            this.__id19_.enemyType = 12;
            this.__id19_.interval = 1;
            this.__id19_.isRandom = false;
            this.__id19_.stopPointIdx = 0;
            this.__id19_.totalNum = 1;
            try
            {
               this.__id19_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id19___185() : *
      {
         if(this.__setPropDict[this.__id19_] == undefined || int(this.__setPropDict[this.__id19_]) != 6)
         {
            this.__setPropDict[this.__id19_] = 6;
            try
            {
               this.__id19_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id19_.delay = 5;
            this.__id19_.enemyType = 16;
            this.__id19_.interval = 1;
            this.__id19_.isRandom = false;
            this.__id19_.stopPointIdx = 0;
            this.__id19_.totalNum = 1;
            try
            {
               this.__id19_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id19___186() : *
      {
         if(this.__setPropDict[this.__id19_] == undefined || int(this.__setPropDict[this.__id19_]) != 7)
         {
            this.__setPropDict[this.__id19_] = 7;
            try
            {
               this.__id19_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id19_.delay = 5;
            this.__id19_.enemyType = 26;
            this.__id19_.interval = 1;
            this.__id19_.isRandom = false;
            this.__id19_.stopPointIdx = 0;
            this.__id19_.totalNum = 1;
            try
            {
               this.__id19_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id19___187() : *
      {
         if(this.__setPropDict[this.__id19_] == undefined || int(this.__setPropDict[this.__id19_]) != 8)
         {
            this.__setPropDict[this.__id19_] = 8;
            try
            {
               this.__id19_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id19_.delay = 5;
            this.__id19_.enemyType = 20;
            this.__id19_.interval = 1;
            this.__id19_.isRandom = false;
            this.__id19_.stopPointIdx = 0;
            this.__id19_.totalNum = 1;
            try
            {
               this.__id19_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id19___188() : *
      {
         if(this.__setPropDict[this.__id19_] == undefined || int(this.__setPropDict[this.__id19_]) != 9)
         {
            this.__setPropDict[this.__id19_] = 9;
            try
            {
               this.__id19_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id19_.delay = 1;
            this.__id19_.enemyType = 21;
            this.__id19_.interval = 1;
            this.__id19_.isRandom = false;
            this.__id19_.stopPointIdx = 2;
            this.__id19_.totalNum = 3;
            try
            {
               this.__id19_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id20__() : *
      {
         if(this.__setPropDict[this.__id20_] == undefined || int(this.__setPropDict[this.__id20_]) != 1)
         {
            this.__setPropDict[this.__id20_] = 1;
            try
            {
               this.__id20_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id20_.delay = 2;
            this.__id20_.enemyType = 2;
            this.__id20_.interval = 1;
            this.__id20_.isRandom = false;
            this.__id20_.stopPointIdx = 4;
            this.__id20_.totalNum = 2;
            try
            {
               this.__id20_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id20___189() : *
      {
         if(this.__setPropDict[this.__id20_] == undefined || int(this.__setPropDict[this.__id20_]) != 2)
         {
            this.__setPropDict[this.__id20_] = 2;
            try
            {
               this.__id20_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id20_.delay = 2;
            this.__id20_.enemyType = 4;
            this.__id20_.interval = 1;
            this.__id20_.isRandom = false;
            this.__id20_.stopPointIdx = 4;
            this.__id20_.totalNum = 2;
            try
            {
               this.__id20_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id20___190() : *
      {
         if(this.__setPropDict[this.__id20_] == undefined || int(this.__setPropDict[this.__id20_]) != 3)
         {
            this.__setPropDict[this.__id20_] = 3;
            try
            {
               this.__id20_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id20_.delay = 2;
            this.__id20_.enemyType = 8;
            this.__id20_.interval = 1;
            this.__id20_.isRandom = false;
            this.__id20_.stopPointIdx = 4;
            this.__id20_.totalNum = 2;
            try
            {
               this.__id20_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id20___191() : *
      {
         if(this.__setPropDict[this.__id20_] == undefined || int(this.__setPropDict[this.__id20_]) != 4)
         {
            this.__setPropDict[this.__id20_] = 4;
            try
            {
               this.__id20_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id20_.delay = 2;
            this.__id20_.enemyType = 25;
            this.__id20_.interval = 1;
            this.__id20_.isRandom = false;
            this.__id20_.stopPointIdx = 4;
            this.__id20_.totalNum = 2;
            try
            {
               this.__id20_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id20___192() : *
      {
         if(this.__setPropDict[this.__id20_] == undefined || int(this.__setPropDict[this.__id20_]) != 5)
         {
            this.__setPropDict[this.__id20_] = 5;
            try
            {
               this.__id20_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id20_.delay = 2;
            this.__id20_.enemyType = 13;
            this.__id20_.interval = 1;
            this.__id20_.isRandom = false;
            this.__id20_.stopPointIdx = 4;
            this.__id20_.totalNum = 2;
            try
            {
               this.__id20_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id20___193() : *
      {
         if(this.__setPropDict[this.__id20_] == undefined || int(this.__setPropDict[this.__id20_]) != 6)
         {
            this.__setPropDict[this.__id20_] = 6;
            try
            {
               this.__id20_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id20_.delay = 2;
            this.__id20_.enemyType = 16;
            this.__id20_.interval = 1;
            this.__id20_.isRandom = false;
            this.__id20_.stopPointIdx = 4;
            this.__id20_.totalNum = 2;
            try
            {
               this.__id20_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id20___194() : *
      {
         if(this.__setPropDict[this.__id20_] == undefined || int(this.__setPropDict[this.__id20_]) != 7)
         {
            this.__setPropDict[this.__id20_] = 7;
            try
            {
               this.__id20_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id20_.delay = 2;
            this.__id20_.enemyType = 26;
            this.__id20_.interval = 1;
            this.__id20_.isRandom = false;
            this.__id20_.stopPointIdx = 4;
            this.__id20_.totalNum = 2;
            try
            {
               this.__id20_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id20___195() : *
      {
         if(this.__setPropDict[this.__id20_] == undefined || int(this.__setPropDict[this.__id20_]) != 8)
         {
            this.__setPropDict[this.__id20_] = 8;
            try
            {
               this.__id20_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id20_.delay = 2;
            this.__id20_.enemyType = 20;
            this.__id20_.interval = 1;
            this.__id20_.isRandom = false;
            this.__id20_.stopPointIdx = 4;
            this.__id20_.totalNum = 2;
            try
            {
               this.__id20_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id20___196() : *
      {
         if(this.__setPropDict[this.__id20_] == undefined || int(this.__setPropDict[this.__id20_]) != 9)
         {
            this.__setPropDict[this.__id20_] = 9;
            try
            {
               this.__id20_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id20_.delay = 0;
            this.__id20_.enemyType = 21;
            this.__id20_.interval = 1;
            this.__id20_.isRandom = false;
            this.__id20_.stopPointIdx = 2;
            this.__id20_.totalNum = 2;
            try
            {
               this.__id20_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id21__() : *
      {
         if(this.__setPropDict[this.__id21_] == undefined || int(this.__setPropDict[this.__id21_]) != 1)
         {
            this.__setPropDict[this.__id21_] = 1;
            try
            {
               this.__id21_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id21_.delay = 1;
            this.__id21_.enemyType = 1;
            this.__id21_.interval = 1;
            this.__id21_.isRandom = false;
            this.__id21_.stopPointIdx = 0;
            this.__id21_.totalNum = 3;
            try
            {
               this.__id21_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id21___197() : *
      {
         if(this.__setPropDict[this.__id21_] == undefined || int(this.__setPropDict[this.__id21_]) != 2)
         {
            this.__setPropDict[this.__id21_] = 2;
            try
            {
               this.__id21_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id21_.delay = 1;
            this.__id21_.enemyType = 2;
            this.__id21_.interval = 1;
            this.__id21_.isRandom = false;
            this.__id21_.stopPointIdx = 0;
            this.__id21_.totalNum = 3;
            try
            {
               this.__id21_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id21___198() : *
      {
         if(this.__setPropDict[this.__id21_] == undefined || int(this.__setPropDict[this.__id21_]) != 3)
         {
            this.__setPropDict[this.__id21_] = 3;
            try
            {
               this.__id21_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id21_.delay = 1;
            this.__id21_.enemyType = 6;
            this.__id21_.interval = 1;
            this.__id21_.isRandom = false;
            this.__id21_.stopPointIdx = 0;
            this.__id21_.totalNum = 3;
            try
            {
               this.__id21_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id21___199() : *
      {
         if(this.__setPropDict[this.__id21_] == undefined || int(this.__setPropDict[this.__id21_]) != 4)
         {
            this.__setPropDict[this.__id21_] = 4;
            try
            {
               this.__id21_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id21_.delay = 1;
            this.__id21_.enemyType = 8;
            this.__id21_.interval = 1;
            this.__id21_.isRandom = false;
            this.__id21_.stopPointIdx = 0;
            this.__id21_.totalNum = 3;
            try
            {
               this.__id21_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id21___200() : *
      {
         if(this.__setPropDict[this.__id21_] == undefined || int(this.__setPropDict[this.__id21_]) != 5)
         {
            this.__setPropDict[this.__id21_] = 5;
            try
            {
               this.__id21_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id21_.delay = 1;
            this.__id21_.enemyType = 12;
            this.__id21_.interval = 1;
            this.__id21_.isRandom = false;
            this.__id21_.stopPointIdx = 0;
            this.__id21_.totalNum = 3;
            try
            {
               this.__id21_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id21___201() : *
      {
         if(this.__setPropDict[this.__id21_] == undefined || int(this.__setPropDict[this.__id21_]) != 6)
         {
            this.__setPropDict[this.__id21_] = 6;
            try
            {
               this.__id21_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id21_.delay = 1;
            this.__id21_.enemyType = 15;
            this.__id21_.interval = 1;
            this.__id21_.isRandom = false;
            this.__id21_.stopPointIdx = 0;
            this.__id21_.totalNum = 3;
            try
            {
               this.__id21_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id21___202() : *
      {
         if(this.__setPropDict[this.__id21_] == undefined || int(this.__setPropDict[this.__id21_]) != 7)
         {
            this.__setPropDict[this.__id21_] = 7;
            try
            {
               this.__id21_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id21_.delay = 1;
            this.__id21_.enemyType = 19;
            this.__id21_.interval = 1;
            this.__id21_.isRandom = false;
            this.__id21_.stopPointIdx = 0;
            this.__id21_.totalNum = 3;
            try
            {
               this.__id21_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id21___203() : *
      {
         if(this.__setPropDict[this.__id21_] == undefined || int(this.__setPropDict[this.__id21_]) != 8)
         {
            this.__setPropDict[this.__id21_] = 8;
            try
            {
               this.__id21_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id21_.delay = 1;
            this.__id21_.enemyType = 26;
            this.__id21_.interval = 1;
            this.__id21_.isRandom = false;
            this.__id21_.stopPointIdx = 0;
            this.__id21_.totalNum = 3;
            try
            {
               this.__id21_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id21___204() : *
      {
         if(this.__setPropDict[this.__id21_] == undefined || int(this.__setPropDict[this.__id21_]) != 9)
         {
            this.__setPropDict[this.__id21_] = 9;
            try
            {
               this.__id21_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id21_.delay = 9;
            this.__id21_.enemyType = 22;
            this.__id21_.interval = 1;
            this.__id21_.isRandom = false;
            this.__id21_.stopPointIdx = 2;
            this.__id21_.totalNum = 2;
            try
            {
               this.__id21_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id22__() : *
      {
         if(this.__setPropDict[this.__id22_] == undefined || int(this.__setPropDict[this.__id22_]) != 1)
         {
            this.__setPropDict[this.__id22_] = 1;
            try
            {
               this.__id22_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id22_.delay = 2;
            this.__id22_.enemyType = 1;
            this.__id22_.interval = 1;
            this.__id22_.isRandom = false;
            this.__id22_.stopPointIdx = 2;
            this.__id22_.totalNum = 3;
            try
            {
               this.__id22_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id22___205() : *
      {
         if(this.__setPropDict[this.__id22_] == undefined || int(this.__setPropDict[this.__id22_]) != 2)
         {
            this.__setPropDict[this.__id22_] = 2;
            try
            {
               this.__id22_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id22_.delay = 2;
            this.__id22_.enemyType = 2;
            this.__id22_.interval = 1;
            this.__id22_.isRandom = false;
            this.__id22_.stopPointIdx = 2;
            this.__id22_.totalNum = 3;
            try
            {
               this.__id22_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id22___206() : *
      {
         if(this.__setPropDict[this.__id22_] == undefined || int(this.__setPropDict[this.__id22_]) != 3)
         {
            this.__setPropDict[this.__id22_] = 3;
            try
            {
               this.__id22_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id22_.delay = 2;
            this.__id22_.enemyType = 6;
            this.__id22_.interval = 1;
            this.__id22_.isRandom = false;
            this.__id22_.stopPointIdx = 2;
            this.__id22_.totalNum = 3;
            try
            {
               this.__id22_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id22___207() : *
      {
         if(this.__setPropDict[this.__id22_] == undefined || int(this.__setPropDict[this.__id22_]) != 4)
         {
            this.__setPropDict[this.__id22_] = 4;
            try
            {
               this.__id22_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id22_.delay = 2;
            this.__id22_.enemyType = 8;
            this.__id22_.interval = 1;
            this.__id22_.isRandom = false;
            this.__id22_.stopPointIdx = 2;
            this.__id22_.totalNum = 3;
            try
            {
               this.__id22_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id22___208() : *
      {
         if(this.__setPropDict[this.__id22_] == undefined || int(this.__setPropDict[this.__id22_]) != 5)
         {
            this.__setPropDict[this.__id22_] = 5;
            try
            {
               this.__id22_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id22_.delay = 2;
            this.__id22_.enemyType = 10;
            this.__id22_.interval = 1;
            this.__id22_.isRandom = false;
            this.__id22_.stopPointIdx = 2;
            this.__id22_.totalNum = 3;
            try
            {
               this.__id22_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id22___209() : *
      {
         if(this.__setPropDict[this.__id22_] == undefined || int(this.__setPropDict[this.__id22_]) != 6)
         {
            this.__setPropDict[this.__id22_] = 6;
            try
            {
               this.__id22_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id22_.delay = 2;
            this.__id22_.enemyType = 15;
            this.__id22_.interval = 1;
            this.__id22_.isRandom = false;
            this.__id22_.stopPointIdx = 2;
            this.__id22_.totalNum = 3;
            try
            {
               this.__id22_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id22___210() : *
      {
         if(this.__setPropDict[this.__id22_] == undefined || int(this.__setPropDict[this.__id22_]) != 7)
         {
            this.__setPropDict[this.__id22_] = 7;
            try
            {
               this.__id22_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id22_.delay = 2;
            this.__id22_.enemyType = 19;
            this.__id22_.interval = 1;
            this.__id22_.isRandom = false;
            this.__id22_.stopPointIdx = 2;
            this.__id22_.totalNum = 3;
            try
            {
               this.__id22_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id22___211() : *
      {
         if(this.__setPropDict[this.__id22_] == undefined || int(this.__setPropDict[this.__id22_]) != 8)
         {
            this.__setPropDict[this.__id22_] = 8;
            try
            {
               this.__id22_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id22_.delay = 2;
            this.__id22_.enemyType = 26;
            this.__id22_.interval = 1;
            this.__id22_.isRandom = false;
            this.__id22_.stopPointIdx = 2;
            this.__id22_.totalNum = 3;
            try
            {
               this.__id22_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id22___212() : *
      {
         if(this.__setPropDict[this.__id22_] == undefined || int(this.__setPropDict[this.__id22_]) != 9)
         {
            this.__setPropDict[this.__id22_] = 9;
            try
            {
               this.__id22_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id22_.delay = 3;
            this.__id22_.enemyType = 21;
            this.__id22_.interval = 1;
            this.__id22_.isRandom = false;
            this.__id22_.stopPointIdx = 3;
            this.__id22_.totalNum = 3;
            try
            {
               this.__id22_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id23__() : *
      {
         if(this.__setPropDict[this.__id23_] == undefined || int(this.__setPropDict[this.__id23_]) != 1)
         {
            this.__setPropDict[this.__id23_] = 1;
            try
            {
               this.__id23_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id23_.delay = 0;
            this.__id23_.enemyType = 2;
            this.__id23_.interval = 1;
            this.__id23_.isRandom = false;
            this.__id23_.stopPointIdx = 2;
            this.__id23_.totalNum = 2;
            try
            {
               this.__id23_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id23___213() : *
      {
         if(this.__setPropDict[this.__id23_] == undefined || int(this.__setPropDict[this.__id23_]) != 2)
         {
            this.__setPropDict[this.__id23_] = 2;
            try
            {
               this.__id23_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id23_.delay = 0;
            this.__id23_.enemyType = 4;
            this.__id23_.interval = 1;
            this.__id23_.isRandom = false;
            this.__id23_.stopPointIdx = 2;
            this.__id23_.totalNum = 2;
            try
            {
               this.__id23_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id23___214() : *
      {
         if(this.__setPropDict[this.__id23_] == undefined || int(this.__setPropDict[this.__id23_]) != 3)
         {
            this.__setPropDict[this.__id23_] = 3;
            try
            {
               this.__id23_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id23_.delay = 0;
            this.__id23_.enemyType = 7;
            this.__id23_.interval = 1;
            this.__id23_.isRandom = false;
            this.__id23_.stopPointIdx = 2;
            this.__id23_.totalNum = 2;
            try
            {
               this.__id23_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id23___215() : *
      {
         if(this.__setPropDict[this.__id23_] == undefined || int(this.__setPropDict[this.__id23_]) != 4)
         {
            this.__setPropDict[this.__id23_] = 4;
            try
            {
               this.__id23_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id23_.delay = 0;
            this.__id23_.enemyType = 25;
            this.__id23_.interval = 1;
            this.__id23_.isRandom = false;
            this.__id23_.stopPointIdx = 2;
            this.__id23_.totalNum = 2;
            try
            {
               this.__id23_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id23___216() : *
      {
         if(this.__setPropDict[this.__id23_] == undefined || int(this.__setPropDict[this.__id23_]) != 5)
         {
            this.__setPropDict[this.__id23_] = 5;
            try
            {
               this.__id23_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id23_.delay = 0;
            this.__id23_.enemyType = 12;
            this.__id23_.interval = 1;
            this.__id23_.isRandom = false;
            this.__id23_.stopPointIdx = 2;
            this.__id23_.totalNum = 2;
            try
            {
               this.__id23_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id23___217() : *
      {
         if(this.__setPropDict[this.__id23_] == undefined || int(this.__setPropDict[this.__id23_]) != 6)
         {
            this.__setPropDict[this.__id23_] = 6;
            try
            {
               this.__id23_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id23_.delay = 0;
            this.__id23_.enemyType = 16;
            this.__id23_.interval = 1;
            this.__id23_.isRandom = false;
            this.__id23_.stopPointIdx = 2;
            this.__id23_.totalNum = 2;
            try
            {
               this.__id23_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id23___218() : *
      {
         if(this.__setPropDict[this.__id23_] == undefined || int(this.__setPropDict[this.__id23_]) != 7)
         {
            this.__setPropDict[this.__id23_] = 7;
            try
            {
               this.__id23_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id23_.delay = 0;
            this.__id23_.enemyType = 26;
            this.__id23_.interval = 1;
            this.__id23_.isRandom = false;
            this.__id23_.stopPointIdx = 2;
            this.__id23_.totalNum = 2;
            try
            {
               this.__id23_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id23___219() : *
      {
         if(this.__setPropDict[this.__id23_] == undefined || int(this.__setPropDict[this.__id23_]) != 8)
         {
            this.__setPropDict[this.__id23_] = 8;
            try
            {
               this.__id23_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id23_.delay = 0;
            this.__id23_.enemyType = 20;
            this.__id23_.interval = 1;
            this.__id23_.isRandom = false;
            this.__id23_.stopPointIdx = 2;
            this.__id23_.totalNum = 2;
            try
            {
               this.__id23_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id23___220() : *
      {
         if(this.__setPropDict[this.__id23_] == undefined || int(this.__setPropDict[this.__id23_]) != 9)
         {
            this.__setPropDict[this.__id23_] = 9;
            try
            {
               this.__id23_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id23_.delay = 4;
            this.__id23_.enemyType = 21;
            this.__id23_.interval = 1;
            this.__id23_.isRandom = false;
            this.__id23_.stopPointIdx = 3;
            this.__id23_.totalNum = 4;
            try
            {
               this.__id23_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id24__() : *
      {
         if(this.__setPropDict[this.__id24_] == undefined || int(this.__setPropDict[this.__id24_]) != 1)
         {
            this.__setPropDict[this.__id24_] = 1;
            try
            {
               this.__id24_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id24_.delay = 1;
            this.__id24_.enemyType = 1;
            this.__id24_.interval = 1;
            this.__id24_.isRandom = false;
            this.__id24_.stopPointIdx = 2;
            this.__id24_.totalNum = 3;
            try
            {
               this.__id24_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id24___221() : *
      {
         if(this.__setPropDict[this.__id24_] == undefined || int(this.__setPropDict[this.__id24_]) != 2)
         {
            this.__setPropDict[this.__id24_] = 2;
            try
            {
               this.__id24_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id24_.delay = 1;
            this.__id24_.enemyType = 2;
            this.__id24_.interval = 1;
            this.__id24_.isRandom = false;
            this.__id24_.stopPointIdx = 2;
            this.__id24_.totalNum = 3;
            try
            {
               this.__id24_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id24___222() : *
      {
         if(this.__setPropDict[this.__id24_] == undefined || int(this.__setPropDict[this.__id24_]) != 3)
         {
            this.__setPropDict[this.__id24_] = 3;
            try
            {
               this.__id24_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id24_.delay = 1;
            this.__id24_.enemyType = 6;
            this.__id24_.interval = 1;
            this.__id24_.isRandom = false;
            this.__id24_.stopPointIdx = 2;
            this.__id24_.totalNum = 3;
            try
            {
               this.__id24_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id24___223() : *
      {
         if(this.__setPropDict[this.__id24_] == undefined || int(this.__setPropDict[this.__id24_]) != 4)
         {
            this.__setPropDict[this.__id24_] = 4;
            try
            {
               this.__id24_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id24_.delay = 1;
            this.__id24_.enemyType = 8;
            this.__id24_.interval = 1;
            this.__id24_.isRandom = false;
            this.__id24_.stopPointIdx = 2;
            this.__id24_.totalNum = 3;
            try
            {
               this.__id24_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id24___224() : *
      {
         if(this.__setPropDict[this.__id24_] == undefined || int(this.__setPropDict[this.__id24_]) != 5)
         {
            this.__setPropDict[this.__id24_] = 5;
            try
            {
               this.__id24_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id24_.delay = 1;
            this.__id24_.enemyType = 12;
            this.__id24_.interval = 1;
            this.__id24_.isRandom = false;
            this.__id24_.stopPointIdx = 2;
            this.__id24_.totalNum = 3;
            try
            {
               this.__id24_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id24___225() : *
      {
         if(this.__setPropDict[this.__id24_] == undefined || int(this.__setPropDict[this.__id24_]) != 6)
         {
            this.__setPropDict[this.__id24_] = 6;
            try
            {
               this.__id24_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id24_.delay = 1;
            this.__id24_.enemyType = 15;
            this.__id24_.interval = 1;
            this.__id24_.isRandom = false;
            this.__id24_.stopPointIdx = 2;
            this.__id24_.totalNum = 3;
            try
            {
               this.__id24_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id24___226() : *
      {
         if(this.__setPropDict[this.__id24_] == undefined || int(this.__setPropDict[this.__id24_]) != 7)
         {
            this.__setPropDict[this.__id24_] = 7;
            try
            {
               this.__id24_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id24_.delay = 1;
            this.__id24_.enemyType = 19;
            this.__id24_.interval = 1;
            this.__id24_.isRandom = false;
            this.__id24_.stopPointIdx = 2;
            this.__id24_.totalNum = 3;
            try
            {
               this.__id24_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id24___227() : *
      {
         if(this.__setPropDict[this.__id24_] == undefined || int(this.__setPropDict[this.__id24_]) != 8)
         {
            this.__setPropDict[this.__id24_] = 8;
            try
            {
               this.__id24_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id24_.delay = 1;
            this.__id24_.enemyType = 26;
            this.__id24_.interval = 1;
            this.__id24_.isRandom = false;
            this.__id24_.stopPointIdx = 2;
            this.__id24_.totalNum = 3;
            try
            {
               this.__id24_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id24___228() : *
      {
         if(this.__setPropDict[this.__id24_] == undefined || int(this.__setPropDict[this.__id24_]) != 9)
         {
            this.__setPropDict[this.__id24_] = 9;
            try
            {
               this.__id24_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id24_.delay = 0;
            this.__id24_.enemyType = 21;
            this.__id24_.interval = 1;
            this.__id24_.isRandom = false;
            this.__id24_.stopPointIdx = 3;
            this.__id24_.totalNum = 2;
            try
            {
               this.__id24_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id25__() : *
      {
         if(this.__setPropDict[this.__id25_] == undefined || int(this.__setPropDict[this.__id25_]) != 1)
         {
            this.__setPropDict[this.__id25_] = 1;
            try
            {
               this.__id25_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id25_.delay = 0;
            this.__id25_.enemyType = 1;
            this.__id25_.interval = 1;
            this.__id25_.isRandom = false;
            this.__id25_.stopPointIdx = 2;
            this.__id25_.totalNum = 2;
            try
            {
               this.__id25_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id25___229() : *
      {
         if(this.__setPropDict[this.__id25_] == undefined || int(this.__setPropDict[this.__id25_]) != 2)
         {
            this.__setPropDict[this.__id25_] = 2;
            try
            {
               this.__id25_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id25_.delay = 0;
            this.__id25_.enemyType = 2;
            this.__id25_.interval = 1;
            this.__id25_.isRandom = false;
            this.__id25_.stopPointIdx = 2;
            this.__id25_.totalNum = 2;
            try
            {
               this.__id25_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id25___230() : *
      {
         if(this.__setPropDict[this.__id25_] == undefined || int(this.__setPropDict[this.__id25_]) != 3)
         {
            this.__setPropDict[this.__id25_] = 3;
            try
            {
               this.__id25_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id25_.delay = 0;
            this.__id25_.enemyType = 6;
            this.__id25_.interval = 1;
            this.__id25_.isRandom = false;
            this.__id25_.stopPointIdx = 2;
            this.__id25_.totalNum = 2;
            try
            {
               this.__id25_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id25___231() : *
      {
         if(this.__setPropDict[this.__id25_] == undefined || int(this.__setPropDict[this.__id25_]) != 4)
         {
            this.__setPropDict[this.__id25_] = 4;
            try
            {
               this.__id25_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id25_.delay = 0;
            this.__id25_.enemyType = 8;
            this.__id25_.interval = 1;
            this.__id25_.isRandom = false;
            this.__id25_.stopPointIdx = 2;
            this.__id25_.totalNum = 2;
            try
            {
               this.__id25_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id25___232() : *
      {
         if(this.__setPropDict[this.__id25_] == undefined || int(this.__setPropDict[this.__id25_]) != 5)
         {
            this.__setPropDict[this.__id25_] = 5;
            try
            {
               this.__id25_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id25_.delay = 0;
            this.__id25_.enemyType = 12;
            this.__id25_.interval = 1;
            this.__id25_.isRandom = false;
            this.__id25_.stopPointIdx = 2;
            this.__id25_.totalNum = 2;
            try
            {
               this.__id25_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id25___233() : *
      {
         if(this.__setPropDict[this.__id25_] == undefined || int(this.__setPropDict[this.__id25_]) != 6)
         {
            this.__setPropDict[this.__id25_] = 6;
            try
            {
               this.__id25_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id25_.delay = 0;
            this.__id25_.enemyType = 15;
            this.__id25_.interval = 1;
            this.__id25_.isRandom = false;
            this.__id25_.stopPointIdx = 2;
            this.__id25_.totalNum = 2;
            try
            {
               this.__id25_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id25___234() : *
      {
         if(this.__setPropDict[this.__id25_] == undefined || int(this.__setPropDict[this.__id25_]) != 7)
         {
            this.__setPropDict[this.__id25_] = 7;
            try
            {
               this.__id25_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id25_.delay = 0;
            this.__id25_.enemyType = 19;
            this.__id25_.interval = 1;
            this.__id25_.isRandom = false;
            this.__id25_.stopPointIdx = 2;
            this.__id25_.totalNum = 2;
            try
            {
               this.__id25_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id25___235() : *
      {
         if(this.__setPropDict[this.__id25_] == undefined || int(this.__setPropDict[this.__id25_]) != 8)
         {
            this.__setPropDict[this.__id25_] = 8;
            try
            {
               this.__id25_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id25_.delay = 0;
            this.__id25_.enemyType = 26;
            this.__id25_.interval = 1;
            this.__id25_.isRandom = false;
            this.__id25_.stopPointIdx = 2;
            this.__id25_.totalNum = 2;
            try
            {
               this.__id25_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id25___236() : *
      {
         if(this.__setPropDict[this.__id25_] == undefined || int(this.__setPropDict[this.__id25_]) != 9)
         {
            this.__setPropDict[this.__id25_] = 9;
            try
            {
               this.__id25_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id25_.delay = 3;
            this.__id25_.enemyType = 22;
            this.__id25_.interval = 1;
            this.__id25_.isRandom = false;
            this.__id25_.stopPointIdx = 3;
            this.__id25_.totalNum = 3;
            try
            {
               this.__id25_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id26__() : *
      {
         if(this.__setPropDict[this.__id26_] == undefined || int(this.__setPropDict[this.__id26_]) != 1)
         {
            this.__setPropDict[this.__id26_] = 1;
            try
            {
               this.__id26_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id26_.delay = 9;
            this.__id26_.enemyType = 2;
            this.__id26_.interval = 1;
            this.__id26_.isRandom = false;
            this.__id26_.stopPointIdx = 2;
            this.__id26_.totalNum = 2;
            try
            {
               this.__id26_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id26___237() : *
      {
         if(this.__setPropDict[this.__id26_] == undefined || int(this.__setPropDict[this.__id26_]) != 2)
         {
            this.__setPropDict[this.__id26_] = 2;
            try
            {
               this.__id26_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id26_.delay = 9;
            this.__id26_.enemyType = 4;
            this.__id26_.interval = 1;
            this.__id26_.isRandom = false;
            this.__id26_.stopPointIdx = 2;
            this.__id26_.totalNum = 2;
            try
            {
               this.__id26_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id26___238() : *
      {
         if(this.__setPropDict[this.__id26_] == undefined || int(this.__setPropDict[this.__id26_]) != 3)
         {
            this.__setPropDict[this.__id26_] = 3;
            try
            {
               this.__id26_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id26_.delay = 9;
            this.__id26_.enemyType = 7;
            this.__id26_.interval = 1;
            this.__id26_.isRandom = false;
            this.__id26_.stopPointIdx = 2;
            this.__id26_.totalNum = 2;
            try
            {
               this.__id26_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id26___239() : *
      {
         if(this.__setPropDict[this.__id26_] == undefined || int(this.__setPropDict[this.__id26_]) != 4)
         {
            this.__setPropDict[this.__id26_] = 4;
            try
            {
               this.__id26_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id26_.delay = 9;
            this.__id26_.enemyType = 10;
            this.__id26_.interval = 1;
            this.__id26_.isRandom = false;
            this.__id26_.stopPointIdx = 2;
            this.__id26_.totalNum = 2;
            try
            {
               this.__id26_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id26___240() : *
      {
         if(this.__setPropDict[this.__id26_] == undefined || int(this.__setPropDict[this.__id26_]) != 5)
         {
            this.__setPropDict[this.__id26_] = 5;
            try
            {
               this.__id26_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id26_.delay = 9;
            this.__id26_.enemyType = 12;
            this.__id26_.interval = 1;
            this.__id26_.isRandom = false;
            this.__id26_.stopPointIdx = 2;
            this.__id26_.totalNum = 2;
            try
            {
               this.__id26_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id26___241() : *
      {
         if(this.__setPropDict[this.__id26_] == undefined || int(this.__setPropDict[this.__id26_]) != 6)
         {
            this.__setPropDict[this.__id26_] = 6;
            try
            {
               this.__id26_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id26_.delay = 9;
            this.__id26_.enemyType = 16;
            this.__id26_.interval = 1;
            this.__id26_.isRandom = false;
            this.__id26_.stopPointIdx = 2;
            this.__id26_.totalNum = 2;
            try
            {
               this.__id26_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id26___242() : *
      {
         if(this.__setPropDict[this.__id26_] == undefined || int(this.__setPropDict[this.__id26_]) != 7)
         {
            this.__setPropDict[this.__id26_] = 7;
            try
            {
               this.__id26_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id26_.delay = 9;
            this.__id26_.enemyType = 26;
            this.__id26_.interval = 1;
            this.__id26_.isRandom = false;
            this.__id26_.stopPointIdx = 2;
            this.__id26_.totalNum = 2;
            try
            {
               this.__id26_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id26___243() : *
      {
         if(this.__setPropDict[this.__id26_] == undefined || int(this.__setPropDict[this.__id26_]) != 8)
         {
            this.__setPropDict[this.__id26_] = 8;
            try
            {
               this.__id26_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id26_.delay = 9;
            this.__id26_.enemyType = 20;
            this.__id26_.interval = 1;
            this.__id26_.isRandom = false;
            this.__id26_.stopPointIdx = 2;
            this.__id26_.totalNum = 2;
            try
            {
               this.__id26_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id26___244() : *
      {
         if(this.__setPropDict[this.__id26_] == undefined || int(this.__setPropDict[this.__id26_]) != 9)
         {
            this.__setPropDict[this.__id26_] = 9;
            try
            {
               this.__id26_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id26_.delay = 0;
            this.__id26_.enemyType = 22;
            this.__id26_.interval = 1;
            this.__id26_.isRandom = false;
            this.__id26_.stopPointIdx = 4;
            this.__id26_.totalNum = 1;
            try
            {
               this.__id26_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id27__() : *
      {
         if(this.__setPropDict[this.__id27_] == undefined || int(this.__setPropDict[this.__id27_]) != 1)
         {
            this.__setPropDict[this.__id27_] = 1;
            try
            {
               this.__id27_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id27_.delay = 3;
            this.__id27_.enemyType = 2;
            this.__id27_.interval = 1;
            this.__id27_.isRandom = false;
            this.__id27_.stopPointIdx = 3;
            this.__id27_.totalNum = 3;
            try
            {
               this.__id27_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id27___245() : *
      {
         if(this.__setPropDict[this.__id27_] == undefined || int(this.__setPropDict[this.__id27_]) != 2)
         {
            this.__setPropDict[this.__id27_] = 2;
            try
            {
               this.__id27_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id27_.delay = 3;
            this.__id27_.enemyType = 4;
            this.__id27_.interval = 1;
            this.__id27_.isRandom = false;
            this.__id27_.stopPointIdx = 3;
            this.__id27_.totalNum = 3;
            try
            {
               this.__id27_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id27___246() : *
      {
         if(this.__setPropDict[this.__id27_] == undefined || int(this.__setPropDict[this.__id27_]) != 3)
         {
            this.__setPropDict[this.__id27_] = 3;
            try
            {
               this.__id27_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id27_.delay = 3;
            this.__id27_.enemyType = 6;
            this.__id27_.interval = 1;
            this.__id27_.isRandom = false;
            this.__id27_.stopPointIdx = 3;
            this.__id27_.totalNum = 3;
            try
            {
               this.__id27_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id27___247() : *
      {
         if(this.__setPropDict[this.__id27_] == undefined || int(this.__setPropDict[this.__id27_]) != 4)
         {
            this.__setPropDict[this.__id27_] = 4;
            try
            {
               this.__id27_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id27_.delay = 3;
            this.__id27_.enemyType = 25;
            this.__id27_.interval = 1;
            this.__id27_.isRandom = false;
            this.__id27_.stopPointIdx = 3;
            this.__id27_.totalNum = 3;
            try
            {
               this.__id27_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id27___248() : *
      {
         if(this.__setPropDict[this.__id27_] == undefined || int(this.__setPropDict[this.__id27_]) != 5)
         {
            this.__setPropDict[this.__id27_] = 5;
            try
            {
               this.__id27_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id27_.delay = 3;
            this.__id27_.enemyType = 12;
            this.__id27_.interval = 1;
            this.__id27_.isRandom = false;
            this.__id27_.stopPointIdx = 3;
            this.__id27_.totalNum = 3;
            try
            {
               this.__id27_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id27___249() : *
      {
         if(this.__setPropDict[this.__id27_] == undefined || int(this.__setPropDict[this.__id27_]) != 6)
         {
            this.__setPropDict[this.__id27_] = 6;
            try
            {
               this.__id27_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id27_.delay = 3;
            this.__id27_.enemyType = 16;
            this.__id27_.interval = 1;
            this.__id27_.isRandom = false;
            this.__id27_.stopPointIdx = 3;
            this.__id27_.totalNum = 3;
            try
            {
               this.__id27_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id27___250() : *
      {
         if(this.__setPropDict[this.__id27_] == undefined || int(this.__setPropDict[this.__id27_]) != 7)
         {
            this.__setPropDict[this.__id27_] = 7;
            try
            {
               this.__id27_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id27_.delay = 3;
            this.__id27_.enemyType = 26;
            this.__id27_.interval = 1;
            this.__id27_.isRandom = false;
            this.__id27_.stopPointIdx = 3;
            this.__id27_.totalNum = 3;
            try
            {
               this.__id27_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id27___251() : *
      {
         if(this.__setPropDict[this.__id27_] == undefined || int(this.__setPropDict[this.__id27_]) != 8)
         {
            this.__setPropDict[this.__id27_] = 8;
            try
            {
               this.__id27_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id27_.delay = 3;
            this.__id27_.enemyType = 20;
            this.__id27_.interval = 1;
            this.__id27_.isRandom = false;
            this.__id27_.stopPointIdx = 3;
            this.__id27_.totalNum = 3;
            try
            {
               this.__id27_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id27___252() : *
      {
         if(this.__setPropDict[this.__id27_] == undefined || int(this.__setPropDict[this.__id27_]) != 9)
         {
            this.__setPropDict[this.__id27_] = 9;
            try
            {
               this.__id27_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id27_.delay = 4;
            this.__id27_.enemyType = 22;
            this.__id27_.interval = 1;
            this.__id27_.isRandom = false;
            this.__id27_.stopPointIdx = 3;
            this.__id27_.totalNum = 1;
            try
            {
               this.__id27_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id28__() : *
      {
         if(this.__setPropDict[this.__id28_] == undefined || int(this.__setPropDict[this.__id28_]) != 1)
         {
            this.__setPropDict[this.__id28_] = 1;
            try
            {
               this.__id28_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id28_.delay = 4;
            this.__id28_.enemyType = 1;
            this.__id28_.interval = 1;
            this.__id28_.isRandom = false;
            this.__id28_.stopPointIdx = 3;
            this.__id28_.totalNum = 4;
            try
            {
               this.__id28_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id28___253() : *
      {
         if(this.__setPropDict[this.__id28_] == undefined || int(this.__setPropDict[this.__id28_]) != 2)
         {
            this.__setPropDict[this.__id28_] = 2;
            try
            {
               this.__id28_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id28_.delay = 4;
            this.__id28_.enemyType = 2;
            this.__id28_.interval = 1;
            this.__id28_.isRandom = false;
            this.__id28_.stopPointIdx = 3;
            this.__id28_.totalNum = 4;
            try
            {
               this.__id28_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id28___254() : *
      {
         if(this.__setPropDict[this.__id28_] == undefined || int(this.__setPropDict[this.__id28_]) != 3)
         {
            this.__setPropDict[this.__id28_] = 3;
            try
            {
               this.__id28_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id28_.delay = 4;
            this.__id28_.enemyType = 6;
            this.__id28_.interval = 1;
            this.__id28_.isRandom = false;
            this.__id28_.stopPointIdx = 3;
            this.__id28_.totalNum = 4;
            try
            {
               this.__id28_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id28___255() : *
      {
         if(this.__setPropDict[this.__id28_] == undefined || int(this.__setPropDict[this.__id28_]) != 4)
         {
            this.__setPropDict[this.__id28_] = 4;
            try
            {
               this.__id28_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id28_.delay = 4;
            this.__id28_.enemyType = 25;
            this.__id28_.interval = 1;
            this.__id28_.isRandom = false;
            this.__id28_.stopPointIdx = 3;
            this.__id28_.totalNum = 4;
            try
            {
               this.__id28_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id28___256() : *
      {
         if(this.__setPropDict[this.__id28_] == undefined || int(this.__setPropDict[this.__id28_]) != 5)
         {
            this.__setPropDict[this.__id28_] = 5;
            try
            {
               this.__id28_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id28_.delay = 4;
            this.__id28_.enemyType = 12;
            this.__id28_.interval = 1;
            this.__id28_.isRandom = false;
            this.__id28_.stopPointIdx = 3;
            this.__id28_.totalNum = 4;
            try
            {
               this.__id28_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id28___257() : *
      {
         if(this.__setPropDict[this.__id28_] == undefined || int(this.__setPropDict[this.__id28_]) != 6)
         {
            this.__setPropDict[this.__id28_] = 6;
            try
            {
               this.__id28_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id28_.delay = 4;
            this.__id28_.enemyType = 15;
            this.__id28_.interval = 1;
            this.__id28_.isRandom = false;
            this.__id28_.stopPointIdx = 3;
            this.__id28_.totalNum = 4;
            try
            {
               this.__id28_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id28___258() : *
      {
         if(this.__setPropDict[this.__id28_] == undefined || int(this.__setPropDict[this.__id28_]) != 7)
         {
            this.__setPropDict[this.__id28_] = 7;
            try
            {
               this.__id28_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id28_.delay = 4;
            this.__id28_.enemyType = 19;
            this.__id28_.interval = 1;
            this.__id28_.isRandom = false;
            this.__id28_.stopPointIdx = 3;
            this.__id28_.totalNum = 4;
            try
            {
               this.__id28_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id28___259() : *
      {
         if(this.__setPropDict[this.__id28_] == undefined || int(this.__setPropDict[this.__id28_]) != 8)
         {
            this.__setPropDict[this.__id28_] = 8;
            try
            {
               this.__id28_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id28_.delay = 4;
            this.__id28_.enemyType = 26;
            this.__id28_.interval = 1;
            this.__id28_.isRandom = false;
            this.__id28_.stopPointIdx = 3;
            this.__id28_.totalNum = 4;
            try
            {
               this.__id28_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id28___260() : *
      {
         if(this.__setPropDict[this.__id28_] == undefined || int(this.__setPropDict[this.__id28_]) != 9)
         {
            this.__setPropDict[this.__id28_] = 9;
            try
            {
               this.__id28_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id28_.delay = 2;
            this.__id28_.enemyType = 22;
            this.__id28_.interval = 1;
            this.__id28_.isRandom = false;
            this.__id28_.stopPointIdx = 2;
            this.__id28_.totalNum = 1;
            try
            {
               this.__id28_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id29__() : *
      {
         if(this.__setPropDict[this.__id29_] == undefined || int(this.__setPropDict[this.__id29_]) != 1)
         {
            this.__setPropDict[this.__id29_] = 1;
            try
            {
               this.__id29_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id29_.delay = 0;
            this.__id29_.enemyType = 1;
            this.__id29_.interval = 1;
            this.__id29_.isRandom = false;
            this.__id29_.stopPointIdx = 3;
            this.__id29_.totalNum = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id29___261() : *
      {
         if(this.__setPropDict[this.__id29_] == undefined || int(this.__setPropDict[this.__id29_]) != 2)
         {
            this.__setPropDict[this.__id29_] = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id29_.delay = 0;
            this.__id29_.enemyType = 2;
            this.__id29_.interval = 1;
            this.__id29_.isRandom = false;
            this.__id29_.stopPointIdx = 3;
            this.__id29_.totalNum = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id29___262() : *
      {
         if(this.__setPropDict[this.__id29_] == undefined || int(this.__setPropDict[this.__id29_]) != 3)
         {
            this.__setPropDict[this.__id29_] = 3;
            try
            {
               this.__id29_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id29_.delay = 0;
            this.__id29_.enemyType = 6;
            this.__id29_.interval = 1;
            this.__id29_.isRandom = false;
            this.__id29_.stopPointIdx = 3;
            this.__id29_.totalNum = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id29___263() : *
      {
         if(this.__setPropDict[this.__id29_] == undefined || int(this.__setPropDict[this.__id29_]) != 4)
         {
            this.__setPropDict[this.__id29_] = 4;
            try
            {
               this.__id29_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id29_.delay = 0;
            this.__id29_.enemyType = 8;
            this.__id29_.interval = 1;
            this.__id29_.isRandom = false;
            this.__id29_.stopPointIdx = 3;
            this.__id29_.totalNum = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id29___264() : *
      {
         if(this.__setPropDict[this.__id29_] == undefined || int(this.__setPropDict[this.__id29_]) != 5)
         {
            this.__setPropDict[this.__id29_] = 5;
            try
            {
               this.__id29_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id29_.delay = 0;
            this.__id29_.enemyType = 10;
            this.__id29_.interval = 1;
            this.__id29_.isRandom = false;
            this.__id29_.stopPointIdx = 3;
            this.__id29_.totalNum = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id29___265() : *
      {
         if(this.__setPropDict[this.__id29_] == undefined || int(this.__setPropDict[this.__id29_]) != 6)
         {
            this.__setPropDict[this.__id29_] = 6;
            try
            {
               this.__id29_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id29_.delay = 0;
            this.__id29_.enemyType = 15;
            this.__id29_.interval = 1;
            this.__id29_.isRandom = false;
            this.__id29_.stopPointIdx = 3;
            this.__id29_.totalNum = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id29___266() : *
      {
         if(this.__setPropDict[this.__id29_] == undefined || int(this.__setPropDict[this.__id29_]) != 7)
         {
            this.__setPropDict[this.__id29_] = 7;
            try
            {
               this.__id29_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id29_.delay = 0;
            this.__id29_.enemyType = 19;
            this.__id29_.interval = 1;
            this.__id29_.isRandom = false;
            this.__id29_.stopPointIdx = 3;
            this.__id29_.totalNum = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id29___267() : *
      {
         if(this.__setPropDict[this.__id29_] == undefined || int(this.__setPropDict[this.__id29_]) != 8)
         {
            this.__setPropDict[this.__id29_] = 8;
            try
            {
               this.__id29_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id29_.delay = 0;
            this.__id29_.enemyType = 26;
            this.__id29_.interval = 1;
            this.__id29_.isRandom = false;
            this.__id29_.stopPointIdx = 3;
            this.__id29_.totalNum = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id29___268() : *
      {
         if(this.__setPropDict[this.__id29_] == undefined || int(this.__setPropDict[this.__id29_]) != 9)
         {
            this.__setPropDict[this.__id29_] = 9;
            try
            {
               this.__id29_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id29_.delay = 3;
            this.__id29_.enemyType = 21;
            this.__id29_.interval = 1;
            this.__id29_.isRandom = false;
            this.__id29_.stopPointIdx = 0;
            this.__id29_.totalNum = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id30__() : *
      {
         if(this.__setPropDict[this.__id30_] == undefined || int(this.__setPropDict[this.__id30_]) != 1)
         {
            this.__setPropDict[this.__id30_] = 1;
            try
            {
               this.__id30_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id30_.delay = 5;
            this.__id30_.enemyType = 2;
            this.__id30_.interval = 1;
            this.__id30_.isRandom = false;
            this.__id30_.stopPointIdx = 3;
            this.__id30_.totalNum = 3;
            try
            {
               this.__id30_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id30___269() : *
      {
         if(this.__setPropDict[this.__id30_] == undefined || int(this.__setPropDict[this.__id30_]) != 2)
         {
            this.__setPropDict[this.__id30_] = 2;
            try
            {
               this.__id30_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id30_.delay = 3;
            this.__id30_.enemyType = 4;
            this.__id30_.interval = 1;
            this.__id30_.isRandom = false;
            this.__id30_.stopPointIdx = 3;
            this.__id30_.totalNum = 3;
            try
            {
               this.__id30_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id30___270() : *
      {
         if(this.__setPropDict[this.__id30_] == undefined || int(this.__setPropDict[this.__id30_]) != 3)
         {
            this.__setPropDict[this.__id30_] = 3;
            try
            {
               this.__id30_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id30_.delay = 3;
            this.__id30_.enemyType = 7;
            this.__id30_.interval = 1;
            this.__id30_.isRandom = false;
            this.__id30_.stopPointIdx = 3;
            this.__id30_.totalNum = 3;
            try
            {
               this.__id30_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id30___271() : *
      {
         if(this.__setPropDict[this.__id30_] == undefined || int(this.__setPropDict[this.__id30_]) != 4)
         {
            this.__setPropDict[this.__id30_] = 4;
            try
            {
               this.__id30_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id30_.delay = 3;
            this.__id30_.enemyType = 25;
            this.__id30_.interval = 1;
            this.__id30_.isRandom = false;
            this.__id30_.stopPointIdx = 3;
            this.__id30_.totalNum = 3;
            try
            {
               this.__id30_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id30___272() : *
      {
         if(this.__setPropDict[this.__id30_] == undefined || int(this.__setPropDict[this.__id30_]) != 5)
         {
            this.__setPropDict[this.__id30_] = 5;
            try
            {
               this.__id30_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id30_.delay = 3;
            this.__id30_.enemyType = 12;
            this.__id30_.interval = 1;
            this.__id30_.isRandom = false;
            this.__id30_.stopPointIdx = 3;
            this.__id30_.totalNum = 3;
            try
            {
               this.__id30_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id30___273() : *
      {
         if(this.__setPropDict[this.__id30_] == undefined || int(this.__setPropDict[this.__id30_]) != 6)
         {
            this.__setPropDict[this.__id30_] = 6;
            try
            {
               this.__id30_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id30_.delay = 3;
            this.__id30_.enemyType = 16;
            this.__id30_.interval = 1;
            this.__id30_.isRandom = false;
            this.__id30_.stopPointIdx = 3;
            this.__id30_.totalNum = 3;
            try
            {
               this.__id30_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id30___274() : *
      {
         if(this.__setPropDict[this.__id30_] == undefined || int(this.__setPropDict[this.__id30_]) != 7)
         {
            this.__setPropDict[this.__id30_] = 7;
            try
            {
               this.__id30_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id30_.delay = 3;
            this.__id30_.enemyType = 26;
            this.__id30_.interval = 1;
            this.__id30_.isRandom = false;
            this.__id30_.stopPointIdx = 3;
            this.__id30_.totalNum = 3;
            try
            {
               this.__id30_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id30___275() : *
      {
         if(this.__setPropDict[this.__id30_] == undefined || int(this.__setPropDict[this.__id30_]) != 8)
         {
            this.__setPropDict[this.__id30_] = 8;
            try
            {
               this.__id30_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id30_.delay = 3;
            this.__id30_.enemyType = 20;
            this.__id30_.interval = 1;
            this.__id30_.isRandom = false;
            this.__id30_.stopPointIdx = 3;
            this.__id30_.totalNum = 3;
            try
            {
               this.__id30_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id30___276() : *
      {
         if(this.__setPropDict[this.__id30_] == undefined || int(this.__setPropDict[this.__id30_]) != 9)
         {
            this.__setPropDict[this.__id30_] = 9;
            try
            {
               this.__id30_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id30_.delay = 0;
            this.__id30_.enemyType = 21;
            this.__id30_.interval = 1;
            this.__id30_.isRandom = false;
            this.__id30_.stopPointIdx = 0;
            this.__id30_.totalNum = 1;
            try
            {
               this.__id30_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id31__() : *
      {
         if(this.__setPropDict[this.__id31_] == undefined || int(this.__setPropDict[this.__id31_]) != 1)
         {
            this.__setPropDict[this.__id31_] = 1;
            try
            {
               this.__id31_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id31_.delay = 0;
            this.__id31_.enemyType = 2;
            this.__id31_.interval = 1;
            this.__id31_.isRandom = false;
            this.__id31_.stopPointIdx = 4;
            this.__id31_.totalNum = 1;
            try
            {
               this.__id31_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id31___277() : *
      {
         if(this.__setPropDict[this.__id31_] == undefined || int(this.__setPropDict[this.__id31_]) != 2)
         {
            this.__setPropDict[this.__id31_] = 2;
            try
            {
               this.__id31_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id31_.delay = 0;
            this.__id31_.enemyType = 4;
            this.__id31_.interval = 1;
            this.__id31_.isRandom = false;
            this.__id31_.stopPointIdx = 4;
            this.__id31_.totalNum = 1;
            try
            {
               this.__id31_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id31___278() : *
      {
         if(this.__setPropDict[this.__id31_] == undefined || int(this.__setPropDict[this.__id31_]) != 3)
         {
            this.__setPropDict[this.__id31_] = 3;
            try
            {
               this.__id31_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id31_.delay = 0;
            this.__id31_.enemyType = 8;
            this.__id31_.interval = 1;
            this.__id31_.isRandom = false;
            this.__id31_.stopPointIdx = 4;
            this.__id31_.totalNum = 1;
            try
            {
               this.__id31_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id31___279() : *
      {
         if(this.__setPropDict[this.__id31_] == undefined || int(this.__setPropDict[this.__id31_]) != 4)
         {
            this.__setPropDict[this.__id31_] = 4;
            try
            {
               this.__id31_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id31_.delay = 0;
            this.__id31_.enemyType = 25;
            this.__id31_.interval = 1;
            this.__id31_.isRandom = false;
            this.__id31_.stopPointIdx = 4;
            this.__id31_.totalNum = 1;
            try
            {
               this.__id31_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id31___280() : *
      {
         if(this.__setPropDict[this.__id31_] == undefined || int(this.__setPropDict[this.__id31_]) != 5)
         {
            this.__setPropDict[this.__id31_] = 5;
            try
            {
               this.__id31_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id31_.delay = 0;
            this.__id31_.enemyType = 13;
            this.__id31_.interval = 1;
            this.__id31_.isRandom = false;
            this.__id31_.stopPointIdx = 4;
            this.__id31_.totalNum = 1;
            try
            {
               this.__id31_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id31___281() : *
      {
         if(this.__setPropDict[this.__id31_] == undefined || int(this.__setPropDict[this.__id31_]) != 6)
         {
            this.__setPropDict[this.__id31_] = 6;
            try
            {
               this.__id31_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id31_.delay = 0;
            this.__id31_.enemyType = 16;
            this.__id31_.interval = 1;
            this.__id31_.isRandom = false;
            this.__id31_.stopPointIdx = 4;
            this.__id31_.totalNum = 1;
            try
            {
               this.__id31_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id31___282() : *
      {
         if(this.__setPropDict[this.__id31_] == undefined || int(this.__setPropDict[this.__id31_]) != 7)
         {
            this.__setPropDict[this.__id31_] = 7;
            try
            {
               this.__id31_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id31_.delay = 0;
            this.__id31_.enemyType = 26;
            this.__id31_.interval = 1;
            this.__id31_.isRandom = false;
            this.__id31_.stopPointIdx = 4;
            this.__id31_.totalNum = 1;
            try
            {
               this.__id31_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id31___283() : *
      {
         if(this.__setPropDict[this.__id31_] == undefined || int(this.__setPropDict[this.__id31_]) != 8)
         {
            this.__setPropDict[this.__id31_] = 8;
            try
            {
               this.__id31_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id31_.delay = 0;
            this.__id31_.enemyType = 20;
            this.__id31_.interval = 1;
            this.__id31_.isRandom = false;
            this.__id31_.stopPointIdx = 4;
            this.__id31_.totalNum = 1;
            try
            {
               this.__id31_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id31___284() : *
      {
         if(this.__setPropDict[this.__id31_] == undefined || int(this.__setPropDict[this.__id31_]) != 9)
         {
            this.__setPropDict[this.__id31_] = 9;
            try
            {
               this.__id31_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id31_.delay = 1;
            this.__id31_.enemyType = 21;
            this.__id31_.interval = 1;
            this.__id31_.isRandom = false;
            this.__id31_.stopPointIdx = 0;
            this.__id31_.totalNum = 3;
            try
            {
               this.__id31_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id32__() : *
      {
         if(this.__setPropDict[this.__id32_] == undefined || int(this.__setPropDict[this.__id32_]) != 2)
         {
            this.__setPropDict[this.__id32_] = 2;
            try
            {
               this.__id32_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id32_.delay = 2;
            this.__id32_.enemyType = 2;
            this.__id32_.interval = 1;
            this.__id32_.isRandom = false;
            this.__id32_.stopPointIdx = 0;
            this.__id32_.totalNum = 2;
            try
            {
               this.__id32_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id32___285() : *
      {
         if(this.__setPropDict[this.__id32_] == undefined || int(this.__setPropDict[this.__id32_]) != 3)
         {
            this.__setPropDict[this.__id32_] = 3;
            try
            {
               this.__id32_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id32_.delay = 2;
            this.__id32_.enemyType = 6;
            this.__id32_.interval = 1;
            this.__id32_.isRandom = false;
            this.__id32_.stopPointIdx = 0;
            this.__id32_.totalNum = 2;
            try
            {
               this.__id32_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id32___286() : *
      {
         if(this.__setPropDict[this.__id32_] == undefined || int(this.__setPropDict[this.__id32_]) != 4)
         {
            this.__setPropDict[this.__id32_] = 4;
            try
            {
               this.__id32_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id32_.delay = 2;
            this.__id32_.enemyType = 8;
            this.__id32_.interval = 1;
            this.__id32_.isRandom = false;
            this.__id32_.stopPointIdx = 0;
            this.__id32_.totalNum = 2;
            try
            {
               this.__id32_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id32___287() : *
      {
         if(this.__setPropDict[this.__id32_] == undefined || int(this.__setPropDict[this.__id32_]) != 5)
         {
            this.__setPropDict[this.__id32_] = 5;
            try
            {
               this.__id32_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id32_.delay = 2;
            this.__id32_.enemyType = 10;
            this.__id32_.interval = 1;
            this.__id32_.isRandom = false;
            this.__id32_.stopPointIdx = 0;
            this.__id32_.totalNum = 2;
            try
            {
               this.__id32_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id32___288() : *
      {
         if(this.__setPropDict[this.__id32_] == undefined || int(this.__setPropDict[this.__id32_]) != 6)
         {
            this.__setPropDict[this.__id32_] = 6;
            try
            {
               this.__id32_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id32_.delay = 2;
            this.__id32_.enemyType = 15;
            this.__id32_.interval = 1;
            this.__id32_.isRandom = false;
            this.__id32_.stopPointIdx = 0;
            this.__id32_.totalNum = 2;
            try
            {
               this.__id32_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id32___289() : *
      {
         if(this.__setPropDict[this.__id32_] == undefined || int(this.__setPropDict[this.__id32_]) != 7)
         {
            this.__setPropDict[this.__id32_] = 7;
            try
            {
               this.__id32_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id32_.delay = 2;
            this.__id32_.enemyType = 19;
            this.__id32_.interval = 1;
            this.__id32_.isRandom = false;
            this.__id32_.stopPointIdx = 0;
            this.__id32_.totalNum = 2;
            try
            {
               this.__id32_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id32___290() : *
      {
         if(this.__setPropDict[this.__id32_] == undefined || int(this.__setPropDict[this.__id32_]) != 8)
         {
            this.__setPropDict[this.__id32_] = 8;
            try
            {
               this.__id32_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id32_.delay = 2;
            this.__id32_.enemyType = 26;
            this.__id32_.interval = 1;
            this.__id32_.isRandom = false;
            this.__id32_.stopPointIdx = 0;
            this.__id32_.totalNum = 2;
            try
            {
               this.__id32_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id32___291() : *
      {
         if(this.__setPropDict[this.__id32_] == undefined || int(this.__setPropDict[this.__id32_]) != 9)
         {
            this.__setPropDict[this.__id32_] = 9;
            try
            {
               this.__id32_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id32_.delay = 5;
            this.__id32_.enemyType = 21;
            this.__id32_.interval = 1;
            this.__id32_.isRandom = false;
            this.__id32_.stopPointIdx = 0;
            this.__id32_.totalNum = 1;
            try
            {
               this.__id32_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id33__() : *
      {
         if(this.__setPropDict[this.__id33_] == undefined || int(this.__setPropDict[this.__id33_]) != 2)
         {
            this.__setPropDict[this.__id33_] = 2;
            try
            {
               this.__id33_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id33_.delay = 5;
            this.__id33_.enemyType = 4;
            this.__id33_.interval = 1;
            this.__id33_.isRandom = false;
            this.__id33_.stopPointIdx = 0;
            this.__id33_.totalNum = 1;
            try
            {
               this.__id33_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id33___292() : *
      {
         if(this.__setPropDict[this.__id33_] == undefined || int(this.__setPropDict[this.__id33_]) != 3)
         {
            this.__setPropDict[this.__id33_] = 3;
            try
            {
               this.__id33_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id33_.delay = 5;
            this.__id33_.enemyType = 8;
            this.__id33_.interval = 1;
            this.__id33_.isRandom = false;
            this.__id33_.stopPointIdx = 0;
            this.__id33_.totalNum = 1;
            try
            {
               this.__id33_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id33___293() : *
      {
         if(this.__setPropDict[this.__id33_] == undefined || int(this.__setPropDict[this.__id33_]) != 4)
         {
            this.__setPropDict[this.__id33_] = 4;
            try
            {
               this.__id33_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id33_.delay = 5;
            this.__id33_.enemyType = 10;
            this.__id33_.interval = 1;
            this.__id33_.isRandom = false;
            this.__id33_.stopPointIdx = 0;
            this.__id33_.totalNum = 1;
            try
            {
               this.__id33_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id33___294() : *
      {
         if(this.__setPropDict[this.__id33_] == undefined || int(this.__setPropDict[this.__id33_]) != 5)
         {
            this.__setPropDict[this.__id33_] = 5;
            try
            {
               this.__id33_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id33_.delay = 5;
            this.__id33_.enemyType = 12;
            this.__id33_.interval = 1;
            this.__id33_.isRandom = false;
            this.__id33_.stopPointIdx = 0;
            this.__id33_.totalNum = 1;
            try
            {
               this.__id33_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id33___295() : *
      {
         if(this.__setPropDict[this.__id33_] == undefined || int(this.__setPropDict[this.__id33_]) != 6)
         {
            this.__setPropDict[this.__id33_] = 6;
            try
            {
               this.__id33_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id33_.delay = 5;
            this.__id33_.enemyType = 16;
            this.__id33_.interval = 1;
            this.__id33_.isRandom = false;
            this.__id33_.stopPointIdx = 0;
            this.__id33_.totalNum = 1;
            try
            {
               this.__id33_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id33___296() : *
      {
         if(this.__setPropDict[this.__id33_] == undefined || int(this.__setPropDict[this.__id33_]) != 7)
         {
            this.__setPropDict[this.__id33_] = 7;
            try
            {
               this.__id33_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id33_.delay = 5;
            this.__id33_.enemyType = 26;
            this.__id33_.interval = 1;
            this.__id33_.isRandom = false;
            this.__id33_.stopPointIdx = 0;
            this.__id33_.totalNum = 1;
            try
            {
               this.__id33_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id33___297() : *
      {
         if(this.__setPropDict[this.__id33_] == undefined || int(this.__setPropDict[this.__id33_]) != 8)
         {
            this.__setPropDict[this.__id33_] = 8;
            try
            {
               this.__id33_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id33_.delay = 5;
            this.__id33_.enemyType = 20;
            this.__id33_.interval = 1;
            this.__id33_.isRandom = false;
            this.__id33_.stopPointIdx = 0;
            this.__id33_.totalNum = 1;
            try
            {
               this.__id33_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id33___298() : *
      {
         if(this.__setPropDict[this.__id33_] == undefined || int(this.__setPropDict[this.__id33_]) != 9)
         {
            this.__setPropDict[this.__id33_] = 9;
            try
            {
               this.__id33_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id33_.delay = 8;
            this.__id33_.enemyType = 22;
            this.__id33_.interval = 1;
            this.__id33_.isRandom = false;
            this.__id33_.stopPointIdx = 0;
            this.__id33_.totalNum = 1;
            try
            {
               this.__id33_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id34__() : *
      {
         if(this.__setPropDict[this.__id34_] == undefined || int(this.__setPropDict[this.__id34_]) != 2)
         {
            this.__setPropDict[this.__id34_] = 2;
            try
            {
               this.__id34_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id34_.delay = 4;
            this.__id34_.enemyType = 6;
            this.__id34_.interval = 1;
            this.__id34_.isRandom = false;
            this.__id34_.stopPointIdx = 3;
            this.__id34_.totalNum = 1;
            try
            {
               this.__id34_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id34___299() : *
      {
         if(this.__setPropDict[this.__id34_] == undefined || int(this.__setPropDict[this.__id34_]) != 3)
         {
            this.__setPropDict[this.__id34_] = 3;
            try
            {
               this.__id34_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id34_.delay = 4;
            this.__id34_.enemyType = 8;
            this.__id34_.interval = 1;
            this.__id34_.isRandom = false;
            this.__id34_.stopPointIdx = 3;
            this.__id34_.totalNum = 1;
            try
            {
               this.__id34_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id34___300() : *
      {
         if(this.__setPropDict[this.__id34_] == undefined || int(this.__setPropDict[this.__id34_]) != 4)
         {
            this.__setPropDict[this.__id34_] = 4;
            try
            {
               this.__id34_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id34_.delay = 4;
            this.__id34_.enemyType = 10;
            this.__id34_.interval = 1;
            this.__id34_.isRandom = false;
            this.__id34_.stopPointIdx = 3;
            this.__id34_.totalNum = 3;
            try
            {
               this.__id34_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id34___301() : *
      {
         if(this.__setPropDict[this.__id34_] == undefined || int(this.__setPropDict[this.__id34_]) != 5)
         {
            this.__setPropDict[this.__id34_] = 5;
            try
            {
               this.__id34_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id34_.delay = 4;
            this.__id34_.enemyType = 13;
            this.__id34_.interval = 1;
            this.__id34_.isRandom = false;
            this.__id34_.stopPointIdx = 3;
            this.__id34_.totalNum = 1;
            try
            {
               this.__id34_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id34___302() : *
      {
         if(this.__setPropDict[this.__id34_] == undefined || int(this.__setPropDict[this.__id34_]) != 6)
         {
            this.__setPropDict[this.__id34_] = 6;
            try
            {
               this.__id34_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id34_.delay = 4;
            this.__id34_.enemyType = 16;
            this.__id34_.interval = 1;
            this.__id34_.isRandom = false;
            this.__id34_.stopPointIdx = 3;
            this.__id34_.totalNum = 1;
            try
            {
               this.__id34_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id34___303() : *
      {
         if(this.__setPropDict[this.__id34_] == undefined || int(this.__setPropDict[this.__id34_]) != 7)
         {
            this.__setPropDict[this.__id34_] = 7;
            try
            {
               this.__id34_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id34_.delay = 4;
            this.__id34_.enemyType = 20;
            this.__id34_.interval = 1;
            this.__id34_.isRandom = false;
            this.__id34_.stopPointIdx = 3;
            this.__id34_.totalNum = 3;
            try
            {
               this.__id34_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id34___304() : *
      {
         if(this.__setPropDict[this.__id34_] == undefined || int(this.__setPropDict[this.__id34_]) != 8)
         {
            this.__setPropDict[this.__id34_] = 8;
            try
            {
               this.__id34_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id34_.delay = 4;
            this.__id34_.enemyType = 21;
            this.__id34_.interval = 1;
            this.__id34_.isRandom = false;
            this.__id34_.stopPointIdx = 3;
            this.__id34_.totalNum = 3;
            try
            {
               this.__id34_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id34___305() : *
      {
         if(this.__setPropDict[this.__id34_] == undefined || int(this.__setPropDict[this.__id34_]) != 9)
         {
            this.__setPropDict[this.__id34_] = 9;
            try
            {
               this.__id34_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id34_.delay = 2;
            this.__id34_.enemyType = 21;
            this.__id34_.interval = 1;
            this.__id34_.isRandom = false;
            this.__id34_.stopPointIdx = 1;
            this.__id34_.totalNum = 2;
            try
            {
               this.__id34_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id35__() : *
      {
         if(this.__setPropDict[this.__id35_] == undefined || int(this.__setPropDict[this.__id35_]) != 2)
         {
            this.__setPropDict[this.__id35_] = 2;
            try
            {
               this.__id35_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id35_.delay = 2;
            this.__id35_.enemyType = 6;
            this.__id35_.interval = 1;
            this.__id35_.isRandom = false;
            this.__id35_.stopPointIdx = 2;
            this.__id35_.totalNum = 1;
            try
            {
               this.__id35_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id35___306() : *
      {
         if(this.__setPropDict[this.__id35_] == undefined || int(this.__setPropDict[this.__id35_]) != 3)
         {
            this.__setPropDict[this.__id35_] = 3;
            try
            {
               this.__id35_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id35_.delay = 2;
            this.__id35_.enemyType = 8;
            this.__id35_.interval = 1;
            this.__id35_.isRandom = false;
            this.__id35_.stopPointIdx = 2;
            this.__id35_.totalNum = 1;
            try
            {
               this.__id35_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id35___307() : *
      {
         if(this.__setPropDict[this.__id35_] == undefined || int(this.__setPropDict[this.__id35_]) != 4)
         {
            this.__setPropDict[this.__id35_] = 4;
            try
            {
               this.__id35_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id35_.delay = 2;
            this.__id35_.enemyType = 10;
            this.__id35_.interval = 1;
            this.__id35_.isRandom = false;
            this.__id35_.stopPointIdx = 2;
            this.__id35_.totalNum = 1;
            try
            {
               this.__id35_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id35___308() : *
      {
         if(this.__setPropDict[this.__id35_] == undefined || int(this.__setPropDict[this.__id35_]) != 5)
         {
            this.__setPropDict[this.__id35_] = 5;
            try
            {
               this.__id35_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id35_.delay = 2;
            this.__id35_.enemyType = 13;
            this.__id35_.interval = 1;
            this.__id35_.isRandom = false;
            this.__id35_.stopPointIdx = 2;
            this.__id35_.totalNum = 2;
            try
            {
               this.__id35_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id35___309() : *
      {
         if(this.__setPropDict[this.__id35_] == undefined || int(this.__setPropDict[this.__id35_]) != 6)
         {
            this.__setPropDict[this.__id35_] = 6;
            try
            {
               this.__id35_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id35_.delay = 2;
            this.__id35_.enemyType = 16;
            this.__id35_.interval = 1;
            this.__id35_.isRandom = false;
            this.__id35_.stopPointIdx = 2;
            this.__id35_.totalNum = 1;
            try
            {
               this.__id35_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id35___310() : *
      {
         if(this.__setPropDict[this.__id35_] == undefined || int(this.__setPropDict[this.__id35_]) != 7)
         {
            this.__setPropDict[this.__id35_] = 7;
            try
            {
               this.__id35_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id35_.delay = 2;
            this.__id35_.enemyType = 20;
            this.__id35_.interval = 1;
            this.__id35_.isRandom = false;
            this.__id35_.stopPointIdx = 2;
            this.__id35_.totalNum = 2;
            try
            {
               this.__id35_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id35___311() : *
      {
         if(this.__setPropDict[this.__id35_] == undefined || int(this.__setPropDict[this.__id35_]) != 8)
         {
            this.__setPropDict[this.__id35_] = 8;
            try
            {
               this.__id35_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id35_.delay = 2;
            this.__id35_.enemyType = 21;
            this.__id35_.interval = 1;
            this.__id35_.isRandom = false;
            this.__id35_.stopPointIdx = 2;
            this.__id35_.totalNum = 2;
            try
            {
               this.__id35_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id35___312() : *
      {
         if(this.__setPropDict[this.__id35_] == undefined || int(this.__setPropDict[this.__id35_]) != 9)
         {
            this.__setPropDict[this.__id35_] = 9;
            try
            {
               this.__id35_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id35_.delay = 2;
            this.__id35_.enemyType = 21;
            this.__id35_.interval = 1;
            this.__id35_.isRandom = false;
            this.__id35_.stopPointIdx = 1;
            this.__id35_.totalNum = 1;
            try
            {
               this.__id35_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id36__() : *
      {
         if(this.__setPropDict[this.__id36_] == undefined || int(this.__setPropDict[this.__id36_]) != 13)
         {
            this.__setPropDict[this.__id36_] = 13;
            try
            {
               this.__id36_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id36_.delay = 2;
            this.__id36_.enemyType = 36;
            this.__id36_.interval = 1;
            this.__id36_.isRandom = false;
            this.__id36_.stopPointIdx = 1;
            this.__id36_.totalNum = 1;
            try
            {
               this.__id36_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id37__() : *
      {
         if(this.__setPropDict[this.__id37_] == undefined || int(this.__setPropDict[this.__id37_]) != 13)
         {
            this.__setPropDict[this.__id37_] = 13;
            try
            {
               this.__id37_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id37_.delay = 9;
            this.__id37_.enemyType = 36;
            this.__id37_.interval = 1;
            this.__id37_.isRandom = false;
            this.__id37_.stopPointIdx = 1;
            this.__id37_.totalNum = 3;
            try
            {
               this.__id37_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id38__() : *
      {
         if(this.__setPropDict[this.__id38_] == undefined || int(this.__setPropDict[this.__id38_]) != 13)
         {
            this.__setPropDict[this.__id38_] = 13;
            try
            {
               this.__id38_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id38_.delay = 0;
            this.__id38_.enemyType = 35;
            this.__id38_.interval = 1;
            this.__id38_.isRandom = false;
            this.__id38_.stopPointIdx = 2;
            this.__id38_.totalNum = 1;
            try
            {
               this.__id38_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id39__() : *
      {
         if(this.__setPropDict[this.__id39_] == undefined || int(this.__setPropDict[this.__id39_]) != 13)
         {
            this.__setPropDict[this.__id39_] = 13;
            try
            {
               this.__id39_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id39_.delay = 2;
            this.__id39_.enemyType = 36;
            this.__id39_.interval = 1;
            this.__id39_.isRandom = false;
            this.__id39_.stopPointIdx = 2;
            this.__id39_.totalNum = 3;
            try
            {
               this.__id39_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id40__() : *
      {
         if(this.__setPropDict[this.__id40_] == undefined || int(this.__setPropDict[this.__id40_]) != 13)
         {
            this.__setPropDict[this.__id40_] = 13;
            try
            {
               this.__id40_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id40_.delay = 0;
            this.__id40_.enemyType = 35;
            this.__id40_.interval = 1;
            this.__id40_.isRandom = false;
            this.__id40_.stopPointIdx = 1;
            this.__id40_.totalNum = 1;
            try
            {
               this.__id40_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id41__() : *
      {
         if(this.__setPropDict[this.__id41_] == undefined || int(this.__setPropDict[this.__id41_]) != 13)
         {
            this.__setPropDict[this.__id41_] = 13;
            try
            {
               this.__id41_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id41_.delay = 2;
            this.__id41_.enemyType = 36;
            this.__id41_.interval = 1;
            this.__id41_.isRandom = false;
            this.__id41_.stopPointIdx = 4;
            this.__id41_.totalNum = 2;
            try
            {
               this.__id41_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id42__() : *
      {
         if(this.__setPropDict[this.__id42_] == undefined || int(this.__setPropDict[this.__id42_]) != 13)
         {
            this.__setPropDict[this.__id42_] = 13;
            try
            {
               this.__id42_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id42_.delay = 2;
            this.__id42_.enemyType = 35;
            this.__id42_.interval = 1;
            this.__id42_.isRandom = false;
            this.__id42_.stopPointIdx = 3;
            this.__id42_.totalNum = 2;
            try
            {
               this.__id42_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id43__() : *
      {
         if(this.__setPropDict[this.__id43_] == undefined || int(this.__setPropDict[this.__id43_]) != 13)
         {
            this.__setPropDict[this.__id43_] = 13;
            try
            {
               this.__id43_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id43_.delay = 0;
            this.__id43_.enemyType = 36;
            this.__id43_.interval = 1;
            this.__id43_.isRandom = false;
            this.__id43_.stopPointIdx = 3;
            this.__id43_.totalNum = 3;
            try
            {
               this.__id43_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id44__() : *
      {
         if(this.__setPropDict[this.__id44_] == undefined || int(this.__setPropDict[this.__id44_]) != 13)
         {
            this.__setPropDict[this.__id44_] = 13;
            try
            {
               this.__id44_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id44_.delay = 0;
            this.__id44_.enemyType = 36;
            this.__id44_.interval = 1;
            this.__id44_.isRandom = false;
            this.__id44_.stopPointIdx = 4;
            this.__id44_.totalNum = 2;
            try
            {
               this.__id44_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id45__() : *
      {
         if(this.__setPropDict[this.__id45_] == undefined || int(this.__setPropDict[this.__id45_]) != 13)
         {
            this.__setPropDict[this.__id45_] = 13;
            try
            {
               this.__id45_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id45_.delay = 7;
            this.__id45_.enemyType = 37;
            this.__id45_.interval = 1;
            this.__id45_.isRandom = false;
            this.__id45_.stopPointIdx = 4;
            this.__id45_.totalNum = 1;
            try
            {
               this.__id45_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id46__() : *
      {
         if(this.__setPropDict[this.__id46_] == undefined || int(this.__setPropDict[this.__id46_]) != 13)
         {
            this.__setPropDict[this.__id46_] = 13;
            try
            {
               this.__id46_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id46_.delay = 3;
            this.__id46_.enemyType = 36;
            this.__id46_.interval = 1;
            this.__id46_.isRandom = false;
            this.__id46_.stopPointIdx = 2;
            this.__id46_.totalNum = 2;
            try
            {
               this.__id46_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id47__() : *
      {
         if(this.__setPropDict[this.__id47_] == undefined || int(this.__setPropDict[this.__id47_]) != 13)
         {
            this.__setPropDict[this.__id47_] = 13;
            try
            {
               this.__id47_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id47_.delay = 3;
            this.__id47_.enemyType = 35;
            this.__id47_.interval = 1;
            this.__id47_.isRandom = false;
            this.__id47_.stopPointIdx = 3;
            this.__id47_.totalNum = 2;
            try
            {
               this.__id47_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id48__() : *
      {
         if(this.__setPropDict[this.__id48_] == undefined || int(this.__setPropDict[this.__id48_]) != 13)
         {
            this.__setPropDict[this.__id48_] = 13;
            try
            {
               this.__id48_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id48_.delay = 9;
            this.__id48_.enemyType = 36;
            this.__id48_.interval = 1;
            this.__id48_.isRandom = false;
            this.__id48_.stopPointIdx = 3;
            this.__id48_.totalNum = 3;
            try
            {
               this.__id48_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id49__() : *
      {
         if(this.__setPropDict[this.__id49_] == undefined || int(this.__setPropDict[this.__id49_]) != 13)
         {
            this.__setPropDict[this.__id49_] = 13;
            try
            {
               this.__id49_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id49_.delay = 0;
            this.__id49_.enemyType = 36;
            this.__id49_.interval = 1;
            this.__id49_.isRandom = false;
            this.__id49_.stopPointIdx = 1;
            this.__id49_.totalNum = 3;
            try
            {
               this.__id49_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id50__() : *
      {
         if(this.__setPropDict[this.__id50_] == undefined || int(this.__setPropDict[this.__id50_]) != 13)
         {
            this.__setPropDict[this.__id50_] = 13;
            try
            {
               this.__id50_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id50_.delay = 2;
            this.__id50_.enemyType = 35;
            this.__id50_.interval = 1;
            this.__id50_.isRandom = false;
            this.__id50_.stopPointIdx = 1;
            this.__id50_.totalNum = 2;
            try
            {
               this.__id50_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id51__() : *
      {
         if(this.__setPropDict[this.__id51_] == undefined || int(this.__setPropDict[this.__id51_]) != 13)
         {
            this.__setPropDict[this.__id51_] = 13;
            try
            {
               this.__id51_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id51_.delay = 2;
            this.__id51_.enemyType = 35;
            this.__id51_.interval = 1;
            this.__id51_.isRandom = false;
            this.__id51_.stopPointIdx = 4;
            this.__id51_.totalNum = 2;
            try
            {
               this.__id51_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id52__() : *
      {
         if(this.__setPropDict[this.__id52_] == undefined || int(this.__setPropDict[this.__id52_]) != 13)
         {
            this.__setPropDict[this.__id52_] = 13;
            try
            {
               this.__id52_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id52_.delay = 2;
            this.__id52_.enemyType = 35;
            this.__id52_.interval = 1;
            this.__id52_.isRandom = false;
            this.__id52_.stopPointIdx = 2;
            this.__id52_.totalNum = 3;
            try
            {
               this.__id52_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id53__() : *
      {
         if(this.__setPropDict[this.__id53_] == undefined || int(this.__setPropDict[this.__id53_]) != 13)
         {
            this.__setPropDict[this.__id53_] = 13;
            try
            {
               this.__id53_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id53_.delay = 0;
            this.__id53_.enemyType = 35;
            this.__id53_.interval = 1;
            this.__id53_.isRandom = false;
            this.__id53_.stopPointIdx = 2;
            this.__id53_.totalNum = 2;
            try
            {
               this.__id53_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id54__() : *
      {
         if(this.__setPropDict[this.__id54_] == undefined || int(this.__setPropDict[this.__id54_]) != 13)
         {
            this.__setPropDict[this.__id54_] = 13;
            try
            {
               this.__id54_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id54_.delay = 1;
            this.__id54_.enemyType = 36;
            this.__id54_.interval = 1;
            this.__id54_.isRandom = false;
            this.__id54_.stopPointIdx = 2;
            this.__id54_.totalNum = 3;
            try
            {
               this.__id54_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id55__() : *
      {
         if(this.__setPropDict[this.__id55_] == undefined || int(this.__setPropDict[this.__id55_]) != 13)
         {
            this.__setPropDict[this.__id55_] = 13;
            try
            {
               this.__id55_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id55_.delay = 0;
            this.__id55_.enemyType = 36;
            this.__id55_.interval = 1;
            this.__id55_.isRandom = false;
            this.__id55_.stopPointIdx = 2;
            this.__id55_.totalNum = 2;
            try
            {
               this.__id55_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id56__() : *
      {
         if(this.__setPropDict[this.__id56_] == undefined || int(this.__setPropDict[this.__id56_]) != 13)
         {
            this.__setPropDict[this.__id56_] = 13;
            try
            {
               this.__id56_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id56_.delay = 9;
            this.__id56_.enemyType = 36;
            this.__id56_.interval = 1;
            this.__id56_.isRandom = false;
            this.__id56_.stopPointIdx = 2;
            this.__id56_.totalNum = 2;
            try
            {
               this.__id56_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id57__() : *
      {
         if(this.__setPropDict[this.__id57_] == undefined || int(this.__setPropDict[this.__id57_]) != 13)
         {
            this.__setPropDict[this.__id57_] = 13;
            try
            {
               this.__id57_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id57_.delay = 3;
            this.__id57_.enemyType = 36;
            this.__id57_.interval = 1;
            this.__id57_.isRandom = false;
            this.__id57_.stopPointIdx = 3;
            this.__id57_.totalNum = 3;
            try
            {
               this.__id57_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id58__() : *
      {
         if(this.__setPropDict[this.__id58_] == undefined || int(this.__setPropDict[this.__id58_]) != 13)
         {
            this.__setPropDict[this.__id58_] = 13;
            try
            {
               this.__id58_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id58_.delay = 4;
            this.__id58_.enemyType = 35;
            this.__id58_.interval = 1;
            this.__id58_.isRandom = false;
            this.__id58_.stopPointIdx = 3;
            this.__id58_.totalNum = 4;
            try
            {
               this.__id58_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id59__() : *
      {
         if(this.__setPropDict[this.__id59_] == undefined || int(this.__setPropDict[this.__id59_]) != 13)
         {
            this.__setPropDict[this.__id59_] = 13;
            try
            {
               this.__id59_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id59_.delay = 0;
            this.__id59_.enemyType = 35;
            this.__id59_.interval = 1;
            this.__id59_.isRandom = false;
            this.__id59_.stopPointIdx = 3;
            this.__id59_.totalNum = 2;
            try
            {
               this.__id59_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id60__() : *
      {
         if(this.__setPropDict[this.__id60_] == undefined || int(this.__setPropDict[this.__id60_]) != 13)
         {
            this.__setPropDict[this.__id60_] = 13;
            try
            {
               this.__id60_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id60_.delay = 5;
            this.__id60_.enemyType = 35;
            this.__id60_.interval = 1;
            this.__id60_.isRandom = false;
            this.__id60_.stopPointIdx = 3;
            this.__id60_.totalNum = 3;
            try
            {
               this.__id60_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id61__() : *
      {
         if(this.__setPropDict[this.__id61_] == undefined || int(this.__setPropDict[this.__id61_]) != 13)
         {
            this.__setPropDict[this.__id61_] = 13;
            try
            {
               this.__id61_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id61_.delay = 0;
            this.__id61_.enemyType = 36;
            this.__id61_.interval = 1;
            this.__id61_.isRandom = false;
            this.__id61_.stopPointIdx = 4;
            this.__id61_.totalNum = 1;
            try
            {
               this.__id61_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id62__() : *
      {
         if(this.__setPropDict[this.__id62_] == undefined || int(this.__setPropDict[this.__id62_]) != 13)
         {
            this.__setPropDict[this.__id62_] = 13;
            try
            {
               this.__id62_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id62_.delay = 0;
            this.__id62_.enemyType = 35;
            this.__id62_.interval = 1;
            this.__id62_.isRandom = false;
            this.__id62_.stopPointIdx = 0;
            this.__id62_.totalNum = 3;
            try
            {
               this.__id62_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id63__() : *
      {
         if(this.__setPropDict[this.__id63_] == undefined || int(this.__setPropDict[this.__id63_]) != 13)
         {
            this.__setPropDict[this.__id63_] = 13;
            try
            {
               this.__id63_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id63_.delay = 0;
            this.__id63_.enemyType = 35;
            this.__id63_.interval = 1;
            this.__id63_.isRandom = false;
            this.__id63_.stopPointIdx = 0;
            this.__id63_.totalNum = 3;
            try
            {
               this.__id63_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id64__() : *
      {
         if(this.__setPropDict[this.__id64_] == undefined || int(this.__setPropDict[this.__id64_]) != 13)
         {
            this.__setPropDict[this.__id64_] = 13;
            try
            {
               this.__id64_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id64_.delay = 0;
            this.__id64_.enemyType = 36;
            this.__id64_.interval = 1;
            this.__id64_.isRandom = false;
            this.__id64_.stopPointIdx = 0;
            this.__id64_.totalNum = 1;
            try
            {
               this.__id64_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id65__() : *
      {
         if(this.__setPropDict[this.__id65_] == undefined || int(this.__setPropDict[this.__id65_]) != 13)
         {
            this.__setPropDict[this.__id65_] = 13;
            try
            {
               this.__id65_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id65_.delay = 0;
            this.__id65_.enemyType = 35;
            this.__id65_.interval = 1;
            this.__id65_.isRandom = false;
            this.__id65_.stopPointIdx = 0;
            this.__id65_.totalNum = 5;
            try
            {
               this.__id65_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id66__() : *
      {
         if(this.__setPropDict[this.__id66_] == undefined || int(this.__setPropDict[this.__id66_]) != 13)
         {
            this.__setPropDict[this.__id66_] = 13;
            try
            {
               this.__id66_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id66_.delay = 0;
            this.__id66_.enemyType = 35;
            this.__id66_.interval = 1;
            this.__id66_.isRandom = false;
            this.__id66_.stopPointIdx = 0;
            this.__id66_.totalNum = 3;
            try
            {
               this.__id66_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id67__() : *
      {
         if(this.__setPropDict[this.__id67_] == undefined || int(this.__setPropDict[this.__id67_]) != 13)
         {
            this.__setPropDict[this.__id67_] = 13;
            try
            {
               this.__id67_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id67_.delay = 0;
            this.__id67_.enemyType = 36;
            this.__id67_.interval = 1;
            this.__id67_.isRandom = false;
            this.__id67_.stopPointIdx = 0;
            this.__id67_.totalNum = 2;
            try
            {
               this.__id67_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function frame1() : *
      {
         this.__setProp___id31__();
         this.__setProp___id30__();
         this.__setProp___id29__();
         this.__setProp___id28__();
         this.__setProp___id27__();
         this.__setProp___id26__();
         this.__setProp___id25__();
         this.__setProp___id24__();
         this.__setProp___id23__();
         this.__setProp___id22__();
         this.__setProp___id21__();
         this.__setProp___id20__();
         this.__setProp___id19__();
         this.__setProp___id18__();
         this.__setProp___id17__();
         this.__setProp___id16__();
         this.__setProp___id15__();
         this.__setProp___id14__();
         this.__setProp___id13__();
         this.__setProp___id12__();
         this.__setProp___id11__();
         this.__setProp___id10__();
         this.__setProp___id9__();
         this.__setProp___id8__();
         this.__setProp___id7__();
         this.__setProp___id6__();
         this.__setProp___id5__();
         this.__setProp___id4__();
         this.__setProp___id3__();
         this.__setProp___id2__();
         this.__setProp___id1__();
         this.__setProp___id0__();
      }
      
      internal function frame2() : *
      {
         this.__setProp___id35__();
         this.__setProp___id34__();
         this.__setProp___id33__();
         this.__setProp___id32__();
         this.__setProp___id31___277();
         this.__setProp___id30___269();
         this.__setProp___id29___261();
         this.__setProp___id28___253();
         this.__setProp___id27___245();
         this.__setProp___id26___237();
         this.__setProp___id25___229();
         this.__setProp___id24___221();
         this.__setProp___id23___213();
         this.__setProp___id22___205();
         this.__setProp___id21___197();
         this.__setProp___id20___189();
         this.__setProp___id19___181();
         this.__setProp___id18___173();
         this.__setProp___id17___165();
         this.__setProp___id16___157();
         this.__setProp___id15___149();
         this.__setProp___id14___140();
         this.__setProp___id13___131();
         this.__setProp___id12___122();
         this.__setProp___id11___113();
         this.__setProp___id10___104();
         this.__setProp___id9___95();
         this.__setProp___id8___85();
         this.__setProp___id7___75();
         this.__setProp___id6___65();
         this.__setProp___id5___55();
         this.__setProp___id4___45();
         this.__setProp___id3___35();
         this.__setProp___id2___25();
         this.__setProp___id1___13();
         this.__setProp___id0___1();
      }
      
      internal function frame3() : *
      {
         this.__setProp___id35___306();
         this.__setProp___id34___299();
         this.__setProp___id33___292();
         this.__setProp___id32___285();
         this.__setProp___id31___278();
         this.__setProp___id30___270();
         this.__setProp___id29___262();
         this.__setProp___id28___254();
         this.__setProp___id27___246();
         this.__setProp___id26___238();
         this.__setProp___id25___230();
         this.__setProp___id24___222();
         this.__setProp___id23___214();
         this.__setProp___id22___206();
         this.__setProp___id21___198();
         this.__setProp___id20___190();
         this.__setProp___id19___182();
         this.__setProp___id18___174();
         this.__setProp___id17___166();
         this.__setProp___id16___158();
         this.__setProp___id15___150();
         this.__setProp___id14___141();
         this.__setProp___id13___132();
         this.__setProp___id12___123();
         this.__setProp___id11___114();
         this.__setProp___id10___105();
         this.__setProp___id9___96();
         this.__setProp___id8___86();
         this.__setProp___id7___76();
         this.__setProp___id6___66();
         this.__setProp___id5___56();
         this.__setProp___id4___46();
         this.__setProp___id3___36();
         this.__setProp___id2___26();
         this.__setProp___id1___14();
         this.__setProp___id0___2();
      }
      
      internal function frame4() : *
      {
         this.__setProp___id35___307();
         this.__setProp___id34___300();
         this.__setProp___id33___293();
         this.__setProp___id32___286();
         this.__setProp___id31___279();
         this.__setProp___id30___271();
         this.__setProp___id29___263();
         this.__setProp___id28___255();
         this.__setProp___id27___247();
         this.__setProp___id26___239();
         this.__setProp___id25___231();
         this.__setProp___id24___223();
         this.__setProp___id23___215();
         this.__setProp___id22___207();
         this.__setProp___id21___199();
         this.__setProp___id20___191();
         this.__setProp___id19___183();
         this.__setProp___id18___175();
         this.__setProp___id17___167();
         this.__setProp___id16___159();
         this.__setProp___id15___151();
         this.__setProp___id14___142();
         this.__setProp___id13___133();
         this.__setProp___id12___124();
         this.__setProp___id11___115();
         this.__setProp___id10___106();
         this.__setProp___id9___97();
         this.__setProp___id8___87();
         this.__setProp___id7___77();
         this.__setProp___id6___67();
         this.__setProp___id5___57();
         this.__setProp___id4___47();
         this.__setProp___id3___37();
         this.__setProp___id2___27();
         this.__setProp___id1___15();
         this.__setProp___id0___3();
      }
      
      internal function frame5() : *
      {
         this.__setProp___id35___308();
         this.__setProp___id34___301();
         this.__setProp___id33___294();
         this.__setProp___id32___287();
         this.__setProp___id31___280();
         this.__setProp___id30___272();
         this.__setProp___id29___264();
         this.__setProp___id28___256();
         this.__setProp___id27___248();
         this.__setProp___id26___240();
         this.__setProp___id25___232();
         this.__setProp___id24___224();
         this.__setProp___id23___216();
         this.__setProp___id22___208();
         this.__setProp___id21___200();
         this.__setProp___id20___192();
         this.__setProp___id19___184();
         this.__setProp___id18___176();
         this.__setProp___id17___168();
         this.__setProp___id16___160();
         this.__setProp___id15___152();
         this.__setProp___id14___143();
         this.__setProp___id13___134();
         this.__setProp___id12___125();
         this.__setProp___id11___116();
         this.__setProp___id10___107();
         this.__setProp___id9___98();
         this.__setProp___id8___88();
         this.__setProp___id7___78();
         this.__setProp___id6___68();
         this.__setProp___id5___58();
         this.__setProp___id4___48();
         this.__setProp___id3___38();
         this.__setProp___id2___28();
         this.__setProp___id1___16();
         this.__setProp___id0___4();
      }
      
      internal function frame6() : *
      {
         this.__setProp___id35___309();
         this.__setProp___id34___302();
         this.__setProp___id33___295();
         this.__setProp___id32___288();
         this.__setProp___id31___281();
         this.__setProp___id30___273();
         this.__setProp___id29___265();
         this.__setProp___id28___257();
         this.__setProp___id27___249();
         this.__setProp___id26___241();
         this.__setProp___id25___233();
         this.__setProp___id24___225();
         this.__setProp___id23___217();
         this.__setProp___id22___209();
         this.__setProp___id21___201();
         this.__setProp___id20___193();
         this.__setProp___id19___185();
         this.__setProp___id18___177();
         this.__setProp___id17___169();
         this.__setProp___id16___161();
         this.__setProp___id15___153();
         this.__setProp___id14___144();
         this.__setProp___id13___135();
         this.__setProp___id12___126();
         this.__setProp___id11___117();
         this.__setProp___id10___108();
         this.__setProp___id9___99();
         this.__setProp___id8___89();
         this.__setProp___id7___79();
         this.__setProp___id6___69();
         this.__setProp___id5___59();
         this.__setProp___id4___49();
         this.__setProp___id3___39();
         this.__setProp___id2___29();
         this.__setProp___id1___17();
         this.__setProp___id0___5();
      }
      
      internal function frame7() : *
      {
         this.__setProp___id35___310();
         this.__setProp___id34___303();
         this.__setProp___id33___296();
         this.__setProp___id32___289();
         this.__setProp___id31___282();
         this.__setProp___id30___274();
         this.__setProp___id29___266();
         this.__setProp___id28___258();
         this.__setProp___id27___250();
         this.__setProp___id26___242();
         this.__setProp___id25___234();
         this.__setProp___id24___226();
         this.__setProp___id23___218();
         this.__setProp___id22___210();
         this.__setProp___id21___202();
         this.__setProp___id20___194();
         this.__setProp___id19___186();
         this.__setProp___id18___178();
         this.__setProp___id17___170();
         this.__setProp___id16___162();
         this.__setProp___id15___154();
         this.__setProp___id14___145();
         this.__setProp___id13___136();
         this.__setProp___id12___127();
         this.__setProp___id11___118();
         this.__setProp___id10___109();
         this.__setProp___id9___100();
         this.__setProp___id8___90();
         this.__setProp___id7___80();
         this.__setProp___id6___70();
         this.__setProp___id5___60();
         this.__setProp___id4___50();
         this.__setProp___id3___40();
         this.__setProp___id2___30();
         this.__setProp___id1___18();
         this.__setProp___id0___6();
      }
      
      internal function frame8() : *
      {
         this.__setProp___id35___311();
         this.__setProp___id34___304();
         this.__setProp___id33___297();
         this.__setProp___id32___290();
         this.__setProp___id31___283();
         this.__setProp___id30___275();
         this.__setProp___id29___267();
         this.__setProp___id28___259();
         this.__setProp___id27___251();
         this.__setProp___id26___243();
         this.__setProp___id25___235();
         this.__setProp___id24___227();
         this.__setProp___id23___219();
         this.__setProp___id22___211();
         this.__setProp___id21___203();
         this.__setProp___id20___195();
         this.__setProp___id19___187();
         this.__setProp___id18___179();
         this.__setProp___id17___171();
         this.__setProp___id16___163();
         this.__setProp___id15___155();
         this.__setProp___id14___146();
         this.__setProp___id13___137();
         this.__setProp___id12___128();
         this.__setProp___id11___119();
         this.__setProp___id10___110();
         this.__setProp___id9___101();
         this.__setProp___id8___91();
         this.__setProp___id7___81();
         this.__setProp___id6___71();
         this.__setProp___id5___61();
         this.__setProp___id4___51();
         this.__setProp___id3___41();
         this.__setProp___id2___31();
         this.__setProp___id1___19();
         this.__setProp___id0___7();
      }
      
      internal function frame9() : *
      {
         this.__setProp___id35___312();
         this.__setProp___id34___305();
         this.__setProp___id33___298();
         this.__setProp___id32___291();
         this.__setProp___id31___284();
         this.__setProp___id30___276();
         this.__setProp___id29___268();
         this.__setProp___id28___260();
         this.__setProp___id27___252();
         this.__setProp___id26___244();
         this.__setProp___id25___236();
         this.__setProp___id24___228();
         this.__setProp___id23___220();
         this.__setProp___id22___212();
         this.__setProp___id21___204();
         this.__setProp___id20___196();
         this.__setProp___id19___188();
         this.__setProp___id18___180();
         this.__setProp___id17___172();
         this.__setProp___id16___164();
         this.__setProp___id15___156();
         this.__setProp___id14___147();
         this.__setProp___id13___138();
         this.__setProp___id12___129();
         this.__setProp___id11___120();
         this.__setProp___id10___111();
         this.__setProp___id9___102();
         this.__setProp___id8___92();
         this.__setProp___id7___82();
         this.__setProp___id6___72();
         this.__setProp___id5___62();
         this.__setProp___id4___52();
         this.__setProp___id3___42();
         this.__setProp___id2___32();
         this.__setProp___id1___20();
         this.__setProp___id0___8();
      }
      
      internal function frame10() : *
      {
         this.__setProp___id14___148();
         this.__setProp___id13___139();
         this.__setProp___id12___130();
         this.__setProp___id11___121();
         this.__setProp___id10___112();
         this.__setProp___id9___103();
         this.__setProp___id8___93();
         this.__setProp___id7___83();
         this.__setProp___id6___73();
         this.__setProp___id5___63();
         this.__setProp___id4___53();
         this.__setProp___id3___43();
         this.__setProp___id2___33();
         this.__setProp___id1___21();
         this.__setProp___id0___9();
      }
      
      internal function frame11() : *
      {
         this.__setProp___id8___94();
         this.__setProp___id7___84();
         this.__setProp___id6___74();
         this.__setProp___id5___64();
         this.__setProp___id4___54();
         this.__setProp___id3___44();
         this.__setProp___id2___34();
         this.__setProp___id1___22();
         this.__setProp___id0___10();
      }
      
      internal function frame12() : *
      {
         this.__setProp___id1___23();
         this.__setProp___id0___11();
      }
      
      internal function frame13() : *
      {
         this.__setProp___id67__();
         this.__setProp___id66__();
         this.__setProp___id65__();
         this.__setProp___id64__();
         this.__setProp___id63__();
         this.__setProp___id62__();
         this.__setProp___id61__();
         this.__setProp___id60__();
         this.__setProp___id59__();
         this.__setProp___id58__();
         this.__setProp___id57__();
         this.__setProp___id56__();
         this.__setProp___id55__();
         this.__setProp___id54__();
         this.__setProp___id53__();
         this.__setProp___id52__();
         this.__setProp___id51__();
         this.__setProp___id50__();
         this.__setProp___id49__();
         this.__setProp___id48__();
         this.__setProp___id47__();
         this.__setProp___id46__();
         this.__setProp___id45__();
         this.__setProp___id44__();
         this.__setProp___id43__();
         this.__setProp___id42__();
         this.__setProp___id41__();
         this.__setProp___id40__();
         this.__setProp___id39__();
         this.__setProp___id38__();
         this.__setProp___id37__();
         this.__setProp___id36__();
         this.__setProp___id1___24();
         this.__setProp___id0___12();
      }
   }
}

