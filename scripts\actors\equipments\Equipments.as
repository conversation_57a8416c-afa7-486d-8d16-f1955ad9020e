package actors.equipments
{
   import util.GameUtility;
   
   public class Equipments
   {
      
      public var _special1:Equipment;
      
      public var _role1Weapon1:Equipment;
      
      public var _role1Weapon2:Equipment;
      
      public var _role1Weapon3:Equipment;
      
      public var _role1Weapon4:Equipment;
      
      public var _role1Weapon5:Equipment;
      
      public var _role1Weapon6:Equipment;
      
      public var _role1Weapon7:Equipment;
      
      public var _role1Weapon8:Equipment;
      
      public var _role1Weapon9:Equipment;
      
      public var _role1Weapon10:Equipment;
      
      public var _role1Weapon11:Equipment;
      
      public var _role1Weapon12:Equipment;
      
      public var _role1Armor1:Equipment;
      
      public var _role1Armor2:Equipment;
      
      public var _role1Armor3:Equipment;
      
      public var _role1Armor4:Equipment;
      
      public var _role1Armor5:Equipment;
      
      public var _role1Armor6:Equipment;
      
      public var _role1Armor7:Equipment;
      
      public var _role1Armor8:Equipment;
      
      public var _role1Armor9:Equipment;
      
      public var _role1Armor10:Equipment;
      
      public var _role1Armor11:Equipment;
      
      public var _role1Armor12:Equipment;
      
      public var _role1Armor13:Equipment;
      
      public var _role1Armor14:Equipment;
      
      public var _role1Armor15:Equipment;
      
      public var _role1Armor16:Equipment;
      
      public var _role1Armor17:Equipment;
      
      public var _role1Armor18:Equipment;
      
      public var _role1Armor19:Equipment;
      
      public var _role1Armor20:Equipment;
      
      public var _role2Weapon1:Equipment;
      
      public var _role2Weapon2:Equipment;
      
      public var _role2Weapon3:Equipment;
      
      public var _role2Weapon4:Equipment;
      
      public var _role2Weapon5:Equipment;
      
      public var _role2Weapon6:Equipment;
      
      public var _role2Weapon7:Equipment;
      
      public var _role2Weapon8:Equipment;
      
      public var _role2Weapon9:Equipment;
      
      public var _role2Weapon10:Equipment;
      
      public var _role2Weapon11:Equipment;
      
      public var _role2Weapon12:Equipment;
      
      public var _role2Armor1:Equipment;
      
      public var _role2Armor2:Equipment;
      
      public var _role2Armor3:Equipment;
      
      public var _role2Armor4:Equipment;
      
      public var _role2Armor5:Equipment;
      
      public var _role2Armor6:Equipment;
      
      public var _role2Armor7:Equipment;
      
      public var _role2Armor8:Equipment;
      
      public var _role2Armor9:Equipment;
      
      public var _role2Armor10:Equipment;
      
      public var _role2Armor11:Equipment;
      
      public var _role2Armor12:Equipment;
      
      public var _role2Armor13:Equipment;
      
      public var _role2Armor14:Equipment;
      
      public var _role2Armor15:Equipment;
      
      public var _role2Armor16:Equipment;
      
      public var _role2Armor17:Equipment;
      
      public var _role2Armor18:Equipment;
      
      public var _role2Armor19:Equipment;
      
      public var _role2Armor20:Equipment;
      
      public var _role3Weapon1:Equipment;
      
      public var _role3Weapon2:Equipment;
      
      public var _role3Weapon3:Equipment;
      
      public var _role3Weapon4:Equipment;
      
      public var _role3Weapon5:Equipment;
      
      public var _role3Weapon6:Equipment;
      
      public var _role3Weapon7:Equipment;
      
      public var _role3Weapon8:Equipment;
      
      public var _role3Weapon9:Equipment;
      
      public var _role3Weapon10:Equipment;
      
      public var _role3Weapon11:Equipment;
      
      public var _role3Armor1:Equipment;
      
      public var _role3Armor2:Equipment;
      
      public var _role3Armor3:Equipment;
      
      public var _role3Armor4:Equipment;
      
      public var _role3Armor5:Equipment;
      
      public var _role3Armor6:Equipment;
      
      public var _role3Armor7:Equipment;
      
      public var _role3Armor8:Equipment;
      
      public var _role3Armor9:Equipment;
      
      public var _role3Armor10:Equipment;
      
      public var _role3Armor11:Equipment;
      
      public var _role3Armor12:Equipment;
      
      public var _role3Armor13:Equipment;
      
      public var _role3Armor14:Equipment;
      
      public var _role3Armor15:Equipment;
      
      public var _role3Armor16:Equipment;
      
      public var _role3Armor17:Equipment;
      
      public var _role3Armor18:Equipment;
      
      public var _role3Armor19:Equipment;
      
      public var _role3Armor20:Equipment;
      
      public var _decoration1:Equipment;
      
      public var _decoration2:Equipment;
      
      public var _decoration3:Equipment;
      
      public var _decoration4:Equipment;
      
      public var _decoration5:Equipment;
      
      public var _decoration6:Equipment;
      
      public var _suitCoat1:Equipment;
      
      public var _role1Weapons:Array = [];
      
      public var _role1Armors:Array = [];
      
      public var _role2Weapons:Array = [];
      
      public var _role2Armors:Array = [];
      
      public var _role3Weapons:Array = [];
      
      public var _role3Armors:Array = [];
      
      public var _decorations:Array = [];
      
      public var _suitCoats:Array = [];
      
      public var _specials:Array = [];
      
      public var _suitCoat2:Equipment;
      
      public var _suitCoat3:Equipment;
      
      public var _suitCoat4:Equipment;
      
      public var _suitCoat5:Equipment;
      
      public var _suitCoat6:Equipment;
      
      public var _suitCoat7:Equipment;
      
      public var _suitCoat8:Equipment;
      
      public var _suitCoat9:Equipment;
      
      public var _special2:Equipment;
      
      public var _special3:Equipment;
      
      public var _special4:Equipment;
      
      public function Equipments()
      {
         super();
         this.init();
      }
      
      private function setProNumber(param1:Number) : Number
      {
         return int(param1) * 0.1 + 0.1;
      }
      
      private function getRandom() : int
      {
         if(GameUtility.getRandomNumber(15))
         {
            if(GameUtility.getRandomNumber(20))
            {
               return 9;
            }
            if(GameUtility.getRandomNumber(50))
            {
               return 8;
            }
            return 7;
         }
         if(GameUtility.getRandomNumber(40))
         {
            if(GameUtility.getRandomNumber(30))
            {
               return 6;
            }
            if(GameUtility.getRandomNumber(35))
            {
               return 5;
            }
            return 4;
         }
         if(GameUtility.getRandomNumber(30))
         {
            return 3;
         }
         if(GameUtility.getRandomNumber(35))
         {
            return 2;
         }
         return 1;
      }
      
      public function init() : void
      {
         this._role1Weapon1 = new Equipment(1,1,"新兵木剑","weapon","LiuBei",16777215,["铁器","名器","宝器"],[this.setProNumber(3 + Math.random() * 6),this.setProNumber(6 + Math.random() * 4),this.setProNumber(10 + Math.random() * 4)],[{},{},{"additionalAttackPower":3}],[4,5,6],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级武器","sword");
         this._role1Weapon2 = new Equipment(2,2,"斩铁剑","weapon","LiuBei",32832,["名器","宝器","仙器"],[this.setProNumber(10 + Math.random() * 4),this.setProNumber(14 + Math.random() * 4),this.setProNumber(18 + Math.random() * 4)],[{},{
            "additionalAttackPower":6,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1
         },{
            "additionalAttackPower":10,
            "additionalHealthPoint":50,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(35),this.setProNumber(40),this.setProNumber(80)],"北域名师亲手打造武器传说可以斩断所有铁器","sword");
         this._role1Weapon3 = new Equipment(3,3,"勇气之刃","weapon","LiuBei",255,["宝器","仙器"],[this.setProNumber(22 + Math.random() * 4),this.setProNumber(26 + Math.random() * 6)],[{
            "additionalAttackPower":14,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1
         },{
            "additionalAttackPower":18,
            "additionalManaPointRegeneration":2,
            "additionalHealthPointRegeneration":1,
            "additionalHealthPoint":70
         }],[6,7],{
            "isNew":true,
            "startLevel":0
         },0,[this.setProNumber(80),this.setProNumber(120)],"传言有勇气之人用此剑方可成为大将","sword");
         this._role1Weapon4 = new Equipment(4,4,"扑风","weapon","LiuBei",160,["宝器","仙器","神器"],[this.setProNumber(32 + Math.random() * 4),this.setProNumber(36 + Math.random() * 8),this.setProNumber(50 + Math.random() * 10)],[{
            "additionalAttackPower":18,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1,
            "additionalHealthPoint":90
         },{
            "additionalAttackPower":28,
            "additionalManaPointRegeneration":2,
            "additionalHealthPointRegeneration":2,
            "additionalHealthPoint":100,
            "additionalCrit":0.02
         },{
            "additionalAttackPower":34,
            "additionalManaPointRegeneration":2,
            "additionalHealthPointRegeneration":2,
            "additionalHealthPoint":140,
            "additionalCrit":0.03
         }],[6,7,8],{"isNew":true},0,[this.setProNumber(160),this.setProNumber(320),this.setProNumber(640)],"传言人间铸剑大师所铸，此剑如有魂方可为神器","sword");
         this._role1Weapon5 = new Equipment(5,5,"承影","weapon","LiuBei",8388736,["仙器","神器"],[this.setProNumber(57 + Math.random() * 8),this.setProNumber(69 + Math.random() * 8)],[{
            "additionalAttackPower":30,
            "additionalManaPointRegeneration":2,
            "additionalHealthPoint":180,
            "additionalCrit":0.04
         },{
            "additionalAttackPower":38,
            "additionalManaPointRegeneration":3,
            "additionalHealthPoint":280,
            "additionalCrit":0.05
         }],[7,8],{"isNew":true},0,[this.setProNumber(800),this.setProNumber(906)],"传说铸剑大师欧冶子承天之命呕心沥血与众神铸磨十载，此剑方成，此剑一出众神归天","sword");
         this._role1Weapon6 = new Equipment(6,6,"彤鱼【巽】","weapon","LiuBei",4194432,["仙器","神器"],[this.setProNumber(77 + Math.random() * 8),this.setProNumber(90 + Math.random() * 10)],[{
            "additionalAttackPower":40,
            "additionalManaPointRegeneration":3,
            "addHealthSkillPoint":100,
            "additionalHealthPoint":300,
            "additionalCrit":0.05
         },{
            "additionalAttackPower":50,
            "additionalManaPointRegeneration":4,
            "addHealthSkillPoint":180,
            "additionalHealthPoint":400,
            "additionalCrit":0.05
         }],[7,8],{"isNew":true},0,[this.setProNumber(1055),this.setProNumber(1166)],"传说炎帝之女彤鱼氏传于人间之物，能斩杀人间邪恶，治疗世间一切病魔","sword");
         this._role1Weapon7 = new Equipment(56,7,"熊猫竹剑","weapon","LiuBei",4194432,["神器"],[this.setProNumber(90 + Math.random() * 5)],[{
            "additionalAttackPower":40,
            "additionalManaPointRegeneration":6,
            "addHealthSkillPoint":220,
            "additionalHealthPoint":600,
            "additionalMiss":0.08
         }],[7],{"isNew":true},0,[this.setProNumber(3000)],"传说观世音菩萨赐予熊猫一族的镇族之宝。此剑和刀合成后能获得未卜先知之能。","sword");
         this._role1Weapon8 = new Equipment(58,8,"拐杖糖果","weapon","LiuBei",4194432,["仙器"],[this.setProNumber(62 + Math.random() * 5)],[{
            "additionalAttackPower":25,
            "additionalManaPointRegeneration":3,
            "addHealthSkillPoint":100,
            "addLuckyPoint":0.05,
            "additionalCrit":0.02
         }],[7],{"isNew":true},0,[this.setProNumber(1000)],"圣诞老人用来惩罚坏孩子使用的武器，但它又是很多人喜欢的糖果。","sword");
         this._role1Weapon9 = new Equipment(93,9,"干将莫邪","weapon","LiuBei",4194432,["仙器","神器"],[this.setProNumber(80 + Math.random() * 5),this.setProNumber(85 + Math.random() * 5)],[{
            "additionalAttackPower":35,
            "additionalManaPointRegeneration":4,
            "additionalHealthPointRegeneration":4,
            "addHealthSkillPoint":150,
            "additionalMiss":0.08
         },{
            "additionalAttackPower":40,
            "additionalManaPointRegeneration":5,
            "additionalHealthPointRegeneration":5,
            "addHealthSkillPoint":260,
            "additionalMiss":0.12
         }],[7,8],{
            "isNew":true,
            "isExclusive":false,
            "txtExclusive":"你的专属神器！",
            "startLevel":this.getRandom()
         },0,[this.setProNumber(2500),this.setProNumber(3500)],"干将莫邪之传说，已于神州浩土以流传数千年。两剑寄铸剑夫妻之魂魄于内，形影相随，不离不弃。","sword");
         this._role1Weapon10 = new Equipment(104,10,"七星龙渊剑","weapon","LiuBei",4194432,["周年版"],[this.setProNumber(80)],[{
            "additionalAttackPower":33,
            "additionalManaPointRegeneration":4,
            "additionalCrit":0.03,
            "addHealthSkillPoint":220,
            "addLuckyPoint":0.1
         }],[7],{"isNew":true},0,[this.setProNumber(1000)],"传说是由欧冶子和干将两大剑师联手所铸，为铸此剑，凿开茨山，放出山中溪水，引至铸剑炉旁成北斗七星环列的七个池中，名为“七星”。","sword");
         this._role1Weapon11 = new Equipment(107,11,"霜锋雪影剑","weapon","LiuBei",4194432,["绝版"],[this.setProNumber(50)],[{
            "additionalAttackPower":28,
            "additionalManaPointRegeneration":3,
            "additionalCrit":0.04,
            "additionalHealthPoint":360
         }],[7],{"isNew":true},0,[this.setProNumber(1000)],"材质名贵的长剑，挥舞之时空中道道白影，如瑞雪飘落，故此得名。","sword");
         this._role1Weapon12 = new Equipment(110,12,"金蛇剑","weapon","LiuBei",4194432,["宝器","仙器","神器"],[this.setProNumber(25),this.setProNumber(60),this.setProNumber(80)],[{
            "additionalAttackPower":12,
            "additionalManaPointRegeneration":1,
            "addHealthSkillPoint":100
         },{
            "additionalAttackPower":25,
            "additionalManaPointRegeneration":2,
            "additionalHealthPointRegeneration":2,
            "addHealthSkillPoint":150
         },{
            "additionalAttackPower":40,
            "additionalManaPointRegeneration":3,
            "additionalHealthPointRegeneration":3,
            "addHealthSkillPoint":200,
            "addLuckyPoint":0.15
         }],[7],{"isNew":true},0,[this.setProNumber(100),this.setProNumber(500),this.setProNumber(1000)],"此剑是五毒教三宝之一，配合金蛇剑法招式神鬼莫测，伤人于无形。","sword");
         var _loc1_:int = 1;
         while(_loc1_ <= 12)
         {
            this._role1Weapons.push(this["_role1Weapon" + _loc1_]);
            _loc1_++;
         }
         this._role1Armor1 = new Equipment(7,1,"新兵粗布帽","armor","LiuBei",16777215,["铁器","名器","宝器"],[this.setProNumber(1),this.setProNumber(2),this.setProNumber(3)],[{},{},{"additionalManaPoint":10}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","helmet");
         this._role1Armor2 = new Equipment(8,1,"新兵粗布衣","armor","LiuBei",16777215,["铁器","名器","宝器"],[this.setProNumber(4),this.setProNumber(6),this.setProNumber(8)],[{},{},{"additionalHealthPoint":30}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","coat");
         this._role1Armor3 = new Equipment(9,1,"新兵粗布裤","armor","LiuBei",16777215,["铁器","名器","宝器"],[this.setProNumber(2),this.setProNumber(3),this.setProNumber(4)],[{},{},{"additionalHealthPoint":15}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","trousers");
         this._role1Armor4 = new Equipment(10,1,"新兵粗布靴","armor","LiuBei",16777215,["铁器","名器","宝器"],[this.setProNumber(1),this.setProNumber(2),this.setProNumber(3)],[{},{},{"additionalHealthPoint":10}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","shoes");
         this._role1Armor5 = new Equipment(11,2,"虎牢之鹿皮帽","armor","LiuBei",32832,["铁器","名器","宝器"],[this.setProNumber(4),this.setProNumber(5),this.setProNumber(6)],[{},{},{
            "additionalManaPoint":30,
            "additionalManaPointRegeneration":1
         }],[3,4,5],{"isNew":true},0,[this.setProNumber(10),this.setProNumber(30),this.setProNumber(35)],"京城著名裁缝师亲手制作，采用天山雪鹿制成防御极好","helmet");
         this._role1Armor6 = new Equipment(12,2,"虎牢之鹿皮衣","armor","LiuBei",32832,["铁器","名器","宝器"],[this.setProNumber(10),this.setProNumber(14),this.setProNumber(18)],[{},{},{
            "additionalHealthPoint":40,
            "additionalHealthPointRegeneration":1
         }],[3,4,5],{"isNew":true},0,[this.setProNumber(10),this.setProNumber(30),this.setProNumber(35)],"京城著名裁缝师亲手制作，采用天山雪鹿制成防御极好","coat");
         this._role1Armor7 = new Equipment(13,2,"虎牢之鹿皮裤","armor","LiuBei",32832,["铁器","名器","宝器"],[this.setProNumber(7),this.setProNumber(9),this.setProNumber(11)],[{},{},{
            "additionalHealthPoint":25,
            "additionalResistance":2
         }],[3,4,5],{"isNew":true},0,[this.setProNumber(10),this.setProNumber(30),this.setProNumber(35)],"京城著名裁缝师亲手制作，采用天山雪鹿制成防御极好","trousers");
         this._role1Armor8 = new Equipment(14,2,"虎牢之鹿皮靴","armor","LiuBei",32832,["铁器","名器","宝器"],[this.setProNumber(4),this.setProNumber(5),this.setProNumber(6)],[{},{},{
            "additionalHealthPoint":20,
            "additionalMoveSpeed":1
         }],[3,4,5],{"isNew":true},0,[this.setProNumber(10),this.setProNumber(30),this.setProNumber(35)],"京城著名裁缝师亲手制作，采用天山雪鹿制成防御极好","shoes");
         this._role1Armor9 = new Equipment(15,3,"飞翔之银鹰盔","armor","LiuBei",255,["名器","宝器","仙器"],[this.setProNumber(7),this.setProNumber(9),this.setProNumber(12)],[{},{
            "additionalManaPoint":40,
            "additionalManaPointRegeneration":2
         },{
            "additionalManaPoint":50,
            "additionalManaPointRegeneration":2
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(35),this.setProNumber(40),this.setProNumber(160)],"强韧的头盔，传说像银鹰一样的速度一击必杀","helmet");
         this._role1Armor10 = new Equipment(16,3,"飞翔之银鹰甲","armor","LiuBei",255,["名器","宝器","仙器"],[this.setProNumber(20),this.setProNumber(24),this.setProNumber(30)],[{},{
            "additionalHealthPoint":80,
            "additionalHealthPointRegeneration":1
         },{
            "additionalHealthPoint":100,
            "additionalHealthPointRegeneration":2,
            "additionalCrit":0.01
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(35),this.setProNumber(40),this.setProNumber(160)],"强韧的铠甲，传说像银鹰一样的速度一击必杀","coat");
         this._role1Armor11 = new Equipment(17,3,"飞翔之银鹰护腿","armor","LiuBei",255,["名器","宝器","仙器"],[this.setProNumber(12),this.setProNumber(14),this.setProNumber(18)],[{},{
            "additionalHealthPoint":50,
            "additionalResistance":6
         },{
            "additionalHealthPoint":70,
            "additionalResistance":10,
            "additionalAttackPower":5
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(35),this.setProNumber(40),this.setProNumber(160)],"强韧的护腿，传说像银鹰一样的速度一击必杀","trousers");
         this._role1Armor12 = new Equipment(18,3,"飞翔之银鹰靴","armor","LiuBei",255,["名器","宝器","仙器"],[this.setProNumber(7),this.setProNumber(9),this.setProNumber(12)],[{},{
            "additionalHealthPoint":40,
            "additionalMoveSpeed":1
         },{
            "additionalHealthPoint":50,
            "additionalMoveSpeed":2
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(35),this.setProNumber(40),this.setProNumber(160)],"强韧的轻靴，传说像银鹰一样的速度一击必杀","shoes");
         this._role1Armor13 = new Equipment(19,4,"风云之白炎盔","armor","LiuBei",8388736,["仙器","神器"],[this.setProNumber(18),this.setProNumber(24)],[{
            "additionalManaPoint":100,
            "additionalManaPointRegeneration":2
         },{
            "additionalManaPoint":150,
            "additionalManaPointRegeneration":3
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"此头盔相传乃上古隐仙广成子取五火之精铸造而成。此护具威力无边，刘备专属","helmet");
         this._role1Armor14 = new Equipment(20,4,"风云之白炎甲","armor","LiuBei",8388736,["仙器","神器"],[this.setProNumber(45),this.setProNumber(55)],[{
            "additionalHealthPoint":150,
            "additionalHealthPointRegeneration":2
         },{
            "additionalHealthPoint":300,
            "additionalHealthPointRegeneration":3,
            "additionalCrit":0.02
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"此铠甲相传乃上古隐仙广成子取五火之精铸造而成。此护具威力无边，刘备专属","coat");
         this._role1Armor15 = new Equipment(21,4,"风云之白炎护腿","armor","LiuBei",8388736,["仙器","神器"],[this.setProNumber(26),this.setProNumber(34)],[{
            "additionalHealthPoint":120,
            "additionalHealthPointRegeneration":3
         },{
            "additionalHealthPoint":180,
            "additionalHealthPointRegeneration":3,
            "additionalResistance":20,
            "additionalMiss":0.03
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"此护腿相传乃上古隐仙广成子取五火之精铸造而成。此护具威力无边，刘备专属","trousers");
         this._role1Armor16 = new Equipment(22,4,"风云之白炎轻靴","armor","LiuBei",8388736,["仙器","神器"],[this.setProNumber(18),this.setProNumber(24)],[{
            "additionalHealthPoint":100,
            "additionalMoveSpeed":2
         },{
            "additionalHealthPoint":150,
            "additionalMoveSpeed":3
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"此轻靴相传乃上古隐仙广成子取五火之精铸造而成。此护具威力无边，刘备专属","shoes");
         this._role1Armor17 = new Equipment(23,5,"传说之崩龙头盔","armor","LiuBei",4194432,["仙器","神器"],[this.setProNumber(27),this.setProNumber(32)],[{
            "additionalManaPoint":150,
            "additionalManaPointRegeneration":3
         },{
            "additionalManaPoint":200,
            "additionalManaPointRegeneration":3
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"此头盔相传乃上古隐仙广成子取五火之精铸造而成。此护具威力无边，刘备专属","helmet");
         this._role1Armor18 = new Equipment(24,5,"传说之崩龙铠甲","armor","LiuBei",4194432,["仙器","神器"],[this.setProNumber(60),this.setProNumber(68)],[{
            "additionalHealthPoint":200,
            "additionalHealthPointRegeneration":3
         },{
            "additionalHealthPoint":300,
            "additionalHealthPointRegeneration":4,
            "additionalCrit":0.03
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"此铠甲和相传乃上古隐仙广成子取五火之精铸造而成。此护具威力无边，刘备专属","coat");
         this._role1Armor19 = new Equipment(25,5,"传说之崩龙护腿","armor","LiuBei",4194432,["仙器","神器"],[this.setProNumber(37),this.setProNumber(41)],[{
            "additionalHealthPoint":180,
            "additionalHealthPointRegeneration":3
         },{
            "additionalHealthPoint":200,
            "additionalHealthPointRegeneration":3,
            "additionalResistance":25,
            "additionalMiss":0.05
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"此护腿相传乃上古隐仙广成子取五火之精铸造而成。此护具威力无边，刘备专属","trousers");
         this._role1Armor20 = new Equipment(26,5,"传说之崩龙轻靴","armor","LiuBei",4194432,["仙器","神器"],[this.setProNumber(27),this.setProNumber(32)],[{
            "additionalManaPoint":150,
            "additionalMoveSpeed":3
         },{
            "additionalManaPoint":180,
            "additionalMoveSpeed":4
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"此轻靴相传乃上古隐仙广成子取五火之精铸造而成。此护具威力无边，刘备专属","shoes");
         _loc1_ = 1;
         while(_loc1_ <= 20)
         {
            this._role1Armors.push(this["_role1Armor" + _loc1_]);
            _loc1_++;
         }
         this._role2Weapon1 = new Equipment(27,1,"新兵关木刀","weapon","GuanYu",16777215,["铁器","名器","宝器"],[this.setProNumber(2 + Math.random() * 6),this.setProNumber(8 + Math.random() * 5),this.setProNumber(13 + Math.random() * 4)],[{},{},{"additionalAttackPower":5}],[4,5,6],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级武器","sword");
         this._role2Weapon2 = new Equipment(28,2,"三环大刀","weapon","GuanYu",32832,["名器","宝器","仙器"],[this.setProNumber(13 + Math.random() * 4),this.setProNumber(17 + Math.random() * 5),this.setProNumber(22 + Math.random() * 4)],[{},{
            "additionalAttackPower":10,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1
         },{
            "additionalAttackPower":14,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1,
            "additionalHealthPoint":50
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(40),this.setProNumber(46),this.setProNumber(92)],"此刀削铁如泥，逢硬必挫。在这兵荒马乱、天下不平之年，定能为你赢得一方地位","sword");
         this._role2Weapon3 = new Equipment(29,3,"战国大刀","weapon","GuanYu",255,["宝器","仙器"],[this.setProNumber(26 + Math.random() * 6),this.setProNumber(32 + Math.random() * 6)],[{
            "additionalAttackPower":18,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1
         },{
            "additionalAttackPower":23,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1,
            "additionalHealthPoint":50,
            "additionalCrit":0.01
         }],[6,7],{
            "isNew":true,
            "startLevel":0
         },0,[this.setProNumber(105),this.setProNumber(125)],"此刀为战国时期，齐国一位英雄用自己英魂所铸，当时此刀为齐国的护国神器，威力无穷。","sword");
         this._role2Weapon4 = new Equipment(30,4,"云月刀","weapon","GuanYu",160,["宝器","仙器","神器"],[this.setProNumber(38 + Math.random() * 8),this.setProNumber(46 + Math.random() * 9),this.setProNumber(60 + Math.random() * 10)],[{
            "additionalAttackPower":26,
            "additionalHealthPointRegeneration":2,
            "additionalManaPointRegeneration":2,
            "additionalHealthPoint":90
         },{
            "additionalAttackPower":30,
            "additionalHealthPointRegeneration":2,
            "additionalManaPointRegeneration":2,
            "additionalHealthPoint":100,
            "additionalCrit":0.02
         },{
            "additionalAttackPower":33,
            "additionalHealthPointRegeneration":3,
            "additionalManaPointRegeneration":3,
            "additionalHealthPoint":160,
            "additionalCrit":0.03
         }],[6,7,8],{"isNew":true},0,[this.setProNumber(180),this.setProNumber(360),this.setProNumber(750)],"此刀相传为天神云月星君所使用的刀，在神魔大战中所向披靡，但不知道为何流落人间","sword");
         this._role2Weapon5 = new Equipment(31,5,"编驹","weapon","GuanYu",8388736,["仙器","神器"],[this.setProNumber(66 + Math.random() * 9),this.setProNumber(78 + Math.random() * 8)],[{
            "additionalAttackPower":33,
            "additionalManaPointRegeneration":2,
            "additionalHealthPoint":160,
            "additionalCrit":0.03
         },{
            "additionalAttackPower":40,
            "additionalHealthPointRegeneration":4,
            "additionalHealthPoint":240,
            "additionalCrit":0.04
         }],[7,8],{"isNew":true},0,[this.setProNumber(900),this.setProNumber(1800)],"传闻此刀封神时期日月星君弟子为斩出妖魔所化，此刀一出妖魔退让","sword");
         this._role2Weapon6 = new Equipment(32,6,"青龙偃月刀","weapon","GuanYu",4194432,["仙器","神器"],[this.setProNumber(90 + Math.random() * 6),this.setProNumber(100 + Math.random() * 10)],[{
            "additionalAttackPower":45,
            "additionalManaPointRegeneration":3,
            "additionalHealthPointRegeneration":3,
            "additionalHealthPoint":250,
            "additionalCrit":0.06
         },{
            "additionalAttackPower":60,
            "additionalManaPointRegeneration":4,
            "additionalHealthPointRegeneration":4,
            "additionalHealthPoint":350,
            "additionalCrit":0.1
         }],[7,8],{"isNew":true},0,[this.setProNumber(2000),this.setProNumber(4000)],"相传此刀是神兽青龙用自己的精血所铸，在此经过无数的岁月孕育出自己灵魂它能选择自己的主人而成为纵横乱世的一代神刀","sword");
         this._role2Weapon7 = new Equipment(57,7,"熊猫竹刀","weapon","GuanYu",4194432,["神器"],[this.setProNumber(100 + Math.random() * 5)],[{
            "additionalAttackPower":50,
            "additionalManaPointRegeneration":5,
            "additionalHealthPointRegeneration":5,
            "additionalHealthPoint":300,
            "additionalCrit":0.12
         }],[7],{"isNew":true},0,[this.setProNumber(3000)],"传说观世音菩萨赐予熊猫一族的镇族之宝。此刀和剑合成后能获得未卜先知之能。","sword");
         this._role2Weapon8 = new Equipment(59,8,"棒棒糖","weapon","GuanYu",4194432,["仙器"],[this.setProNumber(72 + Math.random() * 5)],[{
            "additionalAttackPower":28,
            "additionalManaPointRegeneration":2,
            "additionalHealthPointRegeneration":2,
            "addLuckyPoint":0.05,
            "additionalCrit":0.04
         }],[7],{"isNew":true},0,[this.setProNumber(1000)],"圣诞老人用来惩罚坏孩子使用的武器，但它又是很多人喜欢的糖果。","sword");
         this._role2Weapon9 = new Equipment(94,9,"断龙绝麟","weapon","GuanYu",4194432,["仙器","神器"],[this.setProNumber(90 + Math.random() * 5),this.setProNumber(95 + Math.random() * 5)],[{
            "additionalAttackPower":45,
            "additionalManaPointRegeneration":4,
            "additionalHealthPointRegeneration":4,
            "additionalMiss":0.08,
            "additionalCrit":0.1
         },{
            "additionalAttackPower":50,
            "additionalManaPointRegeneration":5,
            "additionalHealthPointRegeneration":5,
            "additionalMiss":0.1,
            "additionalCrit":0.15
         }],[7,8],{
            "isNew":true,
            "isExclusive":false,
            "txtExclusive":"你的专属神器！",
            "startLevel":this.getRandom()
         },0,[this.setProNumber(2500),this.setProNumber(3500)],"传说中有一只被同族追逐的巨龙，在极寒之地化为此刀，唤为断龙绝鳞。寄宿着强大龙魂和骇人的怨恨。","sword");
         this._role2Weapon10 = new Equipment(105,10,"血饮天下","weapon","GuanYu",4194432,["周年版"],[this.setProNumber(90)],[{
            "additionalAttackPower":38,
            "additionalManaPointRegeneration":3,
            "additionalHealthPointRegeneration":3,
            "additionalCrit":0.04,
            "addLuckyPoint":0.1
         }],[7],{"isNew":true},0,[this.setProNumber(1000)],"传说这把刀在五帝之首的黄帝身上留下一道伤痕，从此名扬天下。","sword");
         this._role2Weapon11 = new Equipment(108,11,"烈焰关刀","weapon","GuanYu",4194432,["绝版"],[this.setProNumber(60)],[{
            "additionalAttackPower":30,
            "additionalManaPointRegeneration":4,
            "additionalHealthPoint":400,
            "additionalCrit":0.05
         }],[7],{"isNew":true},0,[this.setProNumber(1000)],"通体金红，犹如烈焰，气势逼人，攻城掠地不在话下。","sword");
         this._role2Weapon12 = new Equipment(111,12,"龙蛇刀","weapon","GuanYu",4194432,["宝器","仙器","神器"],[this.setProNumber(30),this.setProNumber(66),this.setProNumber(90)],[{
            "additionalAttackPower":16,
            "additionalManaPointRegeneration":1,
            "additionalCrit":0.02
         },{
            "additionalAttackPower":31,
            "additionalManaPointRegeneration":2,
            "additionalHealthPointRegeneration":2,
            "additionalCrit":0.03
         },{
            "additionalAttackPower":46,
            "additionalManaPointRegeneration":3,
            "additionalHealthPointRegeneration":3,
            "additionalCrit":0.03,
            "addLuckyPoint":0.15
         }],[7],{"isNew":true},0,[this.setProNumber(100),this.setProNumber(500),this.setProNumber(1000)],"龙蛇岛震派之宝，传说龙蛇大战时受仙人封印此刀中，让此刀有着龙的力量，蛇的狠毒。","sword");
         _loc1_ = 1;
         while(_loc1_ <= 12)
         {
            this._role2Weapons.push(this["_role2Weapon" + _loc1_]);
            _loc1_++;
         }
         this._role2Armor1 = new Equipment(33,1,"新兵麻布帽","armor","GuanYu",16777215,["铁器","名器","宝器"],[this.setProNumber(1),this.setProNumber(2),this.setProNumber(3)],[{},{},{"additionalManaPoint":10}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","helmet");
         this._role2Armor2 = new Equipment(34,1,"新兵麻布衣","armor","GuanYu",16777215,["铁器","名器","宝器"],[this.setProNumber(4),this.setProNumber(6),this.setProNumber(8)],[{},{},{"additionalHealthPoint":30}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","coat");
         this._role2Armor3 = new Equipment(35,1,"新兵麻布裤","armor","GuanYu",16777215,["铁器","名器","宝器"],[this.setProNumber(2),this.setProNumber(3),this.setProNumber(4)],[{},{},{"additionahealthlPoint":15}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","trousers");
         this._role2Armor4 = new Equipment(36,1,"新兵麻布靴","armor","GuanYu",16777215,["铁器","名器","宝器"],[this.setProNumber(1),this.setProNumber(2),this.setProNumber(3)],[{},{},{"additionalHealthPoint":10}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","shoes");
         this._role2Armor5 = new Equipment(37,2,"意志之青铜头盔","armor","GuanYu",32832,["铁器","名器","宝器"],[this.setProNumber(4),this.setProNumber(6),this.setProNumber(8)],[{},{},{
            "additionalManaPoint":20,
            "additionalManaPointRegeneration":1
         }],[4,5,6],{"isNew":true},0,[this.setProNumber(10),this.setProNumber(25),this.setProNumber(35)],"天下第一名匠专为高级勇士打造，防御性极好","helmet");
         this._role2Armor6 = new Equipment(38,2,"意志之青铜甲","armor","GuanYu",32832,["铁器","名器","宝器"],[this.setProNumber(10),this.setProNumber(13),this.setProNumber(20)],[{},{},{
            "additionalHealthPoint":40,
            "additionalHealthPointRegeneration":1
         }],[4,5,6],{"isNew":true},0,[this.setProNumber(10),this.setProNumber(25),this.setProNumber(35)],"天下第一名匠专为高级勇士打造，防御性极好","coat");
         this._role2Armor7 = new Equipment(39,2,"意志之青铜护腿","armor","GuanYu",32832,["铁器","名器","宝器"],[this.setProNumber(7),this.setProNumber(9),this.setProNumber(11)],[{},{},{
            "additionalHealthPoint":25,
            "additionalResistance":2
         }],[4,5,6],{"isNew":true},0,[this.setProNumber(10),this.setProNumber(25),this.setProNumber(35)],"天下第一名匠专为高级勇士打造，防御性极好","trousers");
         this._role2Armor8 = new Equipment(40,2,"意志之青铜靴","armor","GuanYu",32832,["铁器","名器","宝器"],[this.setProNumber(4),this.setProNumber(6),this.setProNumber(8)],[{},{},{
            "additionalHealthPoint":20,
            "additionalMoveSpeed":1
         }],[4,5,6],{"isNew":true},0,[this.setProNumber(10),this.setProNumber(25),this.setProNumber(35)],"天下第一名匠专为高级勇士打造，防御性极好","shoes");
         this._role2Armor9 = new Equipment(41,3,"火暴之猛士头盔","armor","GuanYu",255,["名器","宝器","仙器"],[this.setProNumber(7),this.setProNumber(9),this.setProNumber(12)],[{},{
            "additionalManaPoint":40,
            "additionalManaPointRegeneration":2
         },{
            "additionalManaPoint":50,
            "additionalManaPointRegeneration":3
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(30),this.setProNumber(45),this.setProNumber(50)],"相传此件防具是西域第一猛将所使用的防具，穿上它能感觉到力量提升。","helmet");
         this._role2Armor10 = new Equipment(42,3,"火暴之猛士铠甲","armor","GuanYu",255,["名器","宝器","仙器"],[this.setProNumber(18),this.setProNumber(28),this.setProNumber(37)],[{},{
            "additionalHealthPoint":70,
            "additionalHealthPointRegeneration":2
         },{
            "additionalHealthPoint":90,
            "additionalHealthPointRegeneration":3,
            "additionalCrit":0.02
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(30),this.setProNumber(45),this.setProNumber(50)],"相传此件防具是西域第一猛将所使用的防具，穿上它能感觉到力量提升。","coat");
         this._role2Armor11 = new Equipment(43,3,"火暴之猛士护腿","armor","GuanYu",255,["名器","宝器","仙器"],[this.setProNumber(10),this.setProNumber(15),this.setProNumber(20)],[{},{
            "additionalHealthPoint":40,
            "additionalResistance":6
         },{
            "additionalHealthPoint":60,
            "additionalResistance":8,
            "additionalAttackPower":5
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(30),this.setProNumber(45),this.setProNumber(50)],"相传此件防具是西域第一猛将所使用的防具，穿上它能感觉到力量提升。","trousers");
         this._role2Armor12 = new Equipment(44,3,"火暴之猛士靴","armor","GuanYu",255,["名器","宝器","仙器"],[this.setProNumber(7),this.setProNumber(9),this.setProNumber(12)],[{},{
            "additionalHealthPoint":30,
            "additionalMoveSpeed":1
         },{
            "additionalHealthPoint":40,
            "additionalMoveSpeed":2
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(30),this.setProNumber(45),this.setProNumber(50)],"相传此件防具是西域第一猛将所使用的防具，穿上它能感觉到力量提升.","shoes");
         this._role2Armor13 = new Equipment(45,4,"火焰之红纹头盔","armor","GuanYu",8388736,["仙器","神器"],[this.setProNumber(22),this.setProNumber(30)],[{
            "additionalManaPoint":100,
            "additionalManaPointRegeneration":3
         },{
            "additionalManaPoint":150,
            "additionalManaPointRegeneration":4
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"传闻此防具是一通天神人从通天之塔带下人间，如果能集合全套就能爬上通天之塔到达天庭立刻成仙。","helmet");
         this._role2Armor14 = new Equipment(46,4,"火焰之红纹铠甲","armor","GuanYu",8388736,["仙器","神器"],[this.setProNumber(55),this.setProNumber(66)],[{
            "additionalHealthPoint":110,
            "additionalHealthPointRegeneration":3
         },{
            "additionalHealthPoint":220,
            "additionalHealthPointRegeneration":4,
            "additionalCrit":0.03
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"传闻此防具是一通天神人从通天之塔带下人间，如果能集合全套就能爬上通天之塔到达天庭立刻成仙。","coat");
         this._role2Armor15 = new Equipment(47,4,"火焰之红纹护腿","armor","GuanYu",8388736,["仙器","神器"],[this.setProNumber(32),this.setProNumber(40)],[{
            "additionalHealthPoint":120,
            "additionalHealthPointRegeneration":4
         },{
            "additionalHealthPoint":140,
            "additionalHealthPointRegeneration":4,
            "additionalResistance":15,
            "additionalMiss":0.04
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"传闻此防具是一通天神人从通天之塔带下人间，如果能集合全套就能爬上通天之塔到达天庭立刻成仙。","trousers");
         this._role2Armor16 = new Equipment(48,4,"火焰之红纹靴","armor","GuanYu",8388736,["仙器","神器"],[this.setProNumber(22),this.setProNumber(30)],[{
            "additionalHealthPoint":100,
            "additionalMoveSpeed":2
         },{
            "additionalHealthPoint":120,
            "additionalMoveSpeed":3
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"传闻此防具是一通天神人从通天之塔带下人间，如果能集合全套就能爬上通天之塔到达天庭立刻成仙。","shoes");
         this._role2Armor17 = new Equipment(49,5,"烈火之凤凰头盔","armor","GuanYu",4194432,["仙器","神器"],[this.setProNumber(32),this.setProNumber(35)],[{
            "additionalManaPoint":150,
            "additionalManaPointRegeneration":4
         },{
            "additionalManaPoint":220,
            "additionalManaPointRegeneration":4
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"传承自凤凰神兽所化之头盔，穿上全套能让人感觉到凤凰重生的力量.","helmet");
         this._role2Armor18 = new Equipment(50,5,"烈火之凤凰铠甲","armor","GuanYu",4194432,["仙器","神器"],[this.setProNumber(70),this.setProNumber(75)],[{
            "additionalHealthPoint":200,
            "additionalHealthPointRegeneration":4
         },{
            "additionalHealthPoint":280,
            "additionalHealthPointRegeneration":4,
            "additionalCrit":0.03
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"传承自凤凰神兽所化之铠甲，穿上全套能让人感觉到凤凰重生的力量.","coat");
         this._role2Armor19 = new Equipment(51,5,"烈火之凤凰护腿","armor","GuanYu",4194432,["仙器","神器"],[this.setProNumber(43),this.setProNumber(46)],[{
            "additionalHealthPoint":140,
            "additionalHealthPointRegeneration":4
         },{
            "additionalHealthPoint":180,
            "additionalHealthPointRegeneration":4,
            "additionalResistance":15,
            "additionalMiss":0.05
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"传承自凤凰神兽所化之护腿，穿上全套能让人感觉到凤凰重生的力量.","trousers");
         this._role2Armor20 = new Equipment(52,5,"烈火之凤凰靴","armor","GuanYu",4194432,["仙器","神器"],[this.setProNumber(32),this.setProNumber(35)],[{
            "additionalManaPoint":150,
            "additionalMoveSpeed":3
         },{
            "additionalManaPoint":180,
            "additionalMoveSpeed":4
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"传承自凤凰神兽所化之神靴，穿上全套能让人感觉到凤凰重生的力量.","shoes");
         _loc1_ = 1;
         while(_loc1_ <= 20)
         {
            this._role2Armors.push(this["_role2Armor" + _loc1_]);
            _loc1_++;
         }
         this._role3Weapon1 = new Equipment(62,1,"新兵木矛","weapon","ZhangFei",16777215,["铁器","名器","宝器"],[this.setProNumber(3 + Math.random() * 4),this.setProNumber(7 + Math.random() * 4),this.setProNumber(11 + Math.random() * 5)],[{},{},{"additionalAttackPower":4}],[4,5,6],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级武器","sword");
         this._role3Weapon2 = new Equipment(63,2,"钢铁长矛","weapon","ZhangFei",32832,["名器","宝器","仙器"],[this.setProNumber(9 + Math.random() * 6),this.setProNumber(15 + Math.random() * 5),this.setProNumber(20 + Math.random() * 5)],[{},{
            "additionalAttackPower":8,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1
         },{
            "additionalAttackPower":12,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1,
            "additionalHealthPoint":70
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(40),this.setProNumber(46),this.setProNumber(92)],"北域名师打造重达350公斤，天生神力之人方可拿起。","sword");
         this._role3Weapon3 = new Equipment(64,3,"破军蛇矛","weapon","ZhangFei",255,["宝器","仙器"],[this.setProNumber(25 + Math.random() * 5),this.setProNumber(30 + Math.random() * 5)],[{
            "additionalAttackPower":15,
            "additionalManaPointRegeneration":1
         },{
            "additionalAttackPower":20,
            "additionalManaPointRegeneration":1,
            "additionalHealthPoint":80,
            "additionalMiss":0.01
         }],[6,7],{
            "isNew":true,
            "startLevel":0
         },0,[this.setProNumber(105),this.setProNumber(125)],"强劲的长矛，传说是由妖蛇变化而成。","sword");
         this._role3Weapon4 = new Equipment(65,4,"月灵长矛","weapon","ZhangFei",160,["宝器","仙器","神器"],[this.setProNumber(33 + Math.random() * 9),this.setProNumber(42 + Math.random() * 8),this.setProNumber(55 + Math.random() * 10)],[{
            "additionalAttackPower":23,
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1,
            "additionalHealthPoint":90
         },{
            "additionalAttackPower":26,
            "additionalHealthPointRegeneration":2,
            "additionalManaPointRegeneration":2,
            "additionalHealthPoint":130,
            "additionalMiss":0.02
         },{
            "additionalAttackPower":29,
            "additionalHealthPointRegeneration":3,
            "additionalManaPointRegeneration":3,
            "additionalHealthPoint":170,
            "additionalMiss":0.03
         }],[6,7,8],{"isNew":true},0,[this.setProNumber(180),this.setProNumber(360),this.setProNumber(750)],"此矛相传为月神所使用的刀，威力无穷，可斩日月。","sword");
         this._role3Weapon5 = new Equipment(66,5,"淬血长矛","weapon","ZhangFei",8388736,["仙器","神器"],[this.setProNumber(60 + Math.random() * 10),this.setProNumber(70 + Math.random() * 8)],[{
            "additionalAttackPower":30,
            "additionalManaPointRegeneration":2,
            "additionalHealthPointRegeneration":2,
            "additionalHealthPoint":200,
            "additionalMiss":0.02
         },{
            "additionalAttackPower":35,
            "additionalHealthPointRegeneration":3,
            "additionalHealthPointRegeneration":3,
            "additionalHealthPoint":280,
            "additionalMiss":0.04
         }],[7,8],{"isNew":true},0,[this.setProNumber(900),this.setProNumber(1800)],"杀戮之矛，噬血天下。","sword");
         this._role3Weapon6 = new Equipment(67,6,"丈八蛇矛","weapon","ZhangFei",4194432,["仙器","神器"],[this.setProNumber(80 + Math.random() * 8),this.setProNumber(95 + Math.random() * 8)],[{
            "additionalAttackPower":42,
            "additionalManaPointRegeneration":3,
            "additionalHealthPoint":250,
            "additionalMiss":0.04,
            "bloodThirsty":0.05
         },{
            "additionalAttackPower":55,
            "additionalManaPointRegeneration":4,
            "additionalHealthPoint":350,
            "additionalMiss":0.07,
            "bloodThirsty":0.08
         }],[7,8],{"isNew":true},0,[this.setProNumber(2000),this.setProNumber(4000)],"又名丈八点钢矛。全用镔铁占钢打造矛杆长一丈，矛尖长八寸，刃开双锋，作游蛇形状，故而名之。","sword");
         this._role3Weapon7 = new Equipment(68,7,"神威离火","weapon","ZhangFei",4194432,["仙器","神器"],[this.setProNumber(83 + Math.random() * 5),this.setProNumber(93 + Math.random() * 5)],[{
            "additionalAttackPower":37,
            "additionalManaPointRegeneration":3,
            "additionalHealthPoint":350,
            "additionalMiss":0.06,
            "bloodThirsty":0.1
         },{
            "additionalAttackPower":42,
            "additionalManaPointRegeneration":4,
            "additionalHealthPoint":400,
            "additionalMiss":0.09,
            "bloodThirsty":0.15
         }],[7,8],{
            "isNew":true,
            "isExclusive":false,
            "txtExclusive":"你的专属神器！",
            "startLevel":this.getRandom()
         },0,[this.setProNumber(2500),this.setProNumber(3500)],"矛身铭刻着中原浩土无数次的毁灭的记录。似乎每一次的毁灭，都与这把长矛有关。","sword");
         this._role3Weapon8 = new Equipment(69,8,"棒棒糖","weapon","ZhangFei",4194432,["仙器"],[this.setProNumber(72 + Math.random() * 5)],[{
            "additionalAttackPower":28,
            "additionalManaPointRegeneration":2,
            "additionalHealthPointRegeneration":2,
            "addLuckyPoint":0.05,
            "additionalCrit":0.04
         }],[7],{"isNew":true},0,[this.setProNumber(1000)],"圣诞老人用来惩罚坏孩子使用的武器，但它又是很多人喜欢的糖果。","sword");
         this._role3Weapon9 = new Equipment(106,8,"月影龙牙","weapon","ZhangFei",4194432,["周年版"],[this.setProNumber(80)],[{
            "additionalAttackPower":40,
            "additionalHealthPoint":300,
            "additionalHealthPointRegeneration":3,
            "additionalMiss":0.04,
            "addLuckyPoint":0.1
         }],[7],{"isNew":true},0,[this.setProNumber(1000)],"记述着一条仙龙经历万年的修炼，最终达到道法大成，仙化为龙的长矛。身刻有复杂难解的纹样，象征着龙无所不能","sword");
         this._role3Weapon10 = new Equipment(109,9,"夜叉矛","weapon","ZhangFei",4194432,["绝版"],[this.setProNumber(55)],[{
            "additionalAttackPower":30,
            "additionalManaPointRegeneration":4,
            "additionalHealthPointRegeneration":4,
            "additionalMiss":0.05
         }],[7],{"isNew":true},0,[this.setProNumber(1000)],"枪身雕有夜叉王像，阴森恐怖，枪头扁平分叉，刺穿敌人后可放血，是杀人利器。","sword");
         this._role3Weapon11 = new Equipment(112,10,"灵蛇矛","weapon","ZhangFei",4194432,["宝器","仙器","神器"],[this.setProNumber(28),this.setProNumber(63),this.setProNumber(85)],[{
            "additionalAttackPower":14,
            "additionalManaPointRegeneration":1,
            "additionalMiss":0.02
         },{
            "additionalAttackPower":28,
            "additionalManaPointRegeneration":2,
            "additionalHealthPointRegeneration":2,
            "additionalMiss":0.03
         },{
            "additionalAttackPower":43,
            "additionalManaPointRegeneration":3,
            "additionalHealthPointRegeneration":3,
            "additionalMiss":0.03,
            "addLuckyPoint":0.15
         }],[7],{"isNew":true},0,[this.setProNumber(100),this.setProNumber(500),this.setProNumber(1000)],"这杆矛传说是一条化龙的大蛇渡劫失败后所化，又似乎与汉高祖刘邦挥剑斩蛇的传闻有关。","sword");
         _loc1_ = 1;
         while(_loc1_ <= 11)
         {
            this._role3Weapons.push(this["_role3Weapon" + _loc1_]);
            _loc1_++;
         }
         this._role3Armor1 = new Equipment(70,1,"新兵棉布帽","armor","ZhangFei",16777215,["铁器","名器","宝器"],[this.setProNumber(1),this.setProNumber(2),this.setProNumber(3)],[{},{},{"additionalManaPoint":5}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","helmet");
         this._role3Armor2 = new Equipment(71,1,"新兵棉布衣","armor","ZhangFei",16777215,["铁器","名器","宝器"],[this.setProNumber(4),this.setProNumber(6),this.setProNumber(8)],[{},{},{"additionalHealthPoint":20}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","coat");
         this._role3Armor3 = new Equipment(72,1,"新兵棉布裤","armor","ZhangFei",16777215,["铁器","名器","宝器"],[this.setProNumber(2),this.setProNumber(3),this.setProNumber(4)],[{},{},{"additionahealthlPoint":10}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","trousers");
         this._role3Armor4 = new Equipment(73,1,"新兵棉布靴","armor","ZhangFei",16777215,["铁器","名器","宝器"],[this.setProNumber(1),this.setProNumber(2),this.setProNumber(3)],[{},{},{"additionalHealthPoint":10}],[3,4,5],{"isNew":true},0,[this.setProNumber(5),this.setProNumber(10),this.setProNumber(25)],"名匠打造新手初级防具","shoes");
         this._role3Armor5 = new Equipment(74,2,"怒气之兽皮帽","armor","ZhangFei",32832,["名器","宝器"],[this.setProNumber(5),this.setProNumber(7)],[{},{
            "additionalManaPoint":30,
            "additionalManaPointRegeneration":1
         }],[5,6],{"isNew":true},0,[this.setProNumber(25),this.setProNumber(35)],"京城著名裁缝师亲手制作，采用天山雪鹿制成防御极好。","helmet");
         this._role3Armor6 = new Equipment(75,2,"怒气之兽皮甲","armor","ZhangFei",32832,["名器","宝器"],[this.setProNumber(15),this.setProNumber(19)],[{},{
            "additionalHealthPoint":50,
            "additionalHealthPointRegeneration":1
         }],[5,6],{"isNew":true},0,[this.setProNumber(25),this.setProNumber(35)],"京城著名裁缝师亲手制作，采用天山雪鹿制成防御极好。","coat");
         this._role3Armor7 = new Equipment(76,2,"怒气之兽皮裤","armor","ZhangFei",32832,["名器","宝器"],[this.setProNumber(9),this.setProNumber(10)],[{},{
            "additionalHealthPoint":30,
            "additionalResistance":2
         }],[5,6],{"isNew":true},0,[this.setProNumber(25),this.setProNumber(35)],"京城著名裁缝师亲手制作，采用天山雪鹿制成防御极好。","trousers");
         this._role3Armor8 = new Equipment(77,2,"怒气之兽皮靴","armor","ZhangFei",32832,["名器","宝器"],[this.setProNumber(5),this.setProNumber(7)],[{},{
            "additionalHealthPoint":20,
            "additionalMoveSpeed":1
         }],[5,6],{"isNew":true},0,[this.setProNumber(25),this.setProNumber(35)],"京城著名裁缝师亲手制作，采用天山雪鹿制成防御极好。","shoes");
         this._role3Armor9 = new Equipment(78,3,"勇气之蛮牛头盔","armor","ZhangFei",255,["名器","宝器","仙器"],[this.setProNumber(7),this.setProNumber(9),this.setProNumber(12)],[{},{
            "additionalManaPoint":40,
            "additionalManaPointRegeneration":1
         },{
            "additionalManaPoint":50,
            "additionalManaPointRegeneration":2
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(30),this.setProNumber(45),this.setProNumber(50)],"蛮荒强大的野兽犄角蛮牛皮所制，有着强力的防御和战意。","helmet");
         this._role3Armor10 = new Equipment(79,3,"勇气之蛮牛战袍","armor","ZhangFei",255,["名器","宝器","仙器"],[this.setProNumber(18),this.setProNumber(25),this.setProNumber(35)],[{},{
            "additionalHealthPoint":100,
            "additionalHealthPointRegeneration":1
         },{
            "additionalHealthPoint":100,
            "additionalHealthPointRegeneration":2,
            "additionalCrit":0.01
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(30),this.setProNumber(45),this.setProNumber(50)],"蛮荒强大的野兽犄角蛮牛皮所制，有着强力的防御和战意。","coat");
         this._role3Armor11 = new Equipment(80,3,"勇气之蛮牛护腿","armor","ZhangFei",255,["名器","宝器","仙器"],[this.setProNumber(10),this.setProNumber(14),this.setProNumber(20)],[{},{
            "additionalHealthPoint":60,
            "additionalResistance":5
         },{
            "additionalHealthPoint":70,
            "additionalResistance":8,
            "additionalAttackPower":5
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(30),this.setProNumber(45),this.setProNumber(50)],"蛮荒强大的野兽犄角蛮牛皮所制，有着强力的防御和战意。","trousers");
         this._role3Armor12 = new Equipment(81,3,"勇气之蛮牛战靴","armor","ZhangFei",255,["名器","宝器","仙器"],[this.setProNumber(7),this.setProNumber(9),this.setProNumber(12)],[{},{
            "additionalHealthPoint":40,
            "additionalMoveSpeed":1
         },{
            "additionalHealthPoint":50,
            "additionalMoveSpeed":2
         }],[5,6,7],{"isNew":true},0,[this.setProNumber(30),this.setProNumber(45),this.setProNumber(50)],"蛮荒强大的野兽犄角蛮牛皮所制，有着强力的防御和战意。","shoes");
         this._role3Armor13 = new Equipment(82,4,"勇猛之战魂头盔","armor","ZhangFei",8388736,["仙器","神器"],[this.setProNumber(20),this.setProNumber(27)],[{
            "additionalManaPoint":80,
            "additionalManaPointRegeneration":2
         },{
            "additionalManaPoint":120,
            "additionalManaPointRegeneration":3
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"猛士纵身死，魂留天地间。","helmet");
         this._role3Armor14 = new Equipment(83,4,"勇猛之战魂铠甲","armor","ZhangFei",8388736,["仙器","神器"],[this.setProNumber(48),this.setProNumber(59)],[{
            "additionalHealthPoint":200,
            "additionalHealthPointRegeneration":2
         },{
            "additionalHealthPoint":380,
            "additionalHealthPointRegeneration":2,
            "additionalMiss":0.02
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"猛士纵身死，魂留天地间。","coat");
         this._role3Armor15 = new Equipment(84,4,"勇猛之战魂护腿","armor","ZhangFei",8388736,["仙器","神器"],[this.setProNumber(28),this.setProNumber(37)],[{
            "additionalHealthPoint":150,
            "additionalHealthPointRegeneration":1
         },{
            "additionalHealthPoint":200,
            "additionalHealthPointRegeneration":2,
            "additionalResistance":20
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"猛士纵身死，魂留天地间。","trousers");
         this._role3Armor16 = new Equipment(85,4,"勇猛之战魂战靴","armor","ZhangFei",8388736,["仙器","神器"],[this.setProNumber(20),this.setProNumber(27)],[{
            "additionalHealthPoint":120,
            "additionalMoveSpeed":2
         },{
            "additionalHealthPoint":180,
            "additionalMoveSpeed":3
         }],[7,8],{"isNew":true},0,[this.setProNumber(320),this.setProNumber(400)],"猛士纵身死，魂留天地间。","shoes");
         this._role3Armor17 = new Equipment(86,5,"圣兽之白虎头盔","armor","ZhangFei",4194432,["仙器","神器"],[this.setProNumber(30),this.setProNumber(32)],[{
            "additionalManaPoint":150,
            "additionalManaPointRegeneration":3
         },{
            "additionalManaPoint":180,
            "additionalManaPointRegeneration":3
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"四象中位居西方圣兽，上古四大神兽之一。","helmet");
         this._role3Armor18 = new Equipment(87,5,"圣兽之白虎战甲","armor","ZhangFei",4194432,["仙器","神器"],[this.setProNumber(63),this.setProNumber(68)],[{
            "additionalHealthPoint":440,
            "additionalHealthPointRegeneration":2
         },{
            "additionalHealthPoint":500,
            "additionalHealthPointRegeneration":3,
            "additionalCrit":0.02
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"四象中位居西方圣兽，上古四大神兽之一。","coat");
         this._role3Armor19 = new Equipment(88,5,"圣兽之白虎护腿","armor","ZhangFei",4194432,["仙器","神器"],[this.setProNumber(40),this.setProNumber(42)],[{
            "additionalHealthPoint":220,
            "additionalHealthPointRegeneration":2
         },{
            "additionalHealthPoint":250,
            "additionalHealthPointRegeneration":2,
            "additionalResistance":18,
            "additionalMiss":0.06
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"四象中位居西方圣兽，上古四大神兽之一。","trousers");
         this._role3Armor20 = new Equipment(89,5,"圣兽之白虎战靴","armor","ZhangFei",4194432,["仙器","神器"],[this.setProNumber(30),this.setProNumber(32)],[{
            "additionalManaPoint":200,
            "additionalMoveSpeed":3
         },{
            "additionalManaPoint":230,
            "additionalMoveSpeed":4
         }],[7,8],{
            "isNew":true,
            "startLevel":this.getRandom()
         },0,[this.setProNumber(420),this.setProNumber(500)],"四象中位居西方圣兽，上古四大神兽之一。","shoes");
         _loc1_ = 1;
         while(_loc1_ <= 20)
         {
            this._role3Armors.push(this["_role3Armor" + _loc1_]);
            _loc1_++;
         }
         this._decoration1 = new Equipment(53,1,"白虎戒指","ring","",16744448,[],[],[{
            "additionalAttackPower":23,
            "additionalHealthPoint":120
         }],[],{"isNew":true},0,[this.setProNumber(300)],"传说四大神兽之一白虎精血所化，此戒指具有水之灵力，净化一切的力量，白虎套件之一","ring");
         this._decoration2 = new Equipment(54,1,"白虎项链","necklace","",16744448,[],[],[{
            "additionalHealthPointRegeneration":1,
            "additionalManaPointRegeneration":1,
            "additionalCrit":0.02,
            "additionalMiss":0.02
         }],[],{"isNew":true},0,[this.setProNumber(300)],"传说四大神兽之一白虎精血所化，此项链具有水之灵力，净化一切的力量，白虎套件之一","necklace");
         this._decoration3 = new Equipment(55,1,"白虎护符","amulet","",16744448,[],[],[{
            "additionalResistance":15,
            "additionalManaPoint":130
         }],[],{"isNew":true},0,[this.setProNumber(300)],"传说四大神兽之一白虎精血所化，此护符具有水之灵力，净化一切的力量，白虎套件之一","amulet");
         this._decoration4 = new Equipment(100,1,"玄武戒指","ring","",16744448,[],[],[{
            "additionalHealthPointRegeneration":2,
            "additionalManaPointRegeneration":2
         }],[],{"isNew":true},0,[this.setProNumber(300)],"传说四大神兽玄武有着操控大地之力，神魔大战之后玄武用精血所化戒指，拥有着无坚不摧的防御。","ring");
         this._decoration5 = new Equipment(101,1,"玄武项链","necklace","",16744448,[],[],[{
            "additionalResistance":50,
            "additionalMiss":0.07
         }],[],{"isNew":true},0,[this.setProNumber(300)],"传说四大神兽玄武有着操控大地之力，神魔大战之后玄武用龟甲所化项链，拥有着无坚不摧的防御。","necklace");
         this._decoration6 = new Equipment(102,1,"玄武护符","amulet","",16744448,[],[],[{
            "additionalManaPoint":500,
            "additionalHealthPoint":500
         }],[],{"isNew":true},0,[this.setProNumber(300)],"传说四大神兽玄武有着操控大地之力，神魔大战之后玄武用龟甲所化护符，拥有着无坚不摧的防御。","amulet");
         this._suitCoat1 = new Equipment(60,6,"圣诞套装","suit","",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"圣诞节在每个孩子的心中是最美好的节日，在圣诞节时穿上这套服装会给你带来好运气。","suit");
         this._special1 = new Equipment(61,6,"圣灵果","special","Pet",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"圣灵果乃是圣灵树用一千年一开花，二千年一结果．再三千年才得以成熟，灵兽吃一个可增加千年修为。","suit");
         this._suitCoat2 = new Equipment(90,7,"财源滚滚 ","suit","LiuBei",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"唐代的汉服，沿袭了自东汉以来华夏传统的上衣下裳制。现代意义上的唐装泛指具有中国风格的服饰","suit");
         this._suitCoat3 = new Equipment(91,7,"金玉满堂","suit","GuanYu",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"唐代的汉服，沿袭了自东汉以来华夏传统的上衣下裳制。现代意义上的唐装泛指具有中国风格的服饰。","suit");
         this._suitCoat4 = new Equipment(92,7,"招财进宝","suit","ZhangFei",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"唐代的汉服，沿袭了自东汉以来华夏传统的上衣下裳制。现代意义上的唐装泛指具有中国风格的服饰","suit");
         this._suitCoat4 = new Equipment(92,7,"招财进宝","suit","ZhangFei",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"唐代的汉服，沿袭了自东汉以来华夏传统的上衣下裳制。现代意义上的唐装泛指具有中国风格的服饰","suit");
         this._suitCoat5 = new Equipment(98,8,"四代火影","suit","",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"波风水门：外号“木叶的黄色闪光”，将九尾封印鸣人体内的英雄，同时也是漩涡鸣人的父亲，并是自来也的徒弟","suit");
         this._suitCoat6 = new Equipment(99,9,"宇智波鼬","suit","",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"鼬：只要是人都是依靠自己的知识与认知并且被之束缚生活着的，那就叫做现实","suit");
         this._suitCoat7 = new Equipment(103,10,"武状元套装","suit","",16744448,[],[],[{
            "additionalCrit":0.05,
            "addLuckyPoint":0.05
         }],[],{"isNew":true},0,[this.setProNumber(1000)],"古代考取功名后所特有的服饰，武状元服饰穿上后信心满满，衣服似忽传来一股股强大的力量","suit");
         this._suitCoat8 = new Equipment(200,11,"虎虎生威时装","suit","",16744448,[],[],[{
            "additionalManaPoint":500,
            "additionalHealthPoint":1000
         }],[],{"isNew":true},0,[this.setProNumber(1000)],"绝版虎虎生威时装，祝所有大小朋友六一节快乐^0^","suit");
         this._suitCoat9 = new Equipment(201,12,"皇帝时装","suit","",16744448,[],[],[{
            "additionalResistance":50,
            "additionalAttackPower":50
         }],[],{"isNew":true},0,[this.setProNumber(1000)],"绝版皇帝时装，吾皇万岁万岁万万岁。","suit");
         this._special2 = new Equipment(95,6,"元宵灯笼","lantern","",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"元宵节特有的灯笼双击使用它可是有非常大的惊喜哦，有机率获得仙器或神器。","suit");
         this._special3 = new Equipment(96,6,"巧克力","valentine","",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"情人节送一盒巧克力，让你们的亲情，友情，爱情甜蜜蜜。","suit");
         this._special4 = new Equipment(97,6,"红玫瑰","valentine","",16744448,[],[],[],[],{"isNew":true},0,[this.setProNumber(100)],"玫瑰代表着爱情，送一朵玫瑰代表着一心一意，两朵代表着这世界只有你和我，你要送给你最爱的人几朵呢!","suit");
         _loc1_ = 1;
         while(_loc1_ <= 6)
         {
            this._decorations.push(this["_decoration" + _loc1_]);
            _loc1_++;
         }
         this._suitCoats.push(this._suitCoat1);
         this._suitCoats.push(this._suitCoat5);
         this._suitCoats.push(this._suitCoat6);
         this._suitCoats.push(this._suitCoat7);
         this._suitCoats.push(this._suitCoat8);
         this._suitCoats.push(this._suitCoat9);
         this._role1Armors.push(this._suitCoat2);
         this._role2Armors.push(this._suitCoat3);
         this._role3Armors.push(this._suitCoat4);
         this._specials.push(this._special1);
         this._specials.push(this._special2);
         this._specials.push(this._special4);
         this._specials.push(this._special3);
      }
      
      public function getEquipmentByID(param1:uint) : Equipment
      {
         var _loc2_:uint = this._role1Weapons.length;
         while(_loc2_-- > 0)
         {
            if(this._role1Weapons[_loc2_]._id == param1)
            {
               return this._role1Weapons[_loc2_] as Equipment;
            }
         }
         _loc2_ = this._role1Armors.length;
         while(_loc2_-- > 0)
         {
            if(this._role1Armors[_loc2_]._id == param1)
            {
               return this._role1Armors[_loc2_] as Equipment;
            }
         }
         _loc2_ = this._role2Weapons.length;
         while(_loc2_-- > 0)
         {
            if(this._role2Weapons[_loc2_]._id == param1)
            {
               return this._role2Weapons[_loc2_] as Equipment;
            }
         }
         _loc2_ = this._role2Armors.length;
         while(_loc2_-- > 0)
         {
            if(this._role2Armors[_loc2_]._id == param1)
            {
               return this._role2Armors[_loc2_] as Equipment;
            }
         }
         _loc2_ = this._role3Weapons.length;
         while(_loc2_-- > 0)
         {
            if(this._role3Weapons[_loc2_]._id == param1)
            {
               return this._role3Weapons[_loc2_] as Equipment;
            }
         }
         _loc2_ = this._role3Armors.length;
         while(_loc2_-- > 0)
         {
            if(this._role3Armors[_loc2_]._id == param1)
            {
               return this._role3Armors[_loc2_] as Equipment;
            }
         }
         _loc2_ = this._decorations.length;
         while(_loc2_-- > 0)
         {
            if(this._decorations[_loc2_]._id == param1)
            {
               return this._decorations[_loc2_] as Equipment;
            }
         }
         _loc2_ = this._suitCoats.length;
         while(_loc2_-- > 0)
         {
            if(this._suitCoats[_loc2_]._id == param1)
            {
               return this._suitCoats[_loc2_] as Equipment;
            }
         }
         _loc2_ = this._specials.length;
         while(_loc2_-- > 0)
         {
            if(this._specials[_loc2_]._id == param1)
            {
               return this._specials[_loc2_] as Equipment;
            }
         }
         return null;
      }
   }
}

