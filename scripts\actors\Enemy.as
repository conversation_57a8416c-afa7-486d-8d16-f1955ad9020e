package actors
{
   import actors.enemies.Enemy12;
   import actors.enemies.Enemy14;
   import actors.enemies.Enemy17;
   import actors.enemies.Enemy18;
   import actors.enemies.Enemy33;
   import actors.enemies.Enemy34;
   import actors.equipments.FallEquipments;
   import actors.equipments.Gold;
   import actors.equipments.Gold1;
   import actors.equipments.Gold2;
   import actors.equipments.Gold3;
   import actors.equipments.pharmaceutical.HealingSalve;
   import actors.equipments.pharmaceutical.LesserClarityPotion;
   import actors.equipments.pharmaceutical.LesserHealingSalve;
   import actors.pets.Pet;
   import actors.roles.Role1;
   import actors.roles.Role2;
   import actors.roles.Role3;
   import actors.user.User;
   import base.GameObject;
   import event.GameEvent;
   import flash.display.MovieClip;
   import flash.geom.Point;
   import util.FloatingNumber;
   import util.GameUtility;
   import util.HitTest;
   
   public class Enemy extends GameObject
   {
      
      public static const WRAP_TIME:int = 30 * 3;
      
      private var _missAnimation:MovieClip;
      
      private var _gold1:Gold1;
      
      private var _gold2:Gold2;
      
      private var _gold3:Gold3;
      
      protected var _isPatrolToLeftEdge:<PERSON>olean = false;
      
      protected var _isPatrolToRightEdge:Boolean = false;
      
      private var _hero:Hero;
      
      private var _isMagicAttack:Boolean;
      
      protected var _addPoint:Point;
      
      public var _resistance:int;
      
      protected var _experience:int = 10;
      
      protected var _bcount:int = 30;
      
      protected var _hero1:*;
      
      protected var _hero2:*;
      
      protected var _hero3:*;
      
      protected var _curAttackTarget:Hero;
      
      protected var _lastAttackTarget:*;
      
      protected var _theHeroWhoAttackedMe:Hero;
      
      public var _fallEquipmentsList:Array = [];
      
      public var _probability:Number = 0.15;
      
      public var enemyName:String = "";
      
      public var fallList:Array = [];
      
      private var _directTime:int = 30;
      
      public var _patrolPoint:Number;
      
      public var _goldPrice:Number = 10;
      
      public var _attackPower:uint;
      
      public var _attackProbablity:Number = 10;
      
      public var _wrapTime:int = 0;
      
      protected var _skill1CoolDown:uint = 0;
      
      protected var _skill2CoolDown:uint = 0;
      
      protected var _skill3CoolDown:uint = 0;
      
      protected var _skill4CoolDown:uint = 0;
      
      protected var _skill5CoolDown:uint = 0;
      
      private var tk:ThreeKingdoms = ThreeKingdoms._instance;
      
      private var _getPetAnimation:MovieClip;
      
      private var newFallList:Array = [];
      
      private var _dizzinesCount:uint = 0;
      
      internal var _counter:int = 0;
      
      public function Enemy(param1:Number, param2:Number)
      {
         super();
         this._addPoint = new Point(param1,param2);
         this.gotoAndStop(1);
         this._walkSpeed = 1;
         this._runSpeed = 2;
         this._object._maxPatrolView = 100;
         this._hero1 = ThreeKingdoms._instance._role1;
         this._hero2 = ThreeKingdoms._instance._role2;
         this._hero3 = ThreeKingdoms._instance._role3;
         GameUtility.clear();
      }
      
      override public function update() : void
      {
         super.update();
         if(this.currentLabel == "休息")
         {
            this._vx = 0;
         }
         if(this.isBoss)
         {
            ThreeKingdoms._instance._gameInfo.addBossHealthPointBar(this.enemyName,int(100 - 100 * (this._currentHealthPoint / this._totalHealthPoint)));
         }
         if(this._isDizziness)
         {
            this.updateDizzinesCount();
         }
         this.updateCounter();
         this.IntelligenceTime();
         if(!this.isBoss)
         {
            this.drawHealthPoint();
         }
         if(this.isDead())
         {
            if(this.isBoss)
            {
               ThreeKingdoms._instance._gameInfo.addBossHealthPointBar(this.enemyName,100 - 100 * Math.round(this._currentHealthPoint / this._totalHealthPoint));
               ThreeKingdoms._instance._game.transferDoor.visible = true;
            }
            if(!this.currentLabel != "死亡")
            {
               this.gotoAndStop("死亡");
            }
            if(_lifeBar.parent)
            {
               _lifeBar.parent.removeChild(_lifeBar);
            }
         }
         var _loc1_:int = 0;
         while(_loc1_ < _world._heroes.length)
         {
            Hero(_world._heroes[_loc1_]).heroIsUnderAttack(this);
            _loc1_++;
         }
         _loc1_ = 0;
         while(_loc1_ < _world._pets.length)
         {
            Pet(_world._pets[_loc1_]).heroIsUnderAttack(this);
            _loc1_++;
         }
         this.skillCoolDown();
         if(this._wrapTime > 0)
         {
            --this._wrapTime;
         }
         if(this._isFlying)
         {
            if(Math.abs(this._vy) > 8)
            {
               this._vy *= 0.8;
            }
         }
         if(this._curAttackTarget)
         {
            if(Hero(this._curAttackTarget).isDead())
            {
               this._curAttackTarget = null;
            }
         }
      }
      
      private function updateDizzinesCount() : void
      {
         if(this._dizzinesCount++ >= 45)
         {
            this._dizzinesCount = 0;
            hideDizziness();
         }
      }
      
      private function updateCounter() : void
      {
         if(_count++ >= 30)
         {
            _count = 0;
         }
      }
      
      protected function IntelligenceTime() : void
      {
         if(this._bcount-- <= 0)
         {
            this.myIntelligence();
         }
      }
      
      protected function myIntelligence() : void
      {
         if(this.isUnderAttack() || this.isAttacking() || this.isJumping() || this.isDead() || this._isDizziness)
         {
            return;
         }
         if(this._curAttackTarget == null)
         {
            this.patrol();
            if(!this.isUnderAttack())
            {
               this.selectTarget();
            }
         }
         else
         {
            this.alreadyGotAttackTarget();
         }
      }
      
      public function alreadyGotAttackTarget() : void
      {
         if(this._curAttackTarget.isDead() || this.lostTarget())
         {
            this._curAttackTarget = null;
            return;
         }
         if(this._curAttackTarget)
         {
            if(this._wrapTime > 0)
            {
               if(this._vx > 0)
               {
                  this.turnRight();
               }
               else
               {
                  this.turnLeft();
               }
               if(this.y <= 150)
               {
                  this._vy = 0;
               }
               else
               {
                  this._vy = -3;
               }
            }
            else if(this.isSeek())
            {
               this.moveTowardHero();
            }
            else
            {
               if(_count % 24 == 0)
               {
                  this.startAttacking();
                  this.setWrapTime();
               }
               if(this._isFlying)
               {
                  this._vy = 0;
               }
            }
         }
         else
         {
            this.patrol();
         }
      }
      
      public function turnRight() : void
      {
         this._isRight = true;
         this._isLeft = false;
         GameUtility.flipHorizontal(this,-1);
      }
      
      public function turnLeft() : void
      {
         this._isRight = false;
         this._isLeft = true;
         GameUtility.flipHorizontal(this,1);
      }
      
      public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(10))
         {
            this.wait();
         }
         else if(GameUtility.getRandomNumber(this._attackProbablity))
         {
            this.attack();
            this.setWrapTime();
         }
      }
      
      public function setWrapTime() : void
      {
      }
      
      public function skillCoolDown() : void
      {
         if(this._skill1CoolDown-- <= 0)
         {
            this._skill1CoolDown = 0;
         }
         if(this._skill2CoolDown-- <= 0)
         {
            this._skill2CoolDown = 0;
         }
         if(this._skill3CoolDown-- <= 0)
         {
            this._skill3CoolDown = 0;
         }
         if(this._skill4CoolDown-- <= 0)
         {
            this._skill4CoolDown = 0;
         }
         if(this._skill5CoolDown-- <= 0)
         {
            this._skill5CoolDown = 0;
         }
      }
      
      override protected function addBeAttackEffect(param1:GameObject) : void
      {
         var _loc2_:MovieClip = GameUtility.getObject("EnemyBeHurt1");
         _loc2_.x = this.collipse.x;
         _loc2_.y = this.collipse.y;
         this.addChild(_loc2_);
      }
      
      protected function assignTarget() : Boolean
      {
         if(this._curAttackTarget == null)
         {
            if(this._lastAttackTarget != null)
            {
               if(this.isUnderAttack() && GameUtility.getDistance(this._lastAttackTarget,this) > _object._attackRange && GameUtility.getDistance(this._lastAttackTarget,this) < _object._alertRange)
               {
                  this._curAttackTarget = this._lastAttackTarget;
                  return true;
               }
               if(this.isUnderAttack() && GameUtility.getDistance(this._hero1,this) <= _object._attackRange && !this._hero1.isDead())
               {
                  this._curAttackTarget = this._lastAttackTarget;
                  return true;
               }
            }
         }
         return false;
      }
      
      public function selectTarget() : void
      {
         if(this.assignTarget())
         {
            return;
         }
         if(Boolean(ThreeKingdoms._instance._role2) && !ThreeKingdoms._instance._role2.isDead())
         {
            if(GameUtility.getDistance(this,ThreeKingdoms._instance._role2) <= _object._maxPatrolView)
            {
               this._curAttackTarget = ThreeKingdoms._instance._role2;
               return;
            }
         }
         if(Boolean(ThreeKingdoms._instance._role1) && !ThreeKingdoms._instance._role1.isDead())
         {
            if(GameUtility.getDistance(this,ThreeKingdoms._instance._role1) <= _object._maxPatrolView)
            {
               this._curAttackTarget = ThreeKingdoms._instance._role1;
               return;
            }
         }
         if(Boolean(ThreeKingdoms._instance._role3) && !ThreeKingdoms._instance._role3.isDead())
         {
            if(GameUtility.getDistance(this,ThreeKingdoms._instance._role3) <= _object._maxPatrolView)
            {
               this._curAttackTarget = ThreeKingdoms._instance._role3;
               return;
            }
         }
         if(ThreeKingdoms._instance._role3 && ThreeKingdoms._instance._role3.pet && !ThreeKingdoms._instance._role3.pet.isDead())
         {
            if(GameUtility.getDistance(this,ThreeKingdoms._instance._role3.pet) <= _object._maxPatrolView)
            {
               this._curAttackTarget = ThreeKingdoms._instance._role3.pet;
               return;
            }
         }
         if(ThreeKingdoms._instance._role1 && ThreeKingdoms._instance._role1.pet && !ThreeKingdoms._instance._role1.pet.isDead())
         {
            if(GameUtility.getDistance(this,ThreeKingdoms._instance._role1.pet) <= _object._maxPatrolView)
            {
               this._curAttackTarget = ThreeKingdoms._instance._role1.pet;
               return;
            }
         }
         if(ThreeKingdoms._instance._role2 && ThreeKingdoms._instance._role2.pet && !ThreeKingdoms._instance._role2.pet.isDead())
         {
            if(GameUtility.getDistance(this,ThreeKingdoms._instance._role2.pet) <= _object._maxPatrolView)
            {
               this._curAttackTarget = ThreeKingdoms._instance._role2.pet;
               return;
            }
         }
      }
      
      public function wait() : void
      {
         _vx = 0;
         this.gotoAndStop("休息");
      }
      
      override public function attack() : void
      {
         this.steer();
         _vx = 0;
         this.newAttackID();
         this.gotoAndStop("攻击1");
         this._lastHit = "攻击1";
      }
      
      public function enemyIsUnderAttack(param1:Hero) : void
      {
         if(this._underAttackIDVector.indexOf(param1.getAttackID()) != -1)
         {
            return;
         }
         if(this.isDead())
         {
            return;
         }
         var _loc2_:Object = param1._attackBackInfomationDictionary[param1._lastHit];
         if(_loc2_)
         {
            this._theHeroWhoAttackedMe = param1;
            if(Boolean(param1.body) && Boolean(this.collipse))
            {
               if(param1.body.sword)
               {
                  if(!this.collipse.hitTestObject(param1.body.sword))
                  {
                     return;
                  }
                  if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_whosYourDaddy"))
                  {
                     return;
                  }
                  if(HitTest.complexHitTestObject(param1.body.sword,this.collipse))
                  {
                     this.afterAttack(param1,_loc2_);
                     this._curAttackTarget = param1;
                     this._underAttackIDVector.push(param1.getAttackID());
                  }
               }
            }
         }
      }
      
      public function afterAttack(param1:Hero = null, param2:Object = null) : void
      {
         this._theHeroWhoAttackedMe = param1;
         if(this.isDead())
         {
            return;
         }
         this.showLifeBar();
         _vx = 0;
         this.beenAttackBack(param1,param2.attackBackVelocity[0],param2.attackBackVelocity[1]);
         ++User._batterTimes;
         var _loc3_:int = param1.getSpecificAttackPowerByType(param1._lastHit);
         if(param1 is Role1 && param1._properties._propertiesObject.addCrazy == true)
         {
            if(Math.random() < 0.05 && param1._properties.getCrit() < 0.5 && !param1._isAddCirt)
            {
               param1._addCirt = 0.5 - param1._properties.getCrit();
               param1._properties.setCrit(param1._properties.getCrit() + param1._addCirt);
               param1._cirtTimer = 150;
               param1._isAddCirt = true;
               param1.mcCrazy.visible = true;
               param1.mcCrazy.gotoAndPlay(1);
            }
         }
         if(Math.random() < param1._properties.getCrit())
         {
            _loc3_ *= 2;
            _isCrit = true;
         }
         else
         {
            _isCrit = false;
         }
         var _loc4_:int = 0;
         if(param1._lastHit == "攻击6" && param1 is Role2 || param1._lastHit == "攻击5" && param1 is Role1 || param1 is Pet)
         {
            _loc4_ = _loc3_;
         }
         else
         {
            _loc4_ = this.getRealHurtAfterCaculation(_loc3_,param2);
         }
         if(this._theHeroWhoAttackedMe.getPlayer()._isBadPlayer)
         {
            this._currentHealthPoint -= 0;
         }
         else
         {
            this._currentHealthPoint -= _loc4_;
         }
         if(_currentHealthPoint <= 0)
         {
            _currentHealthPoint = 0;
         }
         if(param1._lastHit != "攻击9")
         {
            if(param1 is Role2 && param1._lastHit == "攻击8")
            {
               _world._eventManager.dispatchEvent(new GameEvent(GameEvent.ENEMY_IS_UNDER_ATTACK,[_loc4_ * 0.5,param1]));
            }
            else
            {
               _world._eventManager.dispatchEvent(new GameEvent(GameEvent.ENEMY_IS_UNDER_ATTACK,[_loc4_,param1]));
            }
         }
         this.addMonsterGetHurtAnimation(_loc4_,_loc3_);
         if(this.isDead())
         {
            if(this.isBoss)
            {
               ThreeKingdoms._instance._gameInfo.addBossHealthPointBar(this.enemyName,100 - Math.round(100 * (this._currentHealthPoint / this._totalHealthPoint)));
               ThreeKingdoms._instance._game.transferDoor.visible = true;
            }
            if(this is Enemy33 || this is Enemy34)
            {
               ThreeKingdoms._instance._game.transferDoor.visible = true;
            }
            if(!this.currentLabel != "死亡")
            {
               if(this._isDizziness)
               {
                  this.hideDizziness();
               }
               this.gotoAndStop("死亡");
            }
            if(this.tk._doubleGet2)
            {
               if(ThreeKingdoms._gameWorld._heroes.indexOf(this._theHeroWhoAttackedMe) != -1)
               {
                  Hero(this._theHeroWhoAttackedMe)._properties.setCurrentExperience(this._experience);
                  if(Boolean(Hero(this._theHeroWhoAttackedMe).pet) && !Hero(this._theHeroWhoAttackedMe).pet.isDead())
                  {
                     Hero(this._theHeroWhoAttackedMe).pet._properties.setCurrentExperience(this._experience);
                  }
               }
               if(this._theHeroWhoAttackedMe is Pet && Pet(this._theHeroWhoAttackedMe)._master && !Pet(this._theHeroWhoAttackedMe)._master.isDead())
               {
                  Pet(this._theHeroWhoAttackedMe)._properties.setCurrentExperience(this._experience);
                  Pet(this._theHeroWhoAttackedMe)._master._properties.setCurrentExperience(this._experience);
               }
            }
            else
            {
               if(ThreeKingdoms._gameWorld._heroes.indexOf(this._theHeroWhoAttackedMe) != -1)
               {
                  Hero(this._theHeroWhoAttackedMe)._properties.setCurrentExperience(this._experience);
                  if(Boolean(Hero(this._theHeroWhoAttackedMe).pet) && !Hero(this._theHeroWhoAttackedMe).pet.isDead())
                  {
                     Hero(this._theHeroWhoAttackedMe).pet._properties.setCurrentExperience(this._experience);
                  }
               }
               if(this._theHeroWhoAttackedMe is Pet && Pet(this._theHeroWhoAttackedMe)._master && !Pet(this._theHeroWhoAttackedMe)._master.isDead())
               {
                  Pet(this._theHeroWhoAttackedMe)._properties.setCurrentExperience(this._experience);
                  Pet(this._theHeroWhoAttackedMe)._master._properties.setCurrentExperience(this._experience);
               }
            }
            if(_lifeBar.parent)
            {
               _lifeBar.parent.removeChild(_lifeBar);
            }
            this.onCompleteHandler();
         }
         else
         {
            if(!this.isBoss && param1._lastHit == "攻击8" && param1 is Role3 && !this._isDizziness && GameUtility.getRandomNumber(40) && !(this is Enemy12))
            {
               this.showDizziness();
            }
            if(this.currentLabel != "被攻击")
            {
               this.gotoAndStop("被攻击");
            }
            else if(this.body)
            {
               this.body.gotoAndPlay("休息");
            }
         }
         this.addBeAttackEffect(null);
         SoundManager.play("BeattackByRole1");
      }
      
      public function onCompleteHandler() : void
      {
         destroy();
      }
      
      protected function addMonsterGetHurtAnimation(param1:int, param2:int) : void
      {
         var _loc3_:FloatingNumber = new FloatingNumber();
         ThreeKingdoms._instance._game.addChild(_loc3_);
         if(_isCrit)
         {
            _loc3_.addFloatingNumberBitmap("critnum",param1,this.x - 20,this.y - 60,16);
         }
         else
         {
            _loc3_.addFloatingNumberBitmap("mhurtnum",param1,this.x - 20,this.y - 60,20);
         }
      }
      
      protected function getRealHurtAfterCaculation(param1:int, param2:Object) : int
      {
         var _loc3_:int = 0;
         if(param2)
         {
            if(param2.attackType == "physical")
            {
               if(param1 > this._resistance)
               {
                  _loc3_ = param1 - this._resistance;
               }
               else
               {
                  _loc3_ = 1;
               }
            }
            else if(param2.attackType == "magic")
            {
               _loc3_ = param1;
            }
         }
         else
         {
            _loc3_ = 1;
         }
         return _loc3_;
      }
      
      override public function getSpecificAttackPowerByType(param1:String) : int
      {
         var _loc2_:Object = this._attackBackInfomationDictionary[param1];
         if(Boolean(_loc2_) && Boolean(_loc2_.attackPower))
         {
            return _loc2_.attackPower;
         }
         return 1;
      }
      
      protected function patrol() : void
      {
         if(this.currentFrameLabel != "行走")
         {
            this.gotoAndStop("行走");
         }
         if(this.x <= this._patrolPoint - _object._maxPatrolView)
         {
            this._isPatrolToLeftEdge = true;
            this._isPatrolToRightEdge = false;
         }
         else if(this.x >= this._patrolPoint + _object._maxPatrolView)
         {
            this._isPatrolToRightEdge = true;
            this._isPatrolToLeftEdge = false;
         }
         if(this._isPatrolToLeftEdge)
         {
            _object._direction = RIGHT;
            GameUtility.flipHorizontal(this,-1);
         }
         else if(this._isPatrolToRightEdge)
         {
            _object._direction = LEFT;
            GameUtility.flipHorizontal(this,1);
         }
         if(_isHitLeft || _isHitRight)
         {
            if(GameUtility.getRandomNumber(50))
            {
               if(this.x > 4900)
               {
                  _object._direction = LEFT;
                  GameUtility.flipHorizontal(this,1);
               }
               else if(this.x < 50)
               {
                  _object._direction = RIGHT;
                  GameUtility.flipHorizontal(this,-1);
               }
               else
               {
                  this.jump();
               }
            }
            else
            {
               if(!isInSky() && _isHitRight)
               {
                  _object._direction = LEFT;
                  GameUtility.flipHorizontal(this,1);
               }
               else
               {
                  !isInSky() && _isHitLeft;
               }
               _object._direction = RIGHT;
               GameUtility.flipHorizontal(this,-1);
            }
            _isHitLeft = false;
            _isHitRight = false;
         }
         if(this._object._direction == LEFT)
         {
            _vx = -_walkSpeed;
         }
         else
         {
            _vx = _walkSpeed;
         }
      }
      
      public function lostTarget() : Boolean
      {
         if(this._curAttackTarget)
         {
            if(GameUtility.getDistance(this,this._curAttackTarget) > _object._maxPatrolView)
            {
               this._curAttackTarget = null;
               return true;
            }
         }
         return false;
      }
      
      public function steer() : void
      {
         if(this._curAttackTarget)
         {
            if(this._curAttackTarget.x > this.x)
            {
               _object._direction = RIGHT;
               GameUtility.flipHorizontal(this,-1);
            }
            else if(this._curAttackTarget.x < this.x)
            {
               _object._direction = LEFT;
               GameUtility.flipHorizontal(this,1);
            }
         }
      }
      
      private function directTime() : void
      {
         --this._directTime;
         if(this._directTime != 0)
         {
            return;
         }
         this._directTime = 30;
      }
      
      public function isSeek() : Boolean
      {
         return GameUtility.getDistance(this,this._curAttackTarget) > _object._attackRange && GameUtility.getDistance(this,this._curAttackTarget) <= _object._alertRange;
      }
      
      protected function moveTowardHero() : void
      {
         if(this._curAttackTarget)
         {
            if(_count % 30 == 0)
            {
               this.steer();
            }
         }
         if(this.currentLabel != "行走")
         {
            this.gotoAndStop("行走");
         }
         if(_isHitLeft || _isHitRight)
         {
            if(GameUtility.getRandomNumber(50))
            {
               if(this.x > 4900)
               {
                  _object._direction = LEFT;
                  GameUtility.flipHorizontal(this,1);
               }
               else if(this.x < 50)
               {
                  _object._direction = RIGHT;
                  GameUtility.flipHorizontal(this,-1);
               }
               else
               {
                  this.jump();
               }
            }
            else
            {
               if(!isInSky() && _isHitRight)
               {
                  _object._direction = LEFT;
                  GameUtility.flipHorizontal(this,1);
               }
               else
               {
                  !isInSky() && _isHitLeft;
               }
               _object._direction = RIGHT;
               GameUtility.flipHorizontal(this,-1);
            }
            _isHitLeft = false;
            _isHitRight = false;
         }
         if(this._object._direction == LEFT)
         {
            _vx = -_walkSpeed;
         }
         else if(this._object._direction == RIGHT)
         {
            _vx = _walkSpeed;
         }
      }
      
      override public function jump() : void
      {
         if(this.isJumping() || this.isUnderAttack() || this.isAttacking())
         {
            return;
         }
         this.gotoAndStop("跳跃");
         _vy = -20;
         this._isJumping = true;
         if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") == 2)
         {
            ThreeKingdoms._instance._protectedProperty.setProperty(this,"_jumpCount",1);
         }
         else if(ThreeKingdoms._instance._protectedProperty.getProperty(this,"_jumpCount") == 1)
         {
            ThreeKingdoms._instance._protectedProperty.setProperty(this,"_jumpCount",0);
         }
      }
      
      override public function isJumping() : Boolean
      {
         return this.currentLabel == "跳跃";
      }
      
      public function fallEquipment() : void
      {
         if(this is Enemy33 || this is Enemy34)
         {
            if(this.tk._doubleGet)
            {
               if(Math.random() < this._probability * 1.5)
               {
                  this.fallPet();
               }
            }
            else if(Math.random() < this._probability)
            {
               this.fallPet();
            }
         }
         if(this.isBoss)
         {
            if(ThreeKingdoms._instance._currentLevel <= 9)
            {
               if(this._theHeroWhoAttackedMe is Role1 || this._theHeroWhoAttackedMe is Role2 || this._theHeroWhoAttackedMe is Role3)
               {
                  if(Math.random() < ThreeKingdoms._instance._currentLevel * 0.01)
                  {
                     this.fallSpecial(61);
                  }
               }
            }
         }
         if(this.isBoss && ThreeKingdoms._instance._currentLevel < 5)
         {
            this.fallEquipmentForEnemy();
            if(this.tk._doubleGet)
            {
               if(Math.random() < this._probability)
               {
                  this.fallEquipmentForEnemy();
               }
            }
            else if(Math.random() < this._probability * 0.6)
            {
               this.fallEquipmentForEnemy();
            }
         }
         else if(this.tk._doubleGet)
         {
            if(Math.random() < this._probability)
            {
               this.fallEquipmentForEnemy();
            }
         }
         else if(this._theHeroWhoAttackedMe is Role1 || this._theHeroWhoAttackedMe is Role2 || this._theHeroWhoAttackedMe is Role3)
         {
            if(Math.random() < this._probability * 0.6 + Hero(this._theHeroWhoAttackedMe)._properties.getLuckyPoint())
            {
               this.fallEquipmentForEnemy();
            }
         }
         else if(Math.random() < this._probability)
         {
            this.fallEquipmentForEnemy();
         }
      }
      
      private function fallSpecial(param1:uint) : void
      {
         var _loc2_:FallEquipments = new FallEquipments({
            "id":param1,
            "qualityID":[0],
            "type":"special"
         },0);
         var _loc3_:Point = this.parent.localToGlobal(new Point(this.x,this.y));
         if(_loc3_.x > 40 && _loc3_.x < 920)
         {
            _loc2_.x = this.x;
         }
         else if(_loc3_.x <= 40)
         {
            _loc2_.x = this.x + 40 - _loc3_.x;
         }
         else if(_loc3_.x >= 920)
         {
            _loc2_.x = this.x + 920 - _loc3_.x;
         }
         _loc2_.y = this.y - this.height - 1;
         ThreeKingdoms._instance._game.addChild(_loc2_);
         ThreeKingdoms._gameWorld._otherList.push(_loc2_);
      }
      
      private function fallPet() : void
      {
         if(this._theHeroWhoAttackedMe)
         {
            this.tk._pet1 = GameUtility.getObject("actors.pets.Pet1");
            this.tk._pet1.setPlayer(this.tk._pet1user);
            this.tk._pet1user._roleID = 3;
            this.tk._pet1user.hero = this.tk._pet1;
            this.tk._pet1.x = 200;
            this.tk._pet1.y = 100;
            if(this._theHeroWhoAttackedMe is Pet)
            {
               if(Pet(this._theHeroWhoAttackedMe)._master.getPlayer()._petsVector.length < 5)
               {
                  Pet(this._theHeroWhoAttackedMe)._master.getPlayer()._petsVector.push(this.tk._pet1);
                  this.tk._pet1.initProperties();
                  this.addGetPet();
               }
            }
            else if(Hero(this._theHeroWhoAttackedMe).getPlayer()._petsVector.length < 5)
            {
               Hero(this._theHeroWhoAttackedMe).getPlayer()._petsVector.push(this.tk._pet1);
               this.tk._pet1.initProperties();
               this.addGetPet();
            }
         }
      }
      
      private function fallEquipmentForEnemy() : void
      {
         var _loc6_:uint = 0;
         var _loc7_:FallEquipments = null;
         var _loc8_:Point = null;
         var _loc1_:int = 0;
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Array = [];
         this.newFallList = [];
         _loc1_ = 0;
         while(_loc1_ < this._fallEquipmentsList.length)
         {
            if(this._fallEquipmentsList[_loc1_].id >= 53 && this._fallEquipmentsList[_loc1_].id <= 55 || this._fallEquipmentsList[_loc1_].id == 60 || this._fallEquipmentsList[_loc1_].id == 61 || this._fallEquipmentsList[_loc1_].id == 102 || this._fallEquipmentsList[_loc1_].id == 100 || this._fallEquipmentsList[_loc1_].id == 101 || this._fallEquipmentsList[_loc1_].id == 99)
            {
               _loc2_.push(this._fallEquipmentsList[_loc1_]);
               _loc3_.push(this._fallEquipmentsList[_loc1_]);
               _loc4_.push(this._fallEquipmentsList[_loc1_]);
            }
            if(this._fallEquipmentsList[_loc1_].id >= 1 && this._fallEquipmentsList[_loc1_].id <= 26 || this._fallEquipmentsList[_loc1_].id == 56 || this._fallEquipmentsList[_loc1_].id == 58 || this._fallEquipmentsList[_loc1_].id == 90 || this._fallEquipmentsList[_loc1_].id == 93)
            {
               _loc2_.push(this._fallEquipmentsList[_loc1_]);
            }
            if(this._fallEquipmentsList[_loc1_].id >= 27 && this._fallEquipmentsList[_loc1_].id <= 52 || this._fallEquipmentsList[_loc1_].id == 57 || this._fallEquipmentsList[_loc1_].id == 59 || this._fallEquipmentsList[_loc1_].id == 91 || this._fallEquipmentsList[_loc1_].id == 94)
            {
               _loc3_.push(this._fallEquipmentsList[_loc1_]);
            }
            if(this._fallEquipmentsList[_loc1_].id >= 62 && this._fallEquipmentsList[_loc1_].id <= 89 || this._fallEquipmentsList[_loc1_].id == 92 || this._fallEquipmentsList[_loc1_].id == 68)
            {
               _loc4_.push(this._fallEquipmentsList[_loc1_]);
            }
            _loc1_++;
         }
         if(ThreeKingdoms._instance._role1)
         {
            _loc1_ = 0;
            while(_loc1_ < _loc2_.length)
            {
               this.newFallList.push(_loc2_[_loc1_]);
               _loc1_++;
            }
         }
         if(ThreeKingdoms._instance._role2)
         {
            _loc1_ = 0;
            while(_loc1_ < _loc3_.length)
            {
               this.newFallList.push(_loc3_[_loc1_]);
               _loc1_++;
            }
         }
         if(ThreeKingdoms._instance._role3)
         {
            _loc1_ = 0;
            while(_loc1_ < _loc4_.length)
            {
               this.newFallList.push(_loc4_[_loc1_]);
               _loc1_++;
            }
         }
         var _loc5_:uint = Math.round(Math.random() * (this.newFallList.length - 1));
         if(this is Enemy14)
         {
            if(Math.random() < 0.5)
            {
               if(Math.random() < 0.5)
               {
                  _loc5_ = 0;
               }
               else
               {
                  _loc5_ = 1;
               }
            }
         }
         if(this is Enemy17 || this is Enemy18)
         {
            if(Math.random() < 0.5)
            {
               _loc5_ = 0;
            }
         }
         if(Boolean(this.newFallList[_loc5_]) && this.newFallList[_loc5_] != undefined)
         {
            _loc6_ = Math.round(Math.random() * ((this.newFallList[_loc5_].qualityID as Array).length - 1));
            _loc7_ = new FallEquipments(this.newFallList[_loc5_],this.newFallList[_loc5_].qualityID[_loc6_]);
            _loc8_ = this.parent.localToGlobal(new Point(this.x,this.y));
            if(_loc8_.x > 40 && _loc8_.x < 920)
            {
               _loc7_.x = this.x;
            }
            else if(_loc8_.x <= 40)
            {
               _loc7_.x = this.x + 40 - _loc8_.x;
            }
            else if(_loc8_.x >= 920)
            {
               _loc7_.x = this.x + 920 - _loc8_.x;
            }
            _loc7_.y = this.y - this.height - 1;
            ThreeKingdoms._instance._game.addChild(_loc7_);
            ThreeKingdoms._gameWorld._otherList.push(_loc7_);
         }
      }
      
      public function fallPharmaceutical() : void
      {
         var _loc1_:* = undefined;
         var _loc2_:Number = NaN;
         var _loc3_:Point = null;
         if(Math.random() >= 0.58)
         {
            _loc2_ = Math.random();
            if(_loc2_ <= 0.1)
            {
               if(_loc2_ <= 0.5)
               {
                  if(Math.random() >= 0.5)
                  {
                     _loc1_ = new LesserHealingSalve();
                  }
                  else
                  {
                     _loc1_ = new HealingSalve();
                  }
               }
               else
               {
                  _loc1_ = new LesserHealingSalve();
               }
            }
         }
         else if(Math.random() <= 0.38)
         {
            _loc1_ = new LesserClarityPotion();
         }
         if(_loc1_)
         {
            _loc3_ = this.parent.localToGlobal(new Point(this.x,this.y));
            if(_loc3_.x > 40 && _loc3_.x < 920)
            {
               _loc1_.x = this.x;
            }
            else if(_loc3_.x <= 40)
            {
               _loc1_.x = this.x + 40 - _loc3_.x;
            }
            else if(_loc3_.x >= 920)
            {
               _loc1_.x = this.x + 920 - _loc3_.x;
            }
            _loc1_.y = this.y - this.height - 1;
            ThreeKingdoms._instance._game.addChild(_loc1_);
            ThreeKingdoms._gameWorld._otherList.push(_loc1_);
         }
      }
      
      public function dropGold() : void
      {
         var _loc1_:Gold = new Gold();
         _loc1_.price = this._goldPrice;
         if(_loc1_.price > 100 && _loc1_.price < 500)
         {
            _loc1_ = GameUtility.getObject("actors.equipments.Gold2") as Gold;
         }
         else if(_loc1_.price < 100)
         {
            _loc1_ = GameUtility.getObject("actors.equipments.Gold1") as Gold;
         }
         else if(_loc1_.price > 500)
         {
            _loc1_ = GameUtility.getObject("actors.equipments.Gold3") as Gold;
         }
         ThreeKingdoms._instance._game.addChild(_loc1_);
         _loc1_.price = this._goldPrice;
         ThreeKingdoms._gameWorld._golds.push(_loc1_);
         var _loc2_:Point = this.parent.localToGlobal(new Point(this.x,this.y));
         if(_loc2_.x > 40 && _loc2_.x < 920)
         {
            _loc1_.x = this.x;
         }
         else if(_loc2_.x <= 40)
         {
            _loc1_.x = this.x + 40 - _loc2_.x;
         }
         else if(_loc2_.x >= 920)
         {
            _loc1_.x = this.x + 920 - _loc2_.x;
         }
         _loc1_.y = this.y - this.height - 1;
         this.fallPharmaceutical();
         this.fallEquipment();
      }
      
      private function addMissAnimation() : void
      {
         this._missAnimation = GameUtility.getObject("miss");
         this._missAnimation.x = this.x - 1;
         this._missAnimation.y = this.y - this.height / 2 + 100;
         ThreeKingdoms._instance._game.addChild(this._missAnimation);
      }
      
      private function addGetPet() : void
      {
         this._getPetAnimation = GameUtility.getObject("GetPet");
         this._getPetAnimation.x = this.x - 1;
         this._getPetAnimation.y = this.y - this.height / 2 + 30;
         ThreeKingdoms._instance._game.addChild(this._getPetAnimation);
      }
      
      public function removeThis() : void
      {
         var _loc1_:int = int(_world._enemies.indexOf(this));
         _world._enemies.splice(_loc1_,1);
         GameUtility.clearDisplayList(this);
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         this.destroy();
      }
   }
}

