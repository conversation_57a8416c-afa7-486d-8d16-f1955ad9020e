package actors.menu
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   
   public class StrengthenEquipment extends MovieClip
   {
      
      public var btnClose:SimpleButton;
      
      public function StrengthenEquipment()
      {
         super();
         this.btnClose.addEventListener(MouseEvent.CLICK,this.onClickHandler);
      }
      
      public function onClickHandler(param1:*) : void
      {
         this.removeChild(this.btnClose);
         stage.focus = null;
         this.parent.removeChild(this);
         ThreeKingdoms._instance._gameInfo.visible = true;
      }
   }
}

