package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol1007")]
   public dynamic class BossHealthPointBar extends MovieClip
   {
      
      public var namemc:MovieClip;
      
      public function BossHealthPointBar()
      {
         super();
         addFrameScript(99,this.frame100);
      }
      
      internal function frame100() : *
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
   }
}

