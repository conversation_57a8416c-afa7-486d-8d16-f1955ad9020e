package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol715")]
   public dynamic class LevelUp extends MovieClip
   {
      
      public function LevelUp()
      {
         super();
         addFrameScript(27,this.frame28);
      }
      
      internal function frame28() : *
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
   }
}

