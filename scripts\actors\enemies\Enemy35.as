package actors.enemies
{
   import actors.Enemy;
   
   public class Enemy35 extends Enemy
   {
      
      public function Enemy35(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 450;
         this._object._alertRange = 450;
         this._object._attackRange = 300;
         this._currentHealthPoint = 20000;
         this._totalHealthPoint = 20000;
         this._resistance = 180;
         this._experience = 500;
         this._attackProbablity = 75;
         this._walkSpeed = 3.2;
         this._goldPrice = 500 + Math.round(Math.random() * 50);
         this._probability = 0.05;
         this._fallEquipmentsList = [{
            "id":93,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":68,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":94,
            "qualityID":[0],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,2],
            "attackInterval":4,
            "attackPower":285 + Math.round(Math.random() * 73),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         super.startAttacking();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击";
      }
      
      override public function attack() : void
      {
         _vx = 0;
         steer();
         this._lastHit = "攻击";
         this.gotoAndStop("攻击");
         setYourDaddysTime(16);
         newAttackID();
      }
   }
}

