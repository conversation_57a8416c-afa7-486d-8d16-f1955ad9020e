package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy24 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy24(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 900;
         this._object._alertRange = 900;
         this._object._attackRange = 350;
         this._currentHealthPoint = 90000;
         this._walkSpeed = 6;
         this._totalHealthPoint = 90000;
         this._attackProbablity = 70;
         this._resistance = 150;
         this._experience = 1470;
         this.isBoss = true;
         this._goldPrice = 700;
         this.enemyName = "华雄";
         this._probability = 0.35;
         this._fallEquipmentsList = [{
            "id":84,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":85,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":19,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":20,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":45,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":46,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":6,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":32,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":67,
            "qualityID":[0,1],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":382 + Math.round(Math.random() * 74),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":382 + Math.round(Math.random() * 74),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":382 + Math.round(Math.random() * 74),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击4"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":382 + Math.round(Math.random() * 74),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || currentLabel == "攻击2" || currentLabel == "攻击3" || currentLabel == "攻击4";
      }
      
      override public function startAttacking() : void
      {
         if(_curAttackTarget)
         {
            if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) > 300 && GameUtility.getDistance(this,this._curAttackTarget) < 350)
               {
                  if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 240;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 250 && GameUtility.getDistance(this,this._curAttackTarget) <= 300)
               {
                  if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 240;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 360;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 150 && GameUtility.getDistance(this,this._curAttackTarget) <= 250)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 300;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 240;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 360;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) <= 150)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 300;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 240;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 360;
                  }
                  else
                  {
                     this.attack();
                  }
               }
               else
               {
                  moveTowardHero();
               }
            }
            else
            {
               moveTowardHero();
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         setYourDaddysTime(39);
         newAttackID();
      }
      
      override public function realseSkill2() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击3";
         this.gotoAndStop("攻击3");
         setYourDaddysTime(38);
         newAttackID();
      }
      
      override public function realseSkill3() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击4";
         this.gotoAndStop("攻击4");
         setYourDaddysTime(42);
         newAttackID();
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(8);
         newAttackID();
      }
   }
}

