package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   
   public class Enemy34 extends Enemy
   {
      
      public function Enemy34(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 400;
         this._walkSpeed = 3;
         this._object._alertRange = 400;
         this._object._attackRange = 150;
         this._currentHealthPoint = 25000;
         this._totalHealthPoint = 25000;
         this._attackProbablity = 70;
         this._resistance = 65;
         this._experience = 500;
         this._probability = 0.05;
         this._goldPrice = 140 + Math.round(Math.random() * 15);
         this._fallEquipmentsList = [];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":150 + Math.round(Math.random() * 35),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,-1.5],
            "attackInterval":4,
            "attackPower":200 + Math.round(Math.random() * 35),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(15);
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         newAttackID();
      }
      
      override public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(20))
         {
            wait();
         }
         else if(GameUtility.getRandomNumber(_attackProbablity))
         {
            if(_skill1CoolDown == 0)
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) <= 400)
               {
                  this.realseSkill1();
                  _skill1CoolDown = 150;
               }
               else
               {
                  moveTowardHero();
               }
            }
            else if(GameUtility.getDistance(this,this._curAttackTarget) <= 120)
            {
               steer();
               _vx = 0;
               setYourDaddysTime(17);
               this._lastHit = "攻击1";
               this.gotoAndStop("攻击1");
               newAttackID();
            }
            else
            {
               moveTowardHero();
            }
         }
         else
         {
            moveTowardHero();
         }
      }
   }
}

