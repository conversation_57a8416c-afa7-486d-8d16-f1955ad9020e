package actors.enemies
{
   import actors.Enemy;
   import actors.missles.Enemy21Missle;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy21 extends Enemy
   {
      
      private var _missle:Enemy21Missle;
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy21(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 1000;
         this._object._alertRange = 1000;
         this._object._attackRange = 900;
         this._currentHealthPoint = 15000;
         this._totalHealthPoint = 15000;
         this._resistance = 180;
         this._experience = 500;
         this._attackProbablity = 72;
         this._goldPrice = 350;
         this._walkSpeed = 3.3;
         this._probability = 0.08;
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[4,5],
            "attackInterval":4,
            "attackPower":352 + int(Math.random() * 36),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[4,5],
            "attackInterval":4,
            "attackPower":352 + int(Math.random() * 36),
            "attackType":"physical"
         };
         if(ThreeKingdoms._instance._currentLevel == 8)
         {
            this._currentHealthPoint = 30000;
            this._totalHealthPoint = 30000;
            this._experience = 1200;
            this._goldPrice = 700;
            this._probability = 0.4;
            this._attackBackInfomationDictionary["攻击1"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[4,5],
               "attackInterval":4,
               "attackPower":332 + int(Math.random() * 50),
               "attackType":"physical"
            };
            this._attackBackInfomationDictionary["攻击2"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[4,5],
               "attackInterval":4,
               "attackPower":332 + int(Math.random() * 50),
               "attackType":"physical"
            };
         }
         this._fallEquipmentsList = [{
            "id":66,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":19,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":20,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":21,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":22,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":82,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":83,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":84,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":85,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":45,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":46,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":47,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":48,
            "qualityID":[0],
            "type":"equipment"
         }];
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      public function addMissle() : void
      {
         if(_curAttackTarget)
         {
            this._missle = new Enemy21Missle(this,this._curAttackTarget);
            this._missle.x = this.x;
            this._missle.y = this.y;
            this._missle.x = this.x - 85.15;
            this._missle.y = this.y + 36.85;
            if(_curAttackTarget.x > this._missle.x)
            {
               GameUtility.flipHorizontal(this._missle,-1);
            }
            else
            {
               GameUtility.flipHorizontal(this._missle,1);
            }
         }
         this.parent.addChild(this._missle);
         ThreeKingdoms._gameWorld.addMissle(this._missle);
      }
      
      override public function startAttacking() : void
      {
         if(_curAttackTarget)
         {
            if(GameUtility.getRandomNumber(20))
            {
               wait();
            }
            else if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) < 900 && GameUtility.getDistance(this,this._curAttackTarget) > 200)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 240;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) <= 200)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 240;
                  }
                  else
                  {
                     this.attack();
                  }
               }
               else
               {
                  moveTowardHero();
               }
            }
            else
            {
               moveTowardHero();
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(32);
         newAttackID();
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         setYourDaddysTime(34);
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return super.isAttacking();
      }
   }
}

