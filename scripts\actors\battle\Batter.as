package actors.battle
{
   import flash.display.MovieClip;
   import util.FloatingNumber;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol558")]
   public dynamic class Batter extends MovieClip
   {
      
      private var _numberSprite:FloatingNumber;
      
      public function Batter()
      {
         super();
      }
      
      public function addBatterNumber(param1:int) : void
      {
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         if(param1 < 10)
         {
            _loc2_ = 35;
            _loc3_ = 16;
         }
         else if(param1 >= 10)
         {
            _loc2_ = -10;
            _loc3_ = 16;
         }
         if(Bo<PERSON>an(this._numberSprite) && contains(this._numberSprite))
         {
            removeChild(this._numberSprite);
            this._numberSprite = null;
         }
         this._numberSprite = new FloatingNumber();
         this._numberSprite.addFloatingNumberAnimation("anum",param1,_loc2_,_loc3_,55);
         this.addChild(this._numberSprite);
      }
   }
}

