package util
{
   public class UString
   {
      
      public function UString()
      {
         super();
      }
      
      public static function ddb416368e17ec1e(param1:Array) : String
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:String = null;
         if(param1 == null)
         {
            return null;
         }
         _loc3_ = int(param1.length);
         _loc4_ = "";
         _loc2_ = 0;
         while(_loc2_ < _loc3_)
         {
            _loc4_ += String.fromCharCode(param1[_loc2_]);
            _loc2_++;
         }
         return _loc4_;
      }
   }
}

