package actors.menu
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class GameComplete extends MovieClip
   {
      
      public var backTown:SimpleButton;
      
      public function GameComplete()
      {
         super();
         this.addEventListener(Event.ADDED_TO_STAGE,this.added,false,0,true);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.removed,false,0,true);
      }
      
      private function added(param1:*) : void
      {
         this.removeEventListener(Event.ADDED_TO_STAGE,this.added);
         this.addFrameScript(465,this.backGame);
      }
      
      private function backGame() : void
      {
         this.stop();
         this.backTown.addEventListener(MouseEvent.CLICK,this.onBackTownHandler);
      }
      
      private function onBackTownHandler(param1:MouseEvent) : void
      {
         this.removeChild(this.backTown);
         stage.focus = null;
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         ThreeKingdoms._instance._memory._neeUI = false;
         ThreeKingdoms._instance._memory.setMemory();
         ThreeKingdoms._instance.initGame();
      }
      
      private function removed(param1:*) : void
      {
         this.removeEventListener(Event.REMOVED_FROM_STAGE,this.removed);
         this.backTown.removeEventListener(MouseEvent.CLICK,this.onBackTownHandler);
      }
   }
}

