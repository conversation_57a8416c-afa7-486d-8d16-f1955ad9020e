package base
{
   import actors.Enemy;
   import actors.Hero;
   import actors.pets.Pet;
   import com.greensock.TweenMax;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.display.Stage;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import util.GameUtility;
   import util.Input;
   
   public class GameWorld extends Sprite
   {
      
      private var _heroStop:Boolean = false;
      
      private var _timercount:int = 0;
      
      public var _stage:Stage;
      
      public var _eventManager:EventDispatcher = new EventDispatcher();
      
      public var _heroes:Vector.<Object> = new Vector.<Object>();
      
      public var _enemies:Vector.<Object> = new Vector.<Object>();
      
      public var _walls:Vector.<Object> = new Vector.<Object>();
      
      public var _bullets:Vector.<Object> = new Vector.<Object>();
      
      public var _floors:Vector.<Object> = new Vector.<Object>();
      
      public var _obstacles:Vector.<Object> = new Vector.<Object>();
      
      public var _sendEnemyPoint:Vector.<Object> = new Vector.<Object>();
      
      public var _missles:Vector.<Object> = new Vector.<Object>();
      
      public var _pets:Vector.<Object> = new Vector.<Object>();
      
      public var _input:Input;
      
      public var _otherList:Array = [];
      
      public var _golds:Array = [];
      
      public var _isPause:Boolean;
      
      public function GameWorld(param1:Stage)
      {
         super();
         this._stage = param1;
         this._input = new Input(this._stage);
      }
      
      public function addSendEnemyPoint(param1:*) : void
      {
         this._sendEnemyPoint.push(param1);
      }
      
      public function addHero(param1:DisplayObject) : void
      {
         this._heroes.push(param1);
      }
      
      public function addEnemy(param1:DisplayObject) : void
      {
         this._enemies.push(param1);
      }
      
      public function addPet(param1:DisplayObject) : void
      {
         this._pets.push(param1);
      }
      
      public function addWall(param1:DisplayObject) : void
      {
         this._walls.push(param1);
      }
      
      public function addBullet(param1:DisplayObject) : void
      {
         this._bullets.push(param1);
      }
      
      public function addMissle(param1:DisplayObject) : void
      {
         this._missles.push(param1);
      }
      
      public function start() : void
      {
         this._stage.addEventListener(Event.ENTER_FRAME,this.onEnterFrameHandler);
         this._stage.addEventListener(Event.ENTER_FRAME,this.onEnterFrameHandler1);
         this._stage.addEventListener(Event.DEACTIVATE,this.onDeactiveHandler);
         this._stage.addEventListener(Event.ACTIVATE,this.onActiveHandler);
         this._isPause = false;
      }
      
      private function onEnterFrameHandler1(param1:Event) : void
      {
         if(ThreeKingdoms._instance._gameInfo)
         {
            if(ThreeKingdoms._instance._role1 || ThreeKingdoms._instance._role2 || Boolean(ThreeKingdoms._instance._role3))
            {
               ThreeKingdoms._instance._gameInfo.update();
            }
            if(this._timercount++ >= 30)
            {
               GameUtility.GC();
               this._timercount = 0;
            }
         }
      }
      
      private function onActiveHandler(param1:Event) : void
      {
         this._input = new Input(this._stage);
         this._heroStop = false;
      }
      
      private function onDeactiveHandler(param1:Event) : void
      {
         this._input = new Input(this._stage);
         this._heroStop = true;
      }
      
      public function pauseGame() : void
      {
         var _loc1_:int = 0;
         var _loc2_:* = undefined;
         var _loc3_:int = 0;
         var _loc4_:* = undefined;
         var _loc5_:int = 0;
         var _loc6_:Pet = null;
         if(this._stage.hasEventListener(Event.ENTER_FRAME))
         {
            this._stage.removeEventListener(Event.ENTER_FRAME,this.onEnterFrameHandler);
            TweenMax.pauseAll();
         }
         if(this._heroes.length >= 0)
         {
            _loc1_ = 0;
            while(_loc1_ < this._heroes.length)
            {
               _loc2_ = this._heroes[_loc1_] as Hero;
               GameUtility.stopAllChildren(_loc2_);
               _loc1_++;
            }
         }
         if(this._enemies.length >= 0)
         {
            _loc3_ = 0;
            while(_loc3_ < this._enemies.length)
            {
               _loc4_ = this._enemies[_loc3_] as Enemy;
               GameUtility.stopAllChildren(_loc4_);
               _loc3_++;
            }
         }
         if(this._pets.length >= 0)
         {
            _loc5_ = 0;
            while(_loc5_ < this._pets.length)
            {
               _loc6_ = this._pets[_loc5_] as Pet;
               GameUtility.stopAllChildren(_loc6_);
               _loc5_++;
            }
         }
         this._isPause = true;
      }
      
      public function continueGame() : void
      {
         var _loc1_:int = 0;
         var _loc2_:* = undefined;
         var _loc3_:int = 0;
         var _loc4_:Pet = null;
         var _loc5_:int = 0;
         var _loc6_:* = undefined;
         TweenMax.resumeAll();
         this._stage.addEventListener(Event.ENTER_FRAME,this.onEnterFrameHandler);
         if(this._heroes.length >= 0)
         {
            _loc1_ = 0;
            while(_loc1_ < this._heroes.length)
            {
               _loc2_ = this._heroes[_loc1_] as Hero;
               GameUtility.startAllChildren(_loc2_);
               _loc1_++;
            }
         }
         if(this._pets.length >= 0)
         {
            _loc3_ = 0;
            while(_loc3_ < this._pets.length)
            {
               _loc4_ = this._pets[_loc3_] as Pet;
               GameUtility.startAllChildren(_loc4_);
               _loc3_++;
            }
         }
         if(this._enemies.length >= 0)
         {
            _loc5_ = 0;
            while(_loc5_ < this._enemies.length)
            {
               _loc6_ = this._enemies[_loc5_] as Enemy;
               GameUtility.startAllChildren(_loc6_);
               _loc5_++;
            }
         }
         this._isPause = false;
      }
      
      public function stop() : void
      {
         if(this._stage.hasEventListener(Event.ENTER_FRAME))
         {
            this._stage.removeEventListener(Event.ENTER_FRAME,this.onEnterFrameHandler);
            this._stage.removeEventListener(Event.ENTER_FRAME,this.onEnterFrameHandler1);
            TweenMax.pauseAll();
         }
      }
      
      private function onEnterFrameHandler(param1:Event) : void
      {
         var _loc2_:int = 0;
         while(_loc2_ < this._heroes.length)
         {
            this._heroes[_loc2_].update();
            _loc2_++;
         }
         var _loc3_:int = 0;
         while(_loc3_ < this._enemies.length)
         {
            this._enemies[_loc3_].update();
            _loc3_++;
         }
         var _loc4_:int = 0;
         while(_loc4_ < this._otherList.length)
         {
            this._otherList[_loc4_].update();
            _loc4_++;
         }
         var _loc5_:int = 0;
         while(_loc5_ < this._golds.length)
         {
            this._golds[_loc5_].update();
            _loc5_++;
         }
         var _loc6_:int = 0;
         while(_loc6_ < this._missles.length)
         {
            this._missles[_loc6_].update();
            _loc6_++;
         }
         var _loc7_:int = 0;
         while(_loc7_ < this._pets.length)
         {
            this._pets[_loc7_].update();
            _loc7_++;
         }
         if(ThreeKingdoms._instance._town)
         {
            ThreeKingdoms._instance._town.update();
         }
         if(ThreeKingdoms._instance._viewController)
         {
            ThreeKingdoms._instance._viewController.update();
         }
         if(!this._heroStop)
         {
            Input.update();
         }
      }
      
      public function destroy() : void
      {
         this._eventManager = new EventDispatcher();
         this._heroes = new Vector.<Object>();
         this._pets = new Vector.<Object>();
         this._enemies = new Vector.<Object>();
         this._walls = new Vector.<Object>();
         this._bullets = new Vector.<Object>();
         this._floors = new Vector.<Object>();
         this._obstacles = new Vector.<Object>();
         this._pets = new Vector.<Object>();
         this._missles = new Vector.<Object>();
         this._sendEnemyPoint = new Vector.<Object>();
      }
   }
}

