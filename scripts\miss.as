package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol592")]
   public dynamic class miss extends MovieClip
   {
      
      public function miss()
      {
         super();
         addFrameScript(23,this.frame24);
      }
      
      internal function frame24() : *
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
   }
}

