package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy20 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy20(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 400;
         this._object._alertRange = 400;
         this._object._attackRange = 200;
         this._currentHealthPoint = 11000;
         this._totalHealthPoint = 11000;
         this._attackProbablity = 72;
         this._goldPrice = 210;
         this._resistance = 150;
         this._experience = 350;
         this._walkSpeed = 3.3;
         this._probability = 0.2;
         if(ThreeKingdoms._instance._currentLevel == 7)
         {
            this._currentHealthPoint = 25000;
            this._totalHealthPoint = 25000;
            this._resistance = 138;
            this._experience = 1000;
            this._goldPrice = 600;
            this._probability = 0.3;
            this._fallEquipmentsList = [{
               "id":15,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":16,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":17,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":18,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":78,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":79,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":80,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":65,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":81,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":41,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":42,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":43,
               "qualityID":[2],
               "type":"equipment"
            },{
               "id":44,
               "qualityID":[2],
               "type":"equipment"
            }];
            this._attackBackInfomationDictionary["攻击1"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[2,2],
               "attackInterval":4,
               "attackPower":273 + int(Math.random() * 59),
               "attackType":"physical"
            };
            this._attackBackInfomationDictionary["攻击2"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[2,2],
               "attackInterval":4,
               "attackPower":273 + int(Math.random() * 59),
               "attackType":"physical"
            };
         }
         else if(ThreeKingdoms._instance._currentLevel == 8)
         {
            this._fallEquipmentsList = [{
               "id":15,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":16,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":17,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":18,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":78,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":79,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":80,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":65,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":81,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":41,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":42,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":43,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":44,
               "qualityID":[1],
               "type":"equipment"
            }];
            this._attackBackInfomationDictionary["攻击1"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[2,2],
               "attackInterval":4,
               "attackPower":285 + int(Math.random() * 73),
               "attackType":"physical"
            };
            this._attackBackInfomationDictionary["攻击2"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[2,2],
               "attackInterval":4,
               "attackPower":285 + int(Math.random() * 73),
               "attackType":"physical"
            };
         }
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(this._curAttackTarget)
         {
            if(GameUtility.getRandomNumber(20))
            {
               wait();
            }
            else if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) > 100 && GameUtility.getDistance(this,this._curAttackTarget) < 200)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 150 + Math.round(Math.random() * 90);
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) <= 100)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 150 + Math.round(Math.random() * 90);
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 120 + Math.round(Math.random() * 60);
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
            }
            else
            {
               moveTowardHero();
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(27);
         newAttackID();
      }
      
      override public function realseSkill2() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         setYourDaddysTime(32);
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2";
      }
   }
}

