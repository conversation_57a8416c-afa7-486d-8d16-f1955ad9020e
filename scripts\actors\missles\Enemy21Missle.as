package actors.missles
{
   import actors.Enemy;
   import actors.Hero;
   import actors.Missle;
   import flash.geom.Point;
   import util.GameUtility;
   import util.HitTest;
   
   public class Enemy21Missle extends Missle
   {
      
      public function Enemy21Missle(param1:Enemy = null, param2:Hero = null)
      {
         super(param1,param2);
      }
      
      override public function update() : void
      {
         var _loc1_:Point = null;
         if(this._hero)
         {
            _loc1_ = GameUtility.getNextPoint(this._enemy,this._hero);
            this.x += _loc1_.x * _velocity;
            this.y += _loc1_.y * _velocity;
            this._velocity += 2;
         }
         else
         {
            this.destroy();
         }
         if(this._velocity >= 20)
         {
            this._velocity = 20;
         }
         if(this._hero)
         {
            this.detectCollision();
         }
         ++_counter;
         if(_counter > 300)
         {
            this.destroy();
            _counter = 0;
         }
      }
      
      override public function detectCollision() : void
      {
         var _loc1_:Object = null;
         if(this._hero.isDead())
         {
            return;
         }
         if(ThreeKingdoms._instance._protectedProperty.getProperty(_hero,"_whosYourDaddy"))
         {
            return;
         }
         if(HitTest.complexHitTestObject(this,this._hero))
         {
            _loc1_ = this._enemy._attackBackInfomationDictionary[_enemy._lastHit];
            _hero.afterAttack(this._enemy,_loc1_);
            this.destroy();
         }
      }
   }
}

