package actors.enemies
{
   import actors.Enemy;
   import util.UString;
   
   public class Enemy7 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy7(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._walkSpeed = 3.2;
         this._object._maxPatrolView = 780;
         this._object._alertRange = 780;
         this._object._attackRange = 200;
         this._currentHealthPoint = 550;
         this._totalHealthPoint = 550;
         this._attackProbablity = 40;
         this._resistance = 26;
         this._experience = 120;
         this._probability = 0.3;
         this._goldPrice = 30 + Math.round(Math.random() * 20);
         this._fallEquipmentsList = [{
            "id":1,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":62,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":27,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":8,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":9,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":34,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":35,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":71,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":72,
            "qualityID":[0,1,2],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":60 + Math.random() * 10,
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
   }
}

