package actors.pack
{
   import actors.Hero;
   import actors.equipments.Equipment;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import util.GameUtility;
   
   public class PackageContent extends Sprite
   {
      
      private var _showObj:ShowObj;
      
      private var _owner:uint;
      
      private var _contextMenu:ContextMenu;
      
      public function PackageContent()
      {
         super();
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      private function onAddedToStageHandler(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
      }
      
      public function setShowObj(param1:*, param2:uint) : void
      {
         if(param1 != null)
         {
            this._showObj = new ShowObj(param1);
            this.addChild(this._showObj);
            this._owner = param2;
            this.addEventListener(MouseEvent.CLICK,this.onClickHandler);
            if(this._showObj.getEquipmentObject()._type == "lantern" || this._showObj.getEquipmentObject()._type == "valentine")
            {
               this._showObj.doubleClickEnabled = true;
               this._showObj.mouseChildren = false;
               this._showObj.addEventListener(MouseEvent.DOUBLE_CLICK,this.onDoubleHandler);
            }
         }
      }
      
      private function onDoubleHandler(param1:MouseEvent) : void
      {
         var _loc2_:MovieClip = null;
         var _loc3_:Array = null;
         var _loc4_:int = 0;
         var _loc5_:PackageContent = null;
         if(this._showObj.getEquipmentObject()._type == "lantern")
         {
            if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._equipmentsVector.length >= 30)
            {
               _loc2_ = GameUtility.getObject("GameMsg");
               _loc2_["msg"]["txt"].text = "背包已满,请卖出部分装备后。重新开装备";
               ThreeKingdoms._instance.addChild(_loc2_);
               _loc2_.x = 480;
               _loc2_.y = 250;
            }
            else
            {
               this._showObj.removeEventListener(MouseEvent.DOUBLE_CLICK,this.onDoubleHandler);
               _loc3_ = Package(this.parent.parent).getPackageContentArray();
               _loc4_ = 0;
               while(_loc4_ < _loc3_.length)
               {
                  _loc5_ = _loc3_[_loc4_] as PackageContent;
                  if(_loc5_._contextMenu)
                  {
                     _loc5_.removeContextMenu();
                  }
                  _loc4_++;
               }
               if(this._showObj)
               {
                  this._showObj.removePanel();
               }
               this.removeItem();
               this.removeContextMenu();
               this._showObj = null;
               if(this.parent.name == "special")
               {
                  Package(this.parent.parent).reDrawGrid(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._specialVector,"special");
               }
               this.parent.removeChild(this);
               this.getSurprised();
            }
         }
         else
         {
            Package(this.parent.parent).mcChange.visible = true;
            Package(this.parent.parent).addChild(Package(this.parent.parent).mcChange);
            Package(this.parent.parent).mcChange.addEventListener(MouseEvent.CLICK,this.changeClickHandler);
         }
      }
      
      private function changeClickHandler(param1:MouseEvent) : void
      {
         var _loc2_:MovieClip = GameUtility.getObject("GameMsg");
         _loc2_.x = 480;
         _loc2_.y = 250;
         switch(param1.target.name)
         {
            case "btnClose":
               Package(this.parent.parent).mcChange.visible = false;
               break;
            case "btn1":
               if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._roseVector.length >= 1)
               {
                  if(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverAttackPower() >= 88)
                  {
                     _loc2_["msg"]["txt"].text = "永久攻击属性已加满，不可兑换";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
                  else
                  {
                     Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setForeverAttackPower(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverAttackPower() + 1);
                     this.removeItems(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._roseVector,1);
                     _loc2_["msg"]["txt"].text = "兑换成功，攻击永久增加1！";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
               }
               else
               {
                  _loc2_["msg"]["txt"].text = "玫瑰花不够了，再去打一些！";
                  ThreeKingdoms._instance.addChild(_loc2_);
               }
               break;
            case "btn2":
               if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._roseVector.length >= 20)
               {
                  if(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverAttackPower() >= 88)
                  {
                     _loc2_["msg"]["txt"].text = "永久攻击属性已加满，不可兑换";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
                  else
                  {
                     Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setForeverAttackPower(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverAttackPower() + 22);
                     this.removeItems(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._roseVector,20);
                     _loc2_["msg"]["txt"].text = "兑换成功，攻击永久增加22！";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
               }
               else
               {
                  _loc2_["msg"]["txt"].text = "玫瑰花不够了，再去打一些！";
                  ThreeKingdoms._instance.addChild(_loc2_);
               }
               break;
            case "btn3":
               if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._roseVector.length >= 6)
               {
                  if(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverCrit() >= 0.05)
                  {
                     _loc2_["msg"]["txt"].text = "永久暴击属性已加满，不可兑换";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
                  else
                  {
                     Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setForeverCirt(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverCrit() + 0.01);
                     this.removeItems(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._roseVector,6);
                     _loc2_["msg"]["txt"].text = "兑换成功，暴击永久增加1！";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
               }
               else
               {
                  _loc2_["msg"]["txt"].text = "玫瑰花不够了，再去打一些！";
                  ThreeKingdoms._instance.addChild(_loc2_);
               }
               break;
            case "btn4":
               if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._roseVector.length >= 25)
               {
                  if(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverCrit() >= 0.05)
                  {
                     _loc2_["msg"]["txt"].text = "永久暴击属性已加满，不可兑换";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
                  else
                  {
                     Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setForeverCirt(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverCrit() + 0.05);
                     this.removeItems(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._roseVector,25);
                     _loc2_["msg"]["txt"].text = "兑换成功，暴击永久增加 5 ！";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
               }
               else
               {
                  _loc2_["msg"]["txt"].text = "玫瑰花不够了，再去打一些！";
                  ThreeKingdoms._instance.addChild(_loc2_);
               }
               break;
            case "btn5":
               if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._chocolVector.length >= 3)
               {
                  if(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverResistance() >= 50)
                  {
                     _loc2_["msg"]["txt"].text = "永久防御属性已加满，不可兑换";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
                  else
                  {
                     Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setForeverResistance(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverResistance() + 1);
                     this.removeItems(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._chocolVector,3);
                     _loc2_["msg"]["txt"].text = "兑换成功，防御永久增加 1 ！";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
               }
               else
               {
                  _loc2_["msg"]["txt"].text = "巧克力不够了，再去打一些！";
                  ThreeKingdoms._instance.addChild(_loc2_);
               }
               break;
            case "btn6":
               if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._chocolVector.length >= 25)
               {
                  if(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverResistance() >= 50)
                  {
                     _loc2_["msg"]["txt"].text = "永久防御属性已加满，不可兑换";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
                  else
                  {
                     Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setForeverResistance(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverResistance() + 10);
                     this.removeItems(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._chocolVector,25);
                     _loc2_["msg"]["txt"].text = "兑换成功，防御永久增加 10 ！";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
               }
               else
               {
                  _loc2_["msg"]["txt"].text = "巧克力不够了，再去打一些！";
                  ThreeKingdoms._instance.addChild(_loc2_);
               }
               break;
            case "btn7":
               if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._chocolVector.length >= 6)
               {
                  if(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverMiss() >= 0.05)
                  {
                     _loc2_["msg"]["txt"].text = "永久闪避属性已加满，不可兑换";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
                  else
                  {
                     Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setForeverMiss(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverMiss() + 0.01);
                     this.removeItems(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._chocolVector,6);
                     _loc2_["msg"]["txt"].text = "兑换成功，闪避永久增加1！";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
               }
               else
               {
                  _loc2_["msg"]["txt"].text = "巧克力不够了，再去打一些！";
                  ThreeKingdoms._instance.addChild(_loc2_);
               }
               break;
            case "btn8":
               if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._chocolVector.length >= 25)
               {
                  if(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverMiss() >= 0.05)
                  {
                     _loc2_["msg"]["txt"].text = "永久闪避属性已加满，不可兑换";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
                  else
                  {
                     Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setForeverMiss(Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.getForeverMiss() + 0.05);
                     this.removeItems(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._chocolVector,25);
                     _loc2_["msg"]["txt"].text = "兑换成功，闪避永久增加 5！";
                     ThreeKingdoms._instance.addChild(_loc2_);
                  }
               }
               else
               {
                  _loc2_["msg"]["txt"].text = "巧克力不够了，再去打一些！";
                  ThreeKingdoms._instance.addChild(_loc2_);
               }
         }
      }
      
      private function getSurprised() : void
      {
         var _loc2_:int = 0;
         var _loc3_:String = null;
         var _loc4_:String = null;
         var _loc5_:Equipment = null;
         var _loc1_:MovieClip = GameUtility.getObject("GameMsg");
         _loc1_.x = 480;
         _loc1_.y = 250;
         if(GameUtility.getRandomNumber(10))
         {
            _loc2_ = 0;
            _loc3_ = "";
            _loc4_ = "";
            ThreeKingdoms._instance._equipments.init();
            if(GameUtility.getRandomNumber(2))
            {
               _loc2_ = 1;
               _loc3_ = "神器";
            }
            else
            {
               _loc2_ = 0;
               _loc3_ = "仙器";
            }
            switch(this._owner)
            {
               case 1:
                  _loc5_ = ThreeKingdoms._instance._equipments.getEquipmentByID(93) as Equipment;
                  _loc4_ = "干将莫邪";
                  break;
               case 2:
                  _loc5_ = ThreeKingdoms._instance._equipments.getEquipmentByID(94) as Equipment;
                  _loc4_ = "断龙绝麟";
                  break;
               case 3:
                  _loc5_ = ThreeKingdoms._instance._equipments.getEquipmentByID(68) as Equipment;
                  _loc4_ = "神威离火";
            }
            _loc5_.index = _loc2_;
            Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._equipmentsVector.push(_loc5_);
            _loc1_["msg"]["txt"].text = "获得武器: " + _loc4_ + "·" + _loc3_ + " 请到背包查看!";
            ThreeKingdoms._instance.addChild(_loc1_);
         }
         else
         {
            _loc1_["msg"]["txt"].text = "不好意思空空如也~";
            ThreeKingdoms._instance.addChild(_loc1_);
         }
         ThreeKingdoms._instance._memory._neeUI = false;
         ThreeKingdoms._instance._memory.setMemory();
      }
      
      private function onClickHandler(param1:MouseEvent) : void
      {
         var _loc4_:PackageContent = null;
         var _loc2_:Array = Package(this.parent.parent).getPackageContentArray();
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_.length)
         {
            _loc4_ = _loc2_[_loc3_] as PackageContent;
            if(_loc4_._contextMenu)
            {
               _loc4_.removeContextMenu();
            }
            _loc3_++;
         }
         if(this._showObj)
         {
            this._showObj.removePanel();
         }
         this.removeContextMenu();
         this._contextMenu = GameUtility.getObject("actors.pack.ContextMenu");
         this._contextMenu.x = this.x + 25;
         this._contextMenu.y = this.y + 25;
         this.parent.addChild(this._contextMenu);
         this._contextMenu["btnEquip"].addEventListener(MouseEvent.CLICK,this.onEquipHandler);
         this._contextMenu["btnGift"].addEventListener(MouseEvent.CLICK,this.onGiftHandler);
         this._contextMenu["btnSell"].addEventListener(MouseEvent.CLICK,this.onSellHandler);
         if(this.parent is Sprite && Package(this.parent.parent)._isRepositoryPanel)
         {
            this._contextMenu["mcRepository"].gotoAndStop(2);
         }
         else if(this.parent is Sprite && Package(this.parent.parent)._isEquipmentsPanel)
         {
            this._contextMenu["mcRepository"].gotoAndStop(1);
         }
         this._contextMenu["mcRepository"].addEventListener(MouseEvent.CLICK,this.onClickRepositoryHandler);
         this._contextMenu.addEventListener(MouseEvent.ROLL_OUT,this.onRollOutHandler);
         if(ThreeKingdoms._instance._players == 1)
         {
            if(this.parent is Sprite && Package(this.parent.parent)._isSpecialPanel)
            {
               this._contextMenu.gotoAndStop(5);
               this._contextMenu["btnGift"].mouseEnabled = false;
               this._contextMenu["mcRepository"].mouseEnabled = false;
               this._contextMenu["mcRepository"].visible = false;
               this._contextMenu["btnGift"].visible = false;
               this._contextMenu["btnEquip"].mouseEnabled = false;
               this._contextMenu["btnEquip"].visible = false;
               this._contextMenu["btnGift"].removeEventListener(MouseEvent.CLICK,this.onGiftHandler);
               this._contextMenu["btnEquip"].removeEventListener(MouseEvent.CLICK,this.onEquipHandler);
               this._contextMenu["mcRepository"].removeEventListener(MouseEvent.CLICK,this.onClickRepositoryHandler);
            }
            else
            {
               this._contextMenu.gotoAndStop(1);
               this._contextMenu["btnGift"].mouseEnabled = false;
               this._contextMenu["btnGift"].visible = false;
               this._contextMenu["btnGift"].removeEventListener(MouseEvent.CLICK,this.onGiftHandler);
               if(ThreeKingdoms._instance["_role" + this._owner]._roleName != this._showObj.getEquipmentObject()._user && (this._showObj.getEquipmentObject()._type == "weapon" || this._showObj.getEquipmentObject()._type == "armor" || this._showObj.getEquipmentObject()._type == "suit" && this._showObj.getEquipmentObject()._id != 60 && this._showObj.getEquipmentObject()._id != 98 && this._showObj.getEquipmentObject()._id != 99 && this._showObj.getEquipmentObject()._id != 103 && this._showObj.getEquipmentObject()._id != 200 && this._showObj.getEquipmentObject()._id != 201))
               {
                  this._contextMenu.gotoAndStop(2);
                  this._contextMenu["btnGift"].visible = false;
                  this._contextMenu["btnEquip"].mouseEnabled = false;
                  this._contextMenu["btnEquip"].visible = false;
                  this._contextMenu["btnEquip"].removeEventListener(MouseEvent.CLICK,this.onEquipHandler);
               }
            }
         }
         if(ThreeKingdoms._instance._players == 2)
         {
            if(this.parent is Sprite && Package(this.parent.parent)._isSpecialPanel)
            {
               this._contextMenu.gotoAndStop(6);
               this._contextMenu["mcRepository"].mouseEnabled = false;
               this._contextMenu["mcRepository"].visible = false;
               this._contextMenu["btnEquip"].mouseEnabled = false;
               this._contextMenu["btnEquip"].visible = false;
               this._contextMenu["btnEquip"].removeEventListener(MouseEvent.CLICK,this.onEquipHandler);
               this._contextMenu["mcRepository"].removeEventListener(MouseEvent.CLICK,this.onClickRepositoryHandler);
            }
            else
            {
               this._contextMenu.gotoAndStop(3);
               if(ThreeKingdoms._instance["_role" + this._owner]._roleName != this._showObj.getEquipmentObject()._user && (this._showObj.getEquipmentObject()._type == "weapon" || this._showObj.getEquipmentObject()._type == "armor" || this._showObj.getEquipmentObject()._type == "suit" && this._showObj.getEquipmentObject()._id != 60 && this._showObj.getEquipmentObject()._id != 98 && this._showObj.getEquipmentObject()._id != 99 && this._showObj.getEquipmentObject()._id != 103 && this._showObj.getEquipmentObject()._id != 200 && this._showObj.getEquipmentObject()._id != 201))
               {
                  this._contextMenu.gotoAndStop(4);
                  this._contextMenu["btnEquip"].mouseEnabled = false;
                  this._contextMenu["btnEquip"].visible = false;
                  this._contextMenu["btnEquip"].removeEventListener(MouseEvent.CLICK,this.onEquipHandler);
               }
            }
         }
      }
      
      private function onClickRepositoryHandler(param1:MouseEvent) : void
      {
         if(param1.currentTarget.currentFrame == 2 && Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._equipmentsVector.length >= 30 || param1.currentTarget.currentFrame == 1 && Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._repositoryVector.length >= 30)
         {
            return;
         }
         this.removeContextMenu();
         this.removeItem();
         if(this.parent is Sprite && param1.currentTarget.currentFrame == 2 && Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._equipmentsVector.length <= 30)
         {
            Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._equipmentsVector.push(this._showObj.getEquipmentObject());
            Package(this.parent.parent).reDrawGrid(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._repositoryVector,"repository");
         }
         else if(this.parent is Sprite && param1.currentTarget.currentFrame == 1 && Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._repositoryVector.length <= 30)
         {
            Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._repositoryVector.push(this._showObj.getEquipmentObject());
            Package(this.parent.parent).reDrawGrid(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._equipmentsVector,"equipments");
         }
         this.parent.removeChild(this);
         this._showObj = null;
      }
      
      private function onRollOutHandler(param1:MouseEvent) : void
      {
         this.removeContextMenu();
      }
      
      private function removeContextMenu() : void
      {
         if(this.parent)
         {
            if(Boolean(this._contextMenu) && this.parent.contains(this._contextMenu))
            {
               this._contextMenu["btnEquip"].removeEventListener(MouseEvent.CLICK,this.onEquipHandler);
               this._contextMenu["btnGift"].removeEventListener(MouseEvent.CLICK,this.onGiftHandler);
               this._contextMenu["btnSell"].removeEventListener(MouseEvent.CLICK,this.onSellHandler);
               this._contextMenu["mcRepository"].removeEventListener(MouseEvent.CLICK,this.onClickRepositoryHandler);
               this._contextMenu.removeEventListener(MouseEvent.ROLL_OUT,this.onRollOutHandler);
               this.parent.removeChild(this._contextMenu);
            }
         }
      }
      
      private function removeItems(param1:Vector.<Object>, param2:int) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < param2)
         {
            _loc4_ = 0;
            while(_loc4_ < Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._specialVector.length)
            {
               if(param1.indexOf(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._specialVector[_loc4_]) != -1)
               {
                  Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._specialVector.splice(_loc4_,1);
                  _loc4_ = int(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._specialVector.length);
               }
               _loc4_++;
            }
            _loc3_++;
         }
         param1.splice(0,param2);
         Package(this.parent.parent).setInformationText();
         Package(this.parent.parent).mcChange.visible = false;
         Package(this.parent.parent).mcChange.removeEventListener(MouseEvent.CLICK,this.changeClickHandler);
         Package(this.parent.parent).reDrawGrid(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._specialVector,"special");
      }
      
      private function removeItem() : void
      {
         var _loc1_:Equipment = this._showObj.getEquipmentObject();
         this.removeEventListener(MouseEvent.CLICK,this.onClickHandler);
         var _loc2_:int = -1;
         if(this.parent is Sprite && this.parent.name == "equipments")
         {
            _loc2_ = int(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._equipmentsVector.indexOf(_loc1_));
            if(_loc2_ != -1)
            {
               Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._equipmentsVector.splice(_loc2_,1);
            }
         }
         else if(this.parent is Sprite && this.parent.name == "repository")
         {
            _loc2_ = int(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._repositoryVector.indexOf(_loc1_));
            if(_loc2_ != -1)
            {
               Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._repositoryVector.splice(_loc2_,1);
            }
         }
         else if(this.parent is Sprite && this.parent.name == "special")
         {
            _loc2_ = int(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._specialVector.indexOf(_loc1_));
            if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._petFoodVector.indexOf(_loc1_) != -1)
            {
               Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._petFoodVector.splice(0,1);
            }
            if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._roseVector.indexOf(_loc1_) != -1)
            {
               Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._roseVector.splice(0,1);
            }
            if(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._chocolVector.indexOf(_loc1_) != -1)
            {
               Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._chocolVector.splice(0,1);
            }
            if(_loc2_ != -1)
            {
               Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._specialVector.splice(_loc2_,1);
            }
         }
      }
      
      private function onSellHandler(param1:MouseEvent) : void
      {
         if(this.parent.parent is Package)
         {
            if(this._showObj.getEquipmentObject()._worth[this._showObj.getEquipmentObject().index] == null || this._showObj.getEquipmentObject()._worth[this._showObj.getEquipmentObject().index] == undefined)
            {
               Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setGold(0);
            }
            else if(Boolean(this._showObj.getEquipmentObject()._suitProperty) && Boolean(this._showObj.getEquipmentObject()._suitProperty.isNew))
            {
               Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setGold(Math.round((this._showObj.getEquipmentObject()._worth[this._showObj.getEquipmentObject().index] - 0.1) * 10));
            }
            else
            {
               Hero(ThreeKingdoms._instance["_role" + this._owner])._properties.setGold(this._showObj.getEquipmentObject()._worth[this._showObj.getEquipmentObject().index]);
            }
            Package(this.parent.parent).setInformationText();
         }
         this.removeContextMenu();
         this.removeItem();
         if(this.parent.name == "equipments")
         {
            Package(this.parent.parent).reDrawGrid(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._equipmentsVector,"equipments");
         }
         else if(this.parent.name == "repository")
         {
            Package(this.parent.parent).reDrawGrid(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._repositoryVector,"repository");
         }
         else if(this.parent.name == "special")
         {
            Package(this.parent.parent).reDrawGrid(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._specialVector,"special");
         }
         this.parent.removeChild(this);
         this._showObj = null;
      }
      
      private function onGiftHandler(param1:MouseEvent) : void
      {
         if(Boolean(ThreeKingdoms._instance._role2) && Boolean(ThreeKingdoms._instance._role1))
         {
            if(this._owner == 1)
            {
               if(this.parent.name == "special")
               {
                  ThreeKingdoms._instance._role2.getPlayer()._specialVector.push(this._showObj.getEquipmentObject());
               }
               else
               {
                  ThreeKingdoms._instance._role2.getPlayer()._equipmentsVector.push(this._showObj.getEquipmentObject());
               }
            }
            else if(this._owner == 2)
            {
               if(this.parent.name == "special")
               {
                  ThreeKingdoms._instance._role1.getPlayer()._specialVector.push(this._showObj.getEquipmentObject());
               }
               else
               {
                  ThreeKingdoms._instance._role1.getPlayer()._equipmentsVector.push(this._showObj.getEquipmentObject());
               }
            }
         }
         else if(Boolean(ThreeKingdoms._instance._role3) && Boolean(ThreeKingdoms._instance._role1))
         {
            if(this._owner == 1)
            {
               if(this.parent.name == "special")
               {
                  ThreeKingdoms._instance._role3.getPlayer()._specialVector.push(this._showObj.getEquipmentObject());
               }
               else
               {
                  ThreeKingdoms._instance._role3.getPlayer()._equipmentsVector.push(this._showObj.getEquipmentObject());
               }
            }
            else if(this._owner == 3)
            {
               if(this.parent.name == "special")
               {
                  ThreeKingdoms._instance._role1.getPlayer()._specialVector.push(this._showObj.getEquipmentObject());
               }
               else
               {
                  ThreeKingdoms._instance._role1.getPlayer()._equipmentsVector.push(this._showObj.getEquipmentObject());
               }
            }
         }
         else if(this._owner == 2)
         {
            if(this.parent.name == "special")
            {
               ThreeKingdoms._instance._role3.getPlayer()._specialVector.push(this._showObj.getEquipmentObject());
            }
            else
            {
               ThreeKingdoms._instance._role3.getPlayer()._equipmentsVector.push(this._showObj.getEquipmentObject());
            }
         }
         else if(this._owner == 3)
         {
            if(this.parent.name == "special")
            {
               ThreeKingdoms._instance._role2.getPlayer()._specialVector.push(this._showObj.getEquipmentObject());
            }
            else
            {
               ThreeKingdoms._instance._role2.getPlayer()._equipmentsVector.push(this._showObj.getEquipmentObject());
            }
         }
         this.removeContextMenu();
         this.removeItem();
         if(this.parent.name == "special")
         {
            Package(this.parent.parent).reDrawGrid(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._specialVector,"special");
         }
         else
         {
            Package(this.parent.parent).reDrawGrid(Hero(ThreeKingdoms._instance["_role" + this._owner]).getPlayer()._equipmentsVector,"equipments");
         }
         this.parent.removeChild(this);
         this._showObj = null;
      }
      
      private function onEquipHandler(param1:MouseEvent) : void
      {
         this.removeItem();
         this.useEquipment();
         this.removeContextMenu();
         this._showObj = null;
      }
      
      private function useEquipment() : void
      {
         var _loc1_:Equipment = null;
         if(this.parent.parent is Package)
         {
            _loc1_ = this._showObj.getEquipmentObject();
            Package(this.parent.parent).equip(_loc1_._id,_loc1_);
         }
         this.parent.removeChild(this);
      }
   }
}

