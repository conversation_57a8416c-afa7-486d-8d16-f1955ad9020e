package actors.info
{
   import actors.Hero;
   import actors.menu.SettingMenu;
   import actors.pack.Package;
   import actors.pets.*;
   import actors.roles.*;
   import actors.skill.SkillPanel;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import util.GameUtility;
   
   public class RoleInfo extends MovieClip
   {
      
      public var head:MovieClip;
      
      public var mcSkillIcon:MovieClip;
      
      public var bg:MovieClip;
      
      public var btnPackage:SimpleButton;
      
      public var btnSetting:SimpleButton;
      
      public var btnSkill:SimpleButton;
      
      public var healthPointBar:MovieClip;
      
      public var manaPointBar:MovieClip;
      
      public var experiencePointBar:MovieClip;
      
      public var uniquePointBar:MovieClip;
      
      public var mcUnique:MovieClip;
      
      public var txtHealthPoint:TextField;
      
      public var txtManaPoint:TextField;
      
      public var txtExperiencePoint:TextField;
      
      public var txtLevel:TextField;
      
      public var petInfo:MovieClip;
      
      public var _uniquePointObject:Object = {"uniquePoint":0};
      
      public var _roleNumber:uint;
      
      private var _hero:Hero;
      
      private var _count:Number = 0;
      
      private var _package:Package;
      
      public function RoleInfo(param1:uint = 1)
      {
         super();
         this._roleNumber = param1;
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         var _loc2_:Hero = ThreeKingdoms._instance["_role" + this._roleNumber];
         this._hero = _loc2_;
         if(this._roleNumber == 2)
         {
            this.setPosition();
         }
         this.setHead();
         this.btnPackage.addEventListener(MouseEvent.CLICK,this.onClickPackageHandler);
         this.btnSetting.addEventListener(MouseEvent.CLICK,this.onClickSettingHandler);
         this.btnSkill.addEventListener(MouseEvent.CLICK,this.onClickSkillHandler);
         var _loc3_:uint = 5;
         while(_loc3_-- > 0)
         {
            this.mcSkillIcon["skillIcon" + (_loc3_ + 1)].skillName.visible = false;
            this.mcSkillIcon["skillIcon" + (_loc3_ + 1)].addEventListener(MouseEvent.MOUSE_OVER,this.onMouseOverHandler);
            this.mcSkillIcon["skillIcon" + (_loc3_ + 1)].addEventListener(MouseEvent.MOUSE_OUT,this.onMouseOutHandler);
         }
      }
      
      private function onMouseOutHandler(param1:MouseEvent) : void
      {
         MovieClip(param1.currentTarget).skillName.visible = false;
      }
      
      private function onMouseOverHandler(param1:MouseEvent) : void
      {
         MovieClip(param1.currentTarget).skillName.visible = true;
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         this.btnPackage.removeEventListener(MouseEvent.CLICK,this.onClickPackageHandler);
         this.btnSetting.removeEventListener(MouseEvent.CLICK,this.onClickSettingHandler);
         this.btnSkill.removeEventListener(MouseEvent.CLICK,this.onClickSkillHandler);
      }
      
      private function onAddToStageHandler(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onAddToStageHandler);
      }
      
      private function onClickSkillHandler(param1:MouseEvent) : void
      {
         var _loc2_:SkillPanel = GameUtility.getObject("actors.skill.SkillPanel") as SkillPanel;
         _loc2_._hero = this._hero;
         _loc2_.x = -40;
         _loc2_.y = -35;
         this.parent.addChild(_loc2_);
      }
      
      private function onClickSettingHandler(param1:MouseEvent) : void
      {
         var _loc2_:* = GameUtility.getObject("actors.menu.SettingMenu") as SettingMenu;
         this.parent.addChild(_loc2_);
      }
      
      private function onClickPackageHandler(param1:MouseEvent) : void
      {
         this._package = GameUtility.getObject("actors.pack.Package") as Package;
         this._package.setPackage(this._roleNumber);
         this.parent.addChild(this._package);
      }
      
      private function setHead() : void
      {
         if(this._hero is Role1)
         {
            this.head.gotoAndStop(1);
            this.mcUnique.gotoAndStop(1);
            this.mcSkillIcon.gotoAndStop(1);
         }
         else if(this._hero is Role2)
         {
            this.head.gotoAndStop(2);
            this.mcUnique.gotoAndStop(2);
            this.mcSkillIcon.gotoAndStop(2);
         }
         else
         {
            this.head.gotoAndStop(3);
            this.mcUnique.gotoAndStop(3);
            this.mcSkillIcon.gotoAndStop(3);
         }
         this.head.face.gotoAndStop(1);
      }
      
      private function setPosition() : void
      {
      }
      
      private function updatePetIcon() : void
      {
         if(this._hero.pet)
         {
            if(this._hero.pet is Pet1)
            {
               this.petInfo.mcPetHead.gotoAndStop(1);
            }
            else if(this._hero.pet is Pet2)
            {
               this.petInfo.mcPetHead.gotoAndStop(2);
            }
            else if(this._hero.pet is Pet3)
            {
               this.petInfo.mcPetHead.gotoAndStop(4);
            }
            else if(this._hero.pet is Pet4)
            {
               this.petInfo.mcPetHead.gotoAndStop(3);
            }
            else if(this._hero.pet is Pet5)
            {
               this.petInfo.mcPetHead.gotoAndStop(5);
            }
            this.petInfo.txtPetLevel.selectable = false;
            this.petInfo.txtPetLevel.text = "" + this._hero.pet._properties.getLevel();
            this.petInfo.petHealthPointBar.gotoAndStop(Math.round(100 * (1 - this._hero.pet._properties.getCurrentHealthPoint() / this._hero.pet._properties.getTotalHealthPoint())) + 1);
            this.petInfo.petManaPointBar.gotoAndStop(Math.round(100 * (1 - this._hero.pet._properties.getCurrentManaPoint() / this._hero.pet._properties.getTotalManaPoint())) + 1);
            this.petInfo.petExperiencePointBar.gotoAndStop(Math.round(100 * (1 - this._hero.pet._properties.getCurrentExperience() / this._hero.pet._properties.getTotalExperience())) + 1);
         }
      }
      
      public function update() : void
      {
         this.txtHealthPoint.text = this._hero._properties.getCurrentHealthPoint() + "/" + this._hero._properties.getTotalHealthPoint();
         this.healthPointBar.gotoAndStop(Math.round(100 * (1 - this._hero._properties.getCurrentHealthPoint() / this._hero._properties.getTotalHealthPoint())) + 1);
         if(this.isUniqueReady())
         {
            this.head.face.gotoAndStop(2);
         }
         else if(this.healthPointBar.currentFrame >= 85)
         {
            this.head.face.gotoAndStop(3);
         }
         else
         {
            this.head.face.gotoAndStop(1);
         }
         this.txtManaPoint.text = this._hero._properties.getCurrentManaPoint() + "/" + this._hero._properties.getTotalManaPoint();
         this.manaPointBar.gotoAndStop(Math.round(100 * (1 - this._hero._properties.getCurrentManaPoint() / this._hero._properties.getTotalManaPoint())) + 1);
         this.txtExperiencePoint.text = this._hero._properties.getCurrentExperience() + "/" + this._hero._properties.getTotalExperience();
         this.txtExperiencePoint.selectable = false;
         this.txtManaPoint.selectable = false;
         this.txtHealthPoint.selectable = false;
         this.experiencePointBar.gotoAndStop(Math.round(100 * (1 - this._hero._properties.getCurrentExperience() / this._hero._properties.getTotalExperience())) + 1);
         if(this._uniquePointObject.uniquePoint <= 0)
         {
            this.uniquePointBar.gotoAndStop(100);
         }
         else if(this._uniquePointObject.uniquePoint >= 100)
         {
            this.uniquePointBar.gotoAndStop(1);
         }
         else
         {
            this.uniquePointBar.gotoAndStop(Math.abs(100 - this._uniquePointObject.uniquePoint) + 1);
         }
         this.txtLevel.text = this._hero._properties.getLevel() + "";
         this.txtLevel.selectable = false;
         this.setSkillIcon();
         this.updateUnique();
         if(this._package)
         {
            this._package.update();
         }
         this.updatePetIcon();
         if(!this._hero.pet || this._hero.pet.isDead())
         {
            this.petInfo.visible = false;
         }
         else
         {
            this.petInfo.visible = true;
         }
      }
      
      private function updateUnique() : void
      {
         if(this._uniquePointObject.uniquePoint >= 100)
         {
            return;
         }
         if(this._count >= 30)
         {
            if(this._uniquePointObject.uniquePoint-- <= 0)
            {
               this._uniquePointObject.uniquePoint = 0;
            }
            this._count = 0;
         }
         ++this._count;
      }
      
      private function setSkillIcon() : void
      {
         var _loc1_:uint = this._hero.getPlayer()._learnSkill.length;
         while(_loc1_-- > 0)
         {
            if(this._hero.getPlayer()._learnSkill[_loc1_] > 0)
            {
               this.mcSkillIcon["skillIcon" + (_loc1_ + 1)].gotoAndStop(2);
            }
            else
            {
               this.mcSkillIcon["skillIcon" + (_loc1_ + 1)].gotoAndStop(1);
            }
         }
      }
      
      public function addUnique(param1:*) : void
      {
         var _loc2_:int = 0;
         var _loc3_:Hero = param1[1] as Hero;
         var _loc4_:int = 2;
         _loc2_ = Math.round(param1[0] / _loc3_._properties.getAttackPower()) * 1.2;
         if(!_loc3_._isUnique && _loc3_ == this._hero)
         {
            this._uniquePointObject.uniquePoint += _loc2_;
            if(this._uniquePointObject.uniquePoint >= 100)
            {
               this._uniquePointObject.uniquePoint = 100;
            }
         }
      }
      
      public function isUniqueReady() : Boolean
      {
         return this._uniquePointObject.uniquePoint >= 100;
      }
      
      public function isUniqueAlive() : Boolean
      {
         return this._uniquePointObject.uniquePoint > 0;
      }
      
      public function decreaseUniquePoint(param1:int) : void
      {
         this._uniquePointObject.uniquePoint -= param1;
         if(this._uniquePointObject.uniquePoint < 0)
         {
            this._uniquePointObject.uniquePoint = 0;
         }
      }
   }
}

