package actors.holidays
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public dynamic class ChildrenGiftIcon extends MovieClip
   {
      
      public function ChildrenGiftIcon()
      {
         super();
         if(!stage)
         {
            this.addEventListener(Event.ADDED_TO_STAGE,this.appInit);
         }
         else
         {
            this.appInit();
         }
      }
      
      public function appInit(param1:Event = null) : void
      {
         if(param1)
         {
            this.removeEventListener(Event.ADDED_TO_STAGE,this.appInit);
         }
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         this.buttonMode = true;
         this.addEventListener(MouseEvent.CLICK,this.onIconClick);
      }
      
      private function onIconClick(param1:MouseEvent) : void
      {
         var _loc2_:GetChildrenGift = new GetChildrenGift();
         _loc2_.x = 488.3;
         _loc2_.y = 298;
         ThreeKingdoms._instance._town.addChild(_loc2_);
      }
      
      private function onRemovedFromStageHandler(param1:Event) : void
      {
         var _loc2_:DisplayObject = null;
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         removeEventListener(MouseEvent.CLICK,this.onIconClick);
         this.stop();
         while(this.numChildren > 0)
         {
            _loc2_ = this.removeChildAt(0);
            _loc2_ = null;
         }
      }
   }
}

