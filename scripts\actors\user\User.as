package actors.user
{
   import actors.Hero;
   import actors.equipments.Equipment;
   import util.GameUtility;
   
   public class User
   {
      
      public static var _batterTimes:int;
      
      private var _suitFrame:uint;
      
      public var _controlPlayer:int = 0;
      
      public var _isHadSuit:Boolean = false;
      
      public var _isHideSuit:Boolean = false;
      
      public var _isBadPlayer:Boolean = false;
      
      public var _protectedObject:Object = {
         "gold":0,
         "score":0,
         "foreverAttackPower":0,
         "foreverResistance":0,
         "foreverCrit":0,
         "foreverMiss":0,
         "currentLevel":1,
         "currentExperience":0
      };
      
      public var _roleID:int = 1;
      
      public var _equipmentsVector:Vector.<Object> = new Vector.<Object>();
      
      public var _petsVector:Vector.<Object> = new Vector.<Object>();
      
      public var _specialVector:Vector.<Object> = new Vector.<Object>();
      
      public var _petFoodVector:Vector.<Object> = new Vector.<Object>();
      
      public var _taskVector:Vector.<Object> = new Vector.<Object>();
      
      public var _roseVector:Vector.<Object> = new Vector.<Object>();
      
      public var _chocolVector:Vector.<Object> = new Vector.<Object>();
      
      public var _currentEquipments:Vector.<Object> = new Vector.<Object>();
      
      public var _saveObject:Object = {};
      
      public var _petSaveObject:Object = {};
      
      public var _petsObject:Array = [];
      
      public var _currentEquipmentType:Vector.<Object> = new Vector.<Object>();
      
      public var _allEquipmentsAndItems:Vector.<Object> = new Vector.<Object>();
      
      public var _repositoryVector:Vector.<Object> = new Vector.<Object>();
      
      public var _passiveSkills:Array = [0,0,0,0,0];
      
      public var _values:Array = [[[[0,80,180,220],[0,10,25,40],[0,80,120,150],[0,90,195,320],[0,200,350,650]],[[0,80,185,230],[0,70,170,300],[0,15,20,25],[0,100,250,340],[0,200,355,655]],[[0,100,200,250],[0,50,100,150],[0,15,20,30],[0,200,250,300],[0,300,400,500]]],[[[0,200,300,500],[0,200,300,500],[0,5,10,15],[0,2,3,4],[0,5,8,12]],[[0,200,300,500],[0,200,300,500],[0,5,10,15],[0,2,3,4],[0,3,5,7]],[[0,200,300,500],[0,200,300,500],[0,5,10,15],[0,2,3,4],[0,20,30,50]]]];
      
      public var _text:Array = [[["点固定伤害","点护甲","点治愈","点技能伤害","点技能伤害"],["点技能伤害","点固定伤害","点攻击力","点技能伤害","点技能伤害"],["点技能伤害","点技能伤害","%吸血","点技能伤害","点技能伤害"]],[["点HP","点MP","点护甲","点回血回蓝","%闪避"],["点HP","点MP","点护甲","点回血回蓝","%暴击"],["点HP","点MP","点护甲","点回血回蓝","点攻击力"]]];
      
      public var _needGolds:Array = [[[[1000,3500.1,6500],[400,6000,12000],[400,6000,12000],[1200,3600,7500],[0,9000,18000]],[[1000,6000,12000],[400.2,2500.1,5000.4],[400.2,2500.7,5000.3],[1200.3,6000.1,12000.4],[0,9000.3,18000.2]],[[400.6,3000.4,12000.1],[1000.2,6000.1,12000.4],[400.2,3600.7,15000.3],[1200.3,6000.1,20000.4],[0,9000.3,18000.2]]],[[[800.1,4000.1,8000.2],[900.6,4500.4,9500.3],[2000.2,5000.1,12000.8],[900.6,9000.5,18000.9],[2500.4,10000.2,30000.9]],[[800.1,4000.2,8000.3],[900.3,4500.7,9500.1],[2000.1,5000.1,12000.2],[900.3,9000.5,18000.8],[2500.4,10000.2,30000.3]],[[800.1,4000.2,8000.3],[900.3,4500.7,9500.1],[2000.1,5000.1,12000.2],[900.3,9000.5,18000.8],[2500.4,10000.2,30000.3]]]];
      
      public var _learnSkill:Array = [0,0,0,0,1];
      
      public var hero:Hero;
      
      public var _isGetGift:* = false;
      
      public var _isGetShareGift:* = false;
      
      public var _isChildernsDay:* = false;
      
      public var _isEightYear:* = false;
      
      public var _is4399Safe:* = false;
      
      public var _isGetGift2:* = false;
      
      public var _isGetGift201301:* = false;
      
      public var _lanterns:Array = [1,0,0,0,0,0,0];
      
      public var _isalReadyGet:Boolean = false;
      
      public var _isGet2013ChildrenGift:Boolean = false;
      
      public var _isGet2013WeChatGift:Boolean = false;
      
      public function User()
      {
         super();
      }
      
      public function getAttackValueByActionType(param1:String) : Number
      {
         var _loc2_:Array = this._values[this._roleID - 1];
         var _loc3_:int = int(param1.substr(2,1)) - 4;
         return this._values[0][this._roleID - 1][_loc3_ - 1][this._learnSkill[_loc3_ - 1]];
      }
      
      public function getPassiveSkillPoint(param1:int, param2:int) : Number
      {
         if(param2 <= 0)
         {
            return 0;
         }
         return this._values[1][this._roleID - 1][param1 - 1][param2];
      }
      
      public function getSkillInfo(param1:int, param2:int) : String
      {
         var _loc3_:int = 0;
         if(param1 == 0)
         {
            _loc3_ = int(this._learnSkill[param2 - 1]);
         }
         else
         {
            _loc3_ = int(this._passiveSkills[param2 - 1]);
         }
         if(_loc3_ == 0)
         {
            return "";
         }
         return this._values[param1][this._roleID - 1][param2 - 1][_loc3_] + this._text[param1][this._roleID - 1][param2 - 1];
      }
      
      public function getNextLevelSkillInfo(param1:int, param2:int) : String
      {
         var _loc3_:int = 0;
         if(param1 == 0)
         {
            _loc3_ = int(this._learnSkill[param2 - 1]);
         }
         else
         {
            _loc3_ = int(this._passiveSkills[param2 - 1]);
         }
         if(_loc3_ < 3)
         {
            return this._values[param1][this._roleID - 1][param2 - 1][_loc3_ + 1] + this._text[param1][this._roleID - 1][param2 - 1];
         }
         return "------";
      }
      
      public function getgoldSkillInfo(param1:int, param2:int) : String
      {
         var _loc3_:int = 0;
         if(param1 == 0)
         {
            _loc3_ = int(this._learnSkill[param2 - 1]);
         }
         else
         {
            _loc3_ = int(this._passiveSkills[param2 - 1]);
         }
         if(_loc3_ < 3)
         {
            return int(this._needGolds[param1][this._roleID - 1][param2 - 1][_loc3_]).toString();
         }
         return "------";
      }
      
      public function init(param1:int) : void
      {
         this._controlPlayer = param1;
      }
      
      public function getControlPlayer() : int
      {
         return this._controlPlayer;
      }
      
      public function getEquipmentID() : Object
      {
         var _loc3_:Equipment = null;
         var _loc1_:Object = {
            "helmet":1,
            "coat":1,
            "trousers":1,
            "shoes":1,
            "sword":1
         };
         var _loc2_:uint = this._currentEquipments.length;
         while(_loc2_--)
         {
            _loc3_ = this._currentEquipments[_loc2_] as Equipment;
            switch(_loc3_._category)
            {
               case "helmet":
                  _loc1_.helmet = _loc3_._frame;
                  break;
               case "coat":
                  _loc1_.coat = _loc3_._frame;
                  break;
               case "trousers":
                  _loc1_.trousers = _loc3_._frame;
                  break;
               case "shoes":
                  _loc1_.shoes = _loc3_._frame;
                  break;
               case "sword":
                  _loc1_.sword = _loc3_._frame;
                  break;
               case "suit":
                  this._suitFrame = _loc3_._frame;
                  this._isHadSuit = true;
                  break;
            }
         }
         if(this._isHadSuit && !this._isHideSuit)
         {
            _loc1_.helmet = this._suitFrame;
            _loc1_.coat = this._suitFrame;
            _loc1_.trousers = this._suitFrame;
            _loc1_.shoes = this._suitFrame;
         }
         return _loc1_;
      }
      
      public function getCurrentEquipmentSavingString() : String
      {
         var _loc1_:* = "";
         var _loc2_:uint = this._currentEquipments.length;
         while(_loc2_--)
         {
            _loc1_ += Equipment(this._currentEquipments[_loc2_]).getEquipmentSaveString();
            if(_loc2_ != 0)
            {
               _loc1_ += "}";
            }
         }
         return _loc1_;
      }
      
      public function getPackageEquipmentSavingString() : String
      {
         var _loc1_:* = "";
         var _loc2_:uint = this._currentEquipments.length;
         while(_loc2_--)
         {
            _loc1_ += Equipment(this._currentEquipments[_loc2_]).getEquipmentSaveString();
            if(_loc2_ != 0)
            {
               _loc1_ += "}";
            }
         }
         return _loc1_;
      }
      
      public function getSavingObject() : Object
      {
         this._saveObject.pets = this._petsObject;
         this._saveObject.controlPlayer = this._controlPlayer;
         this._saveObject.score = this._protectedObject.score;
         this._saveObject.gold = this._protectedObject.gold;
         this._saveObject.currentLevel = this._protectedObject.currentLevel;
         this._saveObject.foreverAttackPower = this._protectedObject.foreverAttackPower;
         this._saveObject.foreverResistance = this._protectedObject.foreverResistance;
         this._saveObject.foreverCrit = this._protectedObject.foreverCrit;
         this._saveObject.foreverMiss = this._protectedObject.foreverMiss;
         this._saveObject.lanterns = this._lanterns;
         this._saveObject.alreadyget = this._isalReadyGet;
         this._saveObject.getGift2 = this._isGetGift2;
         this._saveObject.currentExperience = this._protectedObject.currentExperience;
         this._saveObject.roleID = this._roleID;
         this._saveObject._hideSuit = this._isHideSuit;
         this._saveObject.getGift = this._isGetGift;
         this._saveObject.getShareGift = this._isGetShareGift;
         this._saveObject.childrensDay = this._isChildernsDay;
         this._saveObject.eightYear = this._isEightYear;
         this._saveObject.is4399Safe = this._is4399Safe;
         this._saveObject.equipmentsVector = this._equipmentsVector;
         this._saveObject.specialVector = this._specialVector;
         this._saveObject.currentEquipments = this._currentEquipments;
         this._saveObject.repositoryVector = this._repositoryVector;
         this._saveObject.learnSkill = this._learnSkill;
         this._saveObject.passiveSkill = this._passiveSkills;
         this._saveObject.isGet2013ChildrenGift = this._isGet2013ChildrenGift;
         this._saveObject.isGet2013WeChatGift = this._isGet2013WeChatGift;
         return this._saveObject;
      }
      
      public function vectorToArray(param1:Object) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:int = 0;
         while(_loc3_ < param1.length)
         {
            _loc2_[_loc3_] = param1[_loc3_];
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function setSavingObject(param1:Object) : void
      {
         this._protectedObject = GameUtility.clone(this._protectedObject);
         this._controlPlayer = param1.controlPlayer;
         this._protectedObject.score = param1.score;
         if(param1.gold <= 0)
         {
            param1.gold = 0;
         }
         this._protectedObject.gold = param1.gold;
         if(param1._hideSuit != undefined && param1._hideSuit != null)
         {
            this._isHideSuit = param1._hideSuit;
         }
         if(param1.alreadyget != undefined && param1.alreadyget != null)
         {
            this._isalReadyGet = param1.alreadyget;
         }
         if(param1.getGift != undefined && param1.getGift != null)
         {
            this._isGetGift = param1.getGift;
         }
         if(param1.getShareGift != undefined && param1.getShareGift != null)
         {
            this._isGetShareGift = param1.getShareGift;
         }
         if(param1.eightYear != undefined && param1.eightYear != null)
         {
            this._isEightYear = param1.eightYear;
         }
         if(param1.is4399Safe != undefined && param1.is4399Safe != null)
         {
            this._is4399Safe = param1.is4399Safe;
         }
         if(param1.childrensDay != undefined && param1.childrensDay != null)
         {
            this._isChildernsDay = param1.childrensDay;
         }
         if(param1.getGift2 != undefined && param1.getGift2 != null)
         {
            this._isGetGift2 = param1.getGift2;
         }
         if(param1.lanterns != undefined && param1.lanterns != null)
         {
            this._lanterns = param1.lanterns;
            this._lanterns[0] = 1;
         }
         if(param1.foreverAttackPower != undefined && param1.foreverResistance != undefined && param1.foreverCrit != undefined && param1.foreverMiss != undefined)
         {
            this._protectedObject.foreverAttackPower = param1.foreverAttackPower;
            this._protectedObject.foreverResistance = param1.foreverResistance;
            this._protectedObject.foreverCrit = param1.foreverCrit;
            this._protectedObject.foreverMiss = param1.foreverMiss;
         }
         this._protectedObject.currentLevel = param1.currentLevel;
         this._protectedObject.currentExperience = param1.currentExperience;
         this._roleID = param1.roleID;
         if(param1.pets != undefined && param1.pets != null)
         {
            this._petsObject = param1.pets;
         }
         else
         {
            this._petsVector = new Vector.<Object>();
            this._petSaveObject = {};
            this._petsObject = [];
         }
         if(param1.specialVector != undefined && param1.specialVector != null)
         {
            this._specialVector = param1.specialVector;
         }
         else
         {
            this._specialVector = new Vector.<Object>();
         }
         this._equipmentsVector = param1.equipmentsVector;
         this._currentEquipments = param1.currentEquipments;
         if(param1.repositoryVector != undefined && param1.repositoryVector != null)
         {
            this._repositoryVector = param1.repositoryVector;
         }
         else
         {
            this._repositoryVector = new Vector.<Object>();
         }
         this._learnSkill = param1.learnSkill;
         this._passiveSkills = param1.passiveSkill;
         if(param1.isGet2013ChildrenGift != undefined && param1.isGet2013ChildrenGift != null)
         {
            this._isGet2013ChildrenGift = param1.isGet2013ChildrenGift;
         }
         if(param1.isGet2013WeChatGift != undefined && param1.isGet2013WeChatGift != null)
         {
            this._isGet2013WeChatGift = param1.isGet2013WeChatGift;
         }
      }
   }
}

