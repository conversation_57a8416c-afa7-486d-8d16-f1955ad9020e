package util
{
   import flash.display.DisplayObject;
   import flash.display.Stage;
   import flash.events.NetStatusEvent;
   import flash.net.SharedObject;
   import flash.net.SharedObjectFlushStatus;
   
   public class YuanChuangInface
   {
      
      private static var _instanc:YuanChuangInface;
      
      private static var _canCreate:Boolean = false;
      
      private var _soName:String = "com.edgarcai.adm";
      
      private var _dataName:String = "saveConf";
      
      private var _mySo:SharedObject;
      
      private var _interface:Object;
      
      private var _loginProxy:Object;
      
      public function YuanChuangInface()
      {
         super();
         if(!_canCreate)
         {
            throw new ArgumentError("create YuanChuangInface error");
         }
      }
      
      public static function getInstance() : YuanChuangInface
      {
         if(_instanc == null)
         {
            _canCreate = true;
            _instanc = new YuanChuangInface();
            _canCreate = false;
         }
         return _instanc;
      }
      
      public function setInterface(param1:Stage, param2:Object, param3:String) : void
      {
         this._interface = param2 as DisplayObject;
         if(this._interface == null)
         {
            return;
         }
         param1.addChild(this._interface as DisplayObject);
         param2 = new Object();
         param2["integralMode"] = true;
         param2["gameListMode"] = true;
         param2["saveMode"] = true;
         this._interface["setStyle"](param3,param2);
      }
      
      public function isLoadCtrl() : Boolean
      {
         if(this._interface != null)
         {
            return true;
         }
         return false;
      }
      
      public function getServeTime() : *
      {
         if(this._interface != null)
         {
            this._interface["getServerTime"]();
         }
      }
      
      public function showGameList() : void
      {
         if(this._interface != null)
         {
            this._interface["showGameList"]();
         }
      }
      
      public function showLogPanel() : void
      {
         if(this._interface != null)
         {
            this._interface["showLogPanel"]();
         }
      }
      
      public function isLogin() : Object
      {
         if(this._interface != null)
         {
            return this._interface["isLog"];
         }
         return null;
      }
      
      public function openSortWin() : void
      {
         if(this._interface != null)
         {
            this._interface["openSortWin"]();
         }
      }
      
      public function openSaveUI(param1:String, param2:Object) : void
      {
         if(this._interface != null)
         {
            this._interface["openSaveUI"](param1,param2);
         }
      }
      
      public function getData(param1:Boolean = true, param2:Number = 0) : void
      {
         if(this._interface != null)
         {
            this._interface["get"](true,param2);
         }
      }
      
      public function save(param1:String, param2:Object, param3:Boolean = true, param4:int = 0) : void
      {
         if(this._interface != null)
         {
            this._interface["save"](param1,param2,param3,param4);
         }
      }
      
      public function getList() : void
      {
         if(this._interface != null)
         {
            this._interface["getList"]();
         }
      }
      
      public function buyProFun(param1:String, param2:int) : void
      {
         if(this._interface != null)
         {
            this._interface["buyProFun"](param1,param2);
         }
      }
      
      public function consumeItemFun(param1:String) : void
      {
         if(this._interface != null)
         {
            this._interface["consumeItemFun"](param1);
         }
      }
      
      public function removeItemsFun(param1:Array) : void
      {
         if(this._interface != null)
         {
            this._interface["removeItemsFun"](param1);
         }
      }
      
      public function addItemsFun(param1:Array) : void
      {
         if(this._interface != null)
         {
            this._interface["addItemsFun"](param1);
         }
      }
      
      public function updateItemProFun(param1:Object) : void
      {
         if(this._interface != null)
         {
            this._interface["updateItemProFun"](param1);
         }
      }
      
      public function userLogOut() : void
      {
         if(this._interface != null)
         {
            this._interface["userLogOut"]();
         }
         else if(this._loginProxy != null)
         {
            this._loginProxy.loginOut();
         }
      }
      
      public function openIntegralWin(param1:int) : void
      {
         if(this._interface != null)
         {
            this._interface["openIntegralWin"](param1);
         }
      }
      
      public function showShopUi() : void
      {
         if(this._interface != null)
         {
            this._interface["ShowShopUi"]();
         }
      }
      
      public function setMenuVisible(param1:Boolean) : void
      {
         if(this._interface != null)
         {
            this._interface["setMenuVisible"](param1);
         }
      }
      
      public function saveUserInfo(param1:Object) : void
      {
         var flushStatus:String;
         var data:Object = param1;
         if(this._mySo == null)
         {
            this._mySo = SharedObject.getLocal(this._soName);
         }
         this._mySo.data[this._dataName] = data;
         flushStatus = null;
         try
         {
            flushStatus = this._mySo.flush();
         }
         catch(e:Error)
         {
            if(_mySo)
            {
               _mySo.close();
               _mySo = null;
            }
         }
         if(flushStatus != null)
         {
            switch(flushStatus)
            {
               case SharedObjectFlushStatus.PENDING:
                  if(this._mySo)
                  {
                     this._mySo.addEventListener(NetStatusEvent.NET_STATUS,this.onFlushStatus);
                  }
                  break;
               case SharedObjectFlushStatus.FLUSHED:
                  if(this._mySo != null)
                  {
                     this._mySo.close();
                     this._mySo = null;
                  }
            }
         }
      }
      
      private function onFlushStatus(param1:NetStatusEvent) : void
      {
         switch(param1.info.code)
         {
            case "SharedObject.Flush.Success":
            case "SharedObject.Flush.Failed":
         }
         if(this._mySo != null)
         {
            this._mySo.removeEventListener(NetStatusEvent.NET_STATUS,this.onFlushStatus);
            this._mySo.close();
            this._mySo = null;
         }
      }
      
      public function getUserInfo() : Object
      {
         var data:Object = null;
         if(this._mySo == null)
         {
            this._mySo = SharedObject.getLocal(this._soName);
         }
         if(this._mySo == null)
         {
            return null;
         }
         try
         {
            data = this._mySo.data[this._dataName];
         }
         catch(e:Error)
         {
            return null;
         }
         if(this._mySo)
         {
            this._mySo.close();
            this._mySo = null;
         }
         return data;
      }
      
      public function set loginProxy(param1:Object) : void
      {
         this._loginProxy = param1;
      }
      
      public function get loginProxy() : Object
      {
         return this._loginProxy;
      }
   }
}

