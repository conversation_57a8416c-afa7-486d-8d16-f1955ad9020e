package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol718")]
   public dynamic class getGift extends MovieClip
   {
      
      public var mc:MovieClip;
      
      public function getGift()
      {
         super();
         addFrameScript(19,this.frame20);
      }
      
      internal function frame20() : *
      {
         if(this.parent)
         {
            MovieClip(this.parent).removeChild(this);
         }
      }
   }
}

