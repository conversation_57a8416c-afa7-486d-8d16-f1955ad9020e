package actors
{
   import actors.menu.StrengthenEquipment;
   import base.GameWorld;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import util.GameUtility;
   import util.Input;
   
   public class Town extends MovieClip
   {
      
      public var _world:GameWorld;
      
      public var mcTown:MovieClip;
      
      public var mcDeliver:MovieClip;
      
      public var ironHouse:SimpleButton;
      
      public var strengthMenu:StrengthenEquipment;
      
      public var floor:MovieClip;
      
      private var tk:ThreeKingdoms;
      
      private var shakeVal:Number = 0;
      
      public function Town()
      {
         var _loc2_:* = undefined;
         super();
         this.tk = ThreeKingdoms._instance;
         this.tk._longshot.gotoAndStop("Town");
         this._world = ThreeKingdoms._gameWorld;
         this.mcTown.mouseEnabled = false;
         var _loc1_:* = 0;
         while(_loc1_ < this.numChildren)
         {
            _loc2_ = this.getChildAt(_loc1_);
            if(_loc2_ is MovieClip)
            {
               if(MovieClip(_loc2_).getChildByName("isWall"))
               {
                  _loc2_.visible = false;
                  this._world.addWall(_loc2_);
               }
            }
            _loc1_++;
         }
         this._world.start();
      }
      
      public function shake(param1:int) : void
      {
         if(this.shakeVal == 0)
         {
            this.shakeVal = param1;
         }
      }
      
      public function update() : *
      {
         var _loc1_:Boolean = false;
         var _loc2_:Boolean = false;
         if(this.shakeVal > 0)
         {
            this.x += this.shakeVal;
            this.shakeVal *= -1;
         }
         else if(this.shakeVal < 0)
         {
            this.x += this.shakeVal;
            this.shakeVal = 0;
         }
         _loc1_ = !ThreeKingdoms._gameWorld._heroes[0].isInSky() && ThreeKingdoms._gameWorld._heroes[0].x >= 850 && ThreeKingdoms._gameWorld._heroes[0].x <= 920;
         if(ThreeKingdoms._gameWorld._heroes.length == 2)
         {
            _loc2_ = !ThreeKingdoms._gameWorld._heroes[1].isInSky() && ThreeKingdoms._gameWorld._heroes[1].x >= 850 && ThreeKingdoms._gameWorld._heroes[1].x <= 920;
         }
         if((_loc1_ || _loc2_) && (Input.isKeyPressed(38) || Input.isKeyPressed(87)))
         {
            this.destory();
            this.tk._town = null;
            this.tk.showGameMap();
         }
      }
      
      private function onClickHandler(param1:MouseEvent) : void
      {
         this.tk._gameInfo.visible = false;
         this.strengthMenu = GameUtility.getObject("actors.menu.StrengthenEquipment");
         this.addChild(this.strengthMenu);
      }
      
      public function destory() : void
      {
         GameUtility.clearDisplayList(this);
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         if(this.tk._longshot)
         {
            this.tk.removeChild(this.tk._longshot);
            this.tk._longshot = null;
         }
         ThreeKingdoms._gameWorld.stop();
         ThreeKingdoms._gameWorld.destroy();
         if(this.tk._gameQuality)
         {
            this.tk._gameQuality.visible = false;
         }
         this.tk._town = null;
      }
   }
}

