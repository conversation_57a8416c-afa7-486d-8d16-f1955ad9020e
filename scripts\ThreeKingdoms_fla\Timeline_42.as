package ThreeKingdoms_fla
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol823")]
   public dynamic class Timeline_42 extends MovieClip
   {
      
      public var mcDouble:MovieClip;
      
      public function Timeline_42()
      {
         super();
         addFrameScript(0,this.frame1,1,this.frame2);
      }
      
      internal function frame1() : *
      {
         stop();
      }
      
      internal function frame2() : *
      {
         stop();
      }
   }
}

