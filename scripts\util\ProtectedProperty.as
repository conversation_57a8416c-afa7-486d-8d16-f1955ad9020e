package util
{
   import flash.utils.Dictionary;
   
   public class ProtectedProperty
   {
      
      private var _dictionary:Dictionary = new Dictionary();
      
      public function ProtectedProperty()
      {
         super();
      }
      
      public function addProperty(param1:Object, param2:String, param3:Object) : void
      {
         var _loc4_:Object = new Object();
         _loc4_[param2] = param3;
         this._dictionary[param1] = _loc4_;
      }
      
      public function setProperty(param1:Object, param2:String, param3:Object) : void
      {
         var _loc4_:Object = this._dictionary[param1];
         if(_loc4_)
         {
            _loc4_ = GameUtility.clone(_loc4_);
            _loc4_[param2] = param3;
            this._dictionary[param1] = _loc4_;
         }
      }
      
      public function getProperty(param1:Object, param2:String) : Object
      {
         var _loc3_:Object = this._dictionary[param1];
         if(_loc3_)
         {
            return _loc3_[param2];
         }
         return null;
      }
      
      public function removeProperty(param1:Object) : void
      {
         this._dictionary[param1] = null;
      }
      
      public function clear() : void
      {
         var _loc1_:* = undefined;
         for each(_loc1_ in this._dictionary)
         {
            _loc1_ = null;
         }
         this._dictionary = new Dictionary();
      }
   }
}

