package com.adobe.serialization.json
{
   public class AJSONDecoder
   {
      
      private var strict:<PERSON><PERSON>an;
      
      private var value:*;
      
      private var tokenizer:AJSONTokenizer;
      
      private var token:AJ<PERSON>NToken;
      
      public function AJSONDecoder(param1:String, param2:<PERSON>olean = true)
      {
         super();
         this.strict = param2;
         this.tokenizer = new AJSONTokenizer(param1,param2);
         this.nextToken();
         this.value = this.parseValue();
         if(param2 && this.nextToken() != null)
         {
            this.tokenizer.parseError("Unexpected characters left in input stream");
         }
      }
      
      public function getValue() : *
      {
         return this.value;
      }
      
      final private function nextToken() : AJSONToken
      {
         return this.token = this.tokenizer.getNextToken();
      }
      
      final private function nextValidToken() : AJSONToken
      {
         this.token = this.tokenizer.getNextToken();
         this.checkValidToken();
         return this.token;
      }
      
      final private function checkValidToken() : void
      {
         if(this.token == null)
         {
            this.tokenizer.parseError("Unexpected end of input");
         }
      }
      
      final private function parseArray() : Array
      {
         var _loc1_:Array = new Array();
         this.nextValidToken();
         if(this.token.type == AJSONTokenType.RIGHT_BRACKET)
         {
            return _loc1_;
         }
         if(!this.strict && this.token.type == AJSONTokenType.COMMA)
         {
            this.nextValidToken();
            if(this.token.type == AJSONTokenType.RIGHT_BRACKET)
            {
               return _loc1_;
            }
            this.tokenizer.parseError("Leading commas are not supported.  Expecting \']\' but found " + this.token.value);
         }
         while(true)
         {
            _loc1_.push(this.parseValue());
            this.nextValidToken();
            if(this.token.type == AJSONTokenType.RIGHT_BRACKET)
            {
               break;
            }
            if(this.token.type == AJSONTokenType.COMMA)
            {
               this.nextToken();
               if(!this.strict)
               {
                  this.checkValidToken();
                  if(this.token.type == AJSONTokenType.RIGHT_BRACKET)
                  {
                     return _loc1_;
                  }
               }
            }
            else
            {
               this.tokenizer.parseError("Expecting ] or , but found " + this.token.value);
            }
         }
         return _loc1_;
      }
      
      final private function parseObject() : Object
      {
         var _loc2_:String = null;
         var _loc1_:Object = new Object();
         this.nextValidToken();
         if(this.token.type == AJSONTokenType.RIGHT_BRACE)
         {
            return _loc1_;
         }
         if(!this.strict && this.token.type == AJSONTokenType.COMMA)
         {
            this.nextValidToken();
            if(this.token.type == AJSONTokenType.RIGHT_BRACE)
            {
               return _loc1_;
            }
            this.tokenizer.parseError("Leading commas are not supported.  Expecting \'}\' but found " + this.token.value);
         }
         while(true)
         {
            if(this.token.type == AJSONTokenType.STRING)
            {
               _loc2_ = String(this.token.value);
               this.nextValidToken();
               if(this.token.type == AJSONTokenType.COLON)
               {
                  this.nextToken();
                  _loc1_[_loc2_] = this.parseValue();
                  this.nextValidToken();
                  if(this.token.type == AJSONTokenType.RIGHT_BRACE)
                  {
                     break;
                  }
                  if(this.token.type == AJSONTokenType.COMMA)
                  {
                     this.nextToken();
                     if(!this.strict)
                     {
                        this.checkValidToken();
                        if(this.token.type == AJSONTokenType.RIGHT_BRACE)
                        {
                           return _loc1_;
                        }
                     }
                  }
                  else
                  {
                     this.tokenizer.parseError("Expecting } or , but found " + this.token.value);
                  }
               }
               else
               {
                  this.tokenizer.parseError("Expecting : but found " + this.token.value);
               }
            }
            else
            {
               this.tokenizer.parseError("Expecting string but found " + this.token.value);
            }
         }
         return _loc1_;
      }
      
      final private function parseValue() : Object
      {
         this.checkValidToken();
         switch(this.token.type)
         {
            case AJSONTokenType.LEFT_BRACE:
               return this.parseObject();
            case AJSONTokenType.LEFT_BRACKET:
               return this.parseArray();
            case AJSONTokenType.STRING:
            case AJSONTokenType.NUMBER:
            case AJSONTokenType.TRUE:
            case AJSONTokenType.FALSE:
            case AJSONTokenType.NULL:
               return this.token.value;
            case AJSONTokenType.NAN:
               if(!this.strict)
               {
                  return this.token.value;
               }
               this.tokenizer.parseError("Unexpected " + this.token.value);
               break;
         }
         this.tokenizer.parseError("Unexpected " + this.token.value);
         return null;
      }
   }
}

