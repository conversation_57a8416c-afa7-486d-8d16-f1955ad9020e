package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   
   public class Enemy14 extends Enemy
   {
      
      public function Enemy14(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 600;
         this._object._attackRange = 500;
         this._object._alertRange = 600;
         this._goldPrice = 300;
         this.isBoss = true;
         this._walkSpeed = 5;
         this.enemyName = "貂蝉";
         this._currentHealthPoint = 23000;
         this._totalHealthPoint = 23000;
         this._attackProbablity = 90;
         this._experience = 1600;
         this._resistance = 40;
         this._probability = 0.5;
         this._fallEquipmentsList = [{
            "id":58,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":59,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":4,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":15,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":16,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":17,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":18,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":41,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":42,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":43,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":44,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":78,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":79,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":80,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":81,
            "qualityID":[2],
            "type":"equipment"
         },{
            "id":65,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":30,
            "qualityID":[0,1],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,2],
            "attackInterval":4,
            "attackPower":153 + int(Math.random() * 62),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,4],
            "attackInterval":4,
            "attackPower":153 + int(Math.random() * 20),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[-2,-2],
            "attackInterval":4,
            "attackPower":153 + int(Math.random() * 20),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击4"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,4],
            "attackInterval":4,
            "attackPower":153 + int(Math.random() * 62),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(this._curAttackTarget)
         {
            if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) >= 400 && GameUtility.getDistance(this,this._curAttackTarget) <= 500)
               {
                  if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 210;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 200 && GameUtility.getDistance(this,this._curAttackTarget) < 400)
               {
                  if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 210;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 180;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 100 && GameUtility.getDistance(this,this._curAttackTarget) <= 200)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 160;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 210;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 180;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) >= 50 && GameUtility.getDistance(this,this._curAttackTarget) <= 100)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 160;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     _skill2CoolDown = 210;
                     this.realseSkill2();
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     _skill3CoolDown = 180;
                     this.realseSkill3();
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) < 50)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 160;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     _skill2CoolDown = 210;
                     this.realseSkill2();
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     _skill3CoolDown = 180;
                     this.realseSkill3();
                  }
                  else
                  {
                     this.attack();
                  }
               }
               else
               {
                  moveTowardHero();
               }
            }
            else
            {
               moveTowardHero();
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(14);
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         newAttackID();
      }
      
      override public function realseSkill2() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(22);
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         newAttackID();
      }
      
      override public function realseSkill3() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(25);
         this._lastHit = "攻击3";
         this.gotoAndStop("攻击3");
         newAttackID();
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         setYourDaddysTime(23);
         this._lastHit = "攻击4";
         this.gotoAndStop("攻击4");
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4";
      }
   }
}

