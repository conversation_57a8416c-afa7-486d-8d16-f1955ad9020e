package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy25 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy25(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._walkSpeed = 2.5;
         this._object._maxPatrolView = 650;
         this._object._alertRange = 650;
         this._object._attackRange = 300;
         this._currentHealthPoint = 1500;
         this._totalHealthPoint = 1500;
         this._experience = 106;
         this._resistance = 40;
         this._probability = 0.3;
         this._goldPrice = 30 + Math.round(Math.random() * 20);
         this._attackProbablity = 38;
         this._fallEquipmentsList = [{
            "id":12,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":13,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":75,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":76,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":38,
            "qualityID":[0,1],
            "type":"equipment"
         },{
            "id":39,
            "qualityID":[0,1],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,3],
            "attackInterval":4,
            "attackPower":70 + Math.round(Math.random() * 28),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(10))
         {
            wait();
         }
         else if(GameUtility.getRandomNumber(_attackProbablity))
         {
            if(GameUtility.getDistance(this,this._curAttackTarget) < 300)
            {
               this.attack();
            }
            else
            {
               moveTowardHero();
            }
         }
         else
         {
            moveTowardHero();
         }
      }
      
      override public function attack() : void
      {
         steer();
         this._vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(21);
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1";
      }
   }
}

