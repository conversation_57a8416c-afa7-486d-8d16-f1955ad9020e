package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol886")]
   public dynamic class GetPet extends MovieClip
   {
      
      public function GetPet()
      {
         super();
         addFrameScript(29,this.frame30);
      }
      
      internal function frame30() : *
      {
         if(this.parent)
         {
            MovieClip(this.parent).removeChild(this);
         }
      }
   }
}

