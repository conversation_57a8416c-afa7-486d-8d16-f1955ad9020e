package event
{
   import flash.events.Event;
   
   public class GameEvent extends Event
   {
      
      public static const HERO_DEAD:String = "Hero Dead";
      
      public static const LEVEL_UP:String = "Level UP";
      
      public static const LEVEL_CLEAR:String = "Level Clear";
      
      public static const SELECT_HERO_IS_DONE:String = "Select Hero Is Done";
      
      public static const ENEMY_IS_UNDER_ATTACK:String = "Enemy Is Under Attack";
      
      public static const HERO_IS_UNDER_ATTACK:String = "Hero Is Under Attack";
      
      public static const ENEMY_IS_ATTACK_BY_BULLET:String = "Enemy Is Attack By Bullet";
      
      public static const HERO_IS_UNDER_ATTACK_BY_BULLET:String = "Hero Is Under Attack By Bullet";
      
      public static const GAME_SAVED:String = "Game Saved";
      
      public static const CHECK_DATA_ERROR:String = "Check Data Error";
      
      public static const DOUBLE_CLICK_ITEM:String = "Double Click Item";
      
      public var _data:Object;
      
      public function GameEvent(param1:String, param2:Object = null, param3:Boolean = false, param4:Boolean = false)
      {
         this._data = param2;
         super(param1,param3,param4);
      }
      
      override public function clone() : Event
      {
         return new GameEvent(type,bubbles,cancelable);
      }
      
      override public function toString() : String
      {
         return formatToString("GameEvent","type","bubbles","cancelable","eventPhase");
      }
   }
}

