package base
{
   import actors.Enemy;
   import actors.Hero;
   import actors.roles.Role1;
   import actors.roles.Role2;
   import actors.roles.Role3;
   import com.greensock.TweenMax;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.Dictionary;
   import game.Game;
   import util.FloatingNumber;
   import util.GameUtility;
   
   public class GameObject extends MovieClip
   {
      
      public static const LEFT:Number = 1;
      
      public static const RIGHT:Number = 2;
      
      public static const UP:Number = 3;
      
      public static const DOWN:Number = 4;
      
      public var _isHitWall:Boolean = false;
      
      public var _isHitLeft:Boolean = false;
      
      public var _isHitRight:Boolean = false;
      
      public var _isHitUp:Boolean = false;
      
      public var _isHitDown:Boolean = false;
      
      public var _direction:Number = 0;
      
      public var _vx:Number = 0;
      
      public var _vy:Number = 0;
      
      public var _jumpSpeed:Number = -20;
      
      public var _walkSpeed:Number;
      
      public var _runSpeed:Number;
      
      public var _ax:Number = 0;
      
      public var _ay:Number = 1.5;
      
      private var _edgeDistance:Number = 100;
      
      public var collipse:MovieClip;
      
      public var body:MovieClip;
      
      public var standInObj:MovieClip;
      
      public var headInObj:MovieClip;
      
      public var leftInObj:MovieClip;
      
      public var rightInObj:MovieClip;
      
      public var _isJumping:Boolean = false;
      
      public var _isDizziness:Boolean = false;
      
      public var _isRunning:Boolean = false;
      
      public var _isAttacking:Boolean = false;
      
      public var _isUnderAttack:Boolean = false;
      
      public var _game:Game;
      
      protected var _lifeBar:Sprite;
      
      public var _currentHealthPoint:Number = 100;
      
      protected var _totalHealthPoint:Number = 100;
      
      protected var _count:uint = 0;
      
      public var _yourDaddysTime:Number = 0;
      
      public var _framesLength:Number = 0;
      
      public var _attackID:int = 0;
      
      public var _underAttackIDVector:Vector.<Object> = new Vector.<Object>();
      
      public var _currentAction:String = "";
      
      public var _actionType:String = "";
      
      public var _attackBackInfomationDictionary:Dictionary = new Dictionary();
      
      public var _isCrit:Boolean = false;
      
      public var _lastHit:String = "";
      
      public var _isUnique:Boolean = false;
      
      public var isBoss:Boolean = false;
      
      public var _magicBulletArray:Array = [];
      
      public var _isLeft:Boolean;
      
      public var _isRight:Boolean;
      
      public var _isFlying:Boolean = false;
      
      public var _object:Object = {
         "_direction":LEFT,
         "_jumpCount":0,
         "_maxPatrolView":100,
         "_direction":0,
         "_alertRange":100,
         "_attackRange":80
      };
      
      public var _world:GameWorld;
      
      public var _beenAttackIDArray:Array = [];
      
      public function GameObject()
      {
         super();
         this._world = ThreeKingdoms._gameWorld;
         if(this.collipse)
         {
            this.collipse.alpha = 0;
         }
         this._game = ThreeKingdoms._instance._game;
         this.createLifeBar();
         ThreeKingdoms._instance._protectedProperty.addProperty(this,"_whosYourDaddy",false);
         ThreeKingdoms._instance._protectedProperty.addProperty(this,"_jumpCount",0);
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
      }
      
      public function onRemovedFromStageHandler(param1:Event) : void
      {
      }
      
      public function onAddToStageHandler(param1:Event) : void
      {
      }
      
      public function isDead() : Boolean
      {
         return this._currentHealthPoint <= 0;
      }
      
      protected function createLifeBar() : void
      {
         this._lifeBar = new Sprite();
         this._lifeBar.visible = false;
         this._lifeBar.x = -24;
         this._lifeBar.y = -90;
         addChild(this._lifeBar);
      }
      
      protected function showLifeBar() : void
      {
         this._lifeBar.visible = true;
         TweenMax.to(this._lifeBar,2,{
            "alpha":0,
            "onComplete":this.remvoeLifeBar
         });
      }
      
      protected function remvoeLifeBar() : void
      {
         this._lifeBar.visible = false;
      }
      
      protected function drawHealthPoint() : void
      {
         this._lifeBar.graphics.clear();
         this._lifeBar.alpha = 1;
         var _loc1_:Number = this._currentHealthPoint / this._totalHealthPoint;
         _loc1_ = _loc1_ < 0 ? 0 : _loc1_;
         if(this._currentHealthPoint >= 0)
         {
            this._lifeBar.graphics.lineStyle(1.2,0);
            this._lifeBar.graphics.drawRect(0,5,50,5);
            this._lifeBar.graphics.beginFill(16711680);
            if(this.transform.matrix.a == 1)
            {
               this._lifeBar.graphics.drawRect(0,5,50 * _loc1_,5);
            }
            else if(this.transform.matrix.a == -1)
            {
               this._lifeBar.graphics.drawRect(50 - 50 * _loc1_,5,50 * _loc1_,5);
            }
            this._lifeBar.graphics.endFill();
         }
      }
      
      protected function showDizziness() : void
      {
         var _loc1_:MovieClip = GameUtility.getObject("mcDizziness");
         _loc1_.x = -24;
         _loc1_.y = -90;
         _loc1_.name = "Dizziness";
         addChild(_loc1_);
         this._isDizziness = true;
      }
      
      protected function hideDizziness() : void
      {
         if(this.getChildByName("Dizziness"))
         {
            this.removeChild(this.getChildByName("Dizziness"));
         }
         this._isDizziness = false;
      }
      
      public function realseSkill5() : void
      {
      }
      
      public function realseSkill4() : void
      {
      }
      
      public function realseSkill3() : void
      {
      }
      
      public function realseSkill2() : void
      {
      }
      
      public function realseSkill1() : void
      {
      }
      
      public function update() : void
      {
         this.fallOffScreen();
         if(this._framesLength > 0)
         {
            --this._framesLength;
            if(this._framesLength <= 0)
            {
               this._framesLength = 0;
               this.nextAction();
            }
         }
         if(this._yourDaddysTime > 0)
         {
            --this._yourDaddysTime;
            if(this._yourDaddysTime <= 0)
            {
               this._yourDaddysTime = 0;
               ThreeKingdoms._instance._protectedProperty.addProperty(this,"_whosYourDaddy",false);
            }
         }
         if((this is Role1 || this is Role2 || this is Role3) && (this.currentLabel == "跳" || this.currentLabel == "二级跳"))
         {
            if(this._vy > 0 && this.isInSky() && !this.isAttacking() && !this.isUnderAttack())
            {
               this.gotoAndStop("落地");
            }
         }
         if(!this._isFlying)
         {
            this.checkCanMove();
         }
         else
         {
            this.move();
         }
      }
      
      protected function nextAction() : void
      {
      }
      
      private function checkStageBoundrary() : Boolean
      {
         var _loc1_:Rectangle = this.collipse.getBounds(this.parent);
         var _loc2_:Number = _loc1_.x + this._vx;
         var _loc3_:Number = _loc1_.x + _loc1_.width + this._vx;
         var _loc4_:Number = _loc1_.y + this._vy;
         var _loc5_:Number = _loc1_.y + _loc1_.height + this._vy;
         return _loc2_ >= 0 && _loc3_ < 960 || _loc4_ >= 20 && _loc5_ <= 500 || this._vx < 0 && _loc3_ > 960 || this._vx > 0 && _loc2_ < 0;
      }
      
      private function checkCanMove() : void
      {
         var _loc2_:MovieClip = null;
         var _loc3_:Rectangle = null;
         this._isHitRight = false;
         this._isHitLeft = false;
         this.standInObj = null;
         this.headInObj = null;
         this.leftInObj = null;
         this.rightInObj = null;
         var _loc1_:int = 0;
         while(_loc1_ < this._world._walls.length)
         {
            _loc2_ = this._world._walls[_loc1_] as MovieClip;
            _loc3_ = _loc2_.getBounds(_loc2_.parent);
            if(_loc3_.intersects(this.getNextFrameBounds()))
            {
               this.nearByTheWall(_loc2_);
            }
            _loc1_++;
         }
         this.move();
      }
      
      protected function nearByTheWall(param1:MovieClip) : void
      {
         var _loc2_:Rectangle = param1.getBounds(param1.parent);
         var _loc3_:* = param1.getBounds(param1.parent);
         var _loc4_:Rectangle = this.getNextFrameXBounds();
         var _loc5_:Rectangle = this.collipse.getBounds(param1.parent);
         var _loc6_:Number = 8;
         if(this._vx <= 0 && (_loc5_.x >= _loc2_.x + _loc2_.width && _loc4_.x <= _loc3_.x + _loc3_.width && _loc4_.x + _loc4_.width >= _loc3_.x) && this.getBottom() > _loc3_.y + 5)
         {
            if(!this._isJumping || (this is Role1 || this is Role2 || this is Role3))
            {
               this._vx = 0;
            }
            if(!(this is Role1 || this is Role2 || this is Role3) && (this.currentLabel == "被攻击" || this.currentLabel == "死亡"))
            {
               this._vx = 0;
            }
            this._isHitRight = false;
            this._isHitLeft = true;
            this.x = _loc3_.x + _loc3_.width + 1 + this.collipse.width * 0.5;
            this.leftInObj = param1;
         }
         if(this._vx < 0 && this.x - this._vx <= _loc2_.x + _loc2_.width && (_loc4_.x <= _loc3_.x + _loc3_.width && _loc4_.x + _loc4_.width >= _loc3_.x) && this.getBottom() > _loc3_.y + 5)
         {
            this._vx = 0;
            this._isHitRight = false;
            this._isHitLeft = true;
            if(this._vx > 6)
            {
               this.x = _loc3_.x + _loc3_.width + 1 + this.collipse.width * 0.5;
            }
            this.leftInObj = param1;
         }
         if(this._vx >= 0 && (_loc5_.x <= _loc2_.x && _loc4_.x + _loc4_.width >= _loc3_.x && _loc4_.x <= _loc3_.x + _loc3_.width) && this.getBottom() > _loc3_.y + 5)
         {
            if(!this._isJumping || (this is Role1 || this is Role2 || this is Role3))
            {
               this._vx = 0;
            }
            if(!(this is Role1 || this is Role2 || this is Role3) && (this.currentLabel == "被攻击" || this.currentLabel == "死亡"))
            {
               this._vx = 0;
            }
            this._isHitRight = true;
            this._isHitLeft = false;
            this.x = _loc3_.x - 1 - this.collipse.width * 0.5;
            this.rightInObj = param1;
         }
         if(this._vx > 0 && (this.x + this._vx >= _loc2_.x && _loc4_.x + _loc4_.width >= _loc3_.x && _loc4_.x <= _loc3_.x + _loc3_.width) && this.getBottom() > _loc3_.y + 5)
         {
            this._vx = 0;
            this._isHitRight = true;
            this._isHitLeft = false;
            if(this._vx > 6)
            {
               this.x = _loc3_.x - 1 - this.collipse.width * 0.5;
            }
            this.rightInObj = param1;
         }
         if(this._vy > 0 && this.getBottom() <= _loc3_.y)
         {
            if(_loc5_.x + _loc5_.width > _loc3_.x && _loc5_.x < _loc3_.x + _loc3_.width)
            {
               this.y = _loc3_.y - 0.1 - (this.collipse.y + this.collipse.height / 2);
               ThreeKingdoms._instance._protectedProperty.setProperty(this,"_jumpCount",2);
               this._isJumping = false;
               this._vy = 0;
               this.standInObj = param1;
               if(this.isJumping())
               {
                  if(this.currentLabel != "休息")
                  {
                     this.gotoAndStop("休息");
                  }
               }
            }
         }
         if(this._vy <= 0 && this.getTop() >= _loc3_.y + _loc3_.height)
         {
            this.y = _loc3_.y + _loc3_.height + 0.1 + this.collipse.height * 0.5;
            this._vy = 0;
            this.headInObj = param1;
            this._isJumping = true;
         }
      }
      
      protected function move() : void
      {
         var _loc1_:Point = null;
         if(this.isStandStillWhenAttack() && (this is Role1 || this is Role2 || this is Role3))
         {
            if(this._vy < 0)
            {
               this._vy = 0;
            }
            return;
         }
         if(this is Role1 || this is Role2 || this is Role3)
         {
            _loc1_ = this.parent.localToGlobal(new Point(this.x,this.y));
            if(_loc1_.x + this._vx > 40 && _loc1_.x + this._vx < 920)
            {
               this.x += this._vx;
            }
            else if(_loc1_.x <= 40)
            {
               this.x = this.x + 40 - _loc1_.x;
            }
            else if(_loc1_.x >= 920)
            {
               this.x = this.x + 920 - _loc1_.x;
            }
         }
         else
         {
            this.x += this._vx;
         }
         this.y += this._vy;
         this._vx += this._ax;
         this._vy += this._ay;
         if(this._vy > 20)
         {
            this._vy = 20;
         }
      }
      
      public function getNextFrameXBounds() : Rectangle
      {
         var _loc1_:Rectangle = this.collipse.getBounds(this.parent);
         _loc1_.offset(this._vx,0);
         return _loc1_;
      }
      
      public function getNextFrameBounds() : Rectangle
      {
         var _loc1_:Rectangle = this.collipse.getBounds(this.parent);
         _loc1_.offset(this._vx,this._vy);
         return _loc1_;
      }
      
      public function getNextFrameBound() : Rectangle
      {
         var _loc1_:Rectangle = this.getBounds(this.parent);
         _loc1_.offsetPoint(new Point(this._vx,this._vy));
         return _loc1_;
      }
      
      protected function getBottom() : Number
      {
         return this.collipse.y + this.collipse.height / 2 + this.y;
      }
      
      protected function getTop() : Number
      {
         return this.collipse.y - this.collipse.height / 2 + this.y;
      }
      
      public function getAttackID() : String
      {
         return this.name + this._attackID;
      }
      
      public function getOnlyAttackID() : int
      {
         return this._attackID;
      }
      
      public function newAttackID() : void
      {
         ++this._attackID;
      }
      
      public function isMoving() : Boolean
      {
         return true;
      }
      
      public function isStandStillWhenAttack() : Boolean
      {
         return false;
      }
      
      public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || currentLabel == "攻击2" || currentLabel == "攻击3";
      }
      
      public function isJumping() : Boolean
      {
         return false;
      }
      
      public function isRunning() : Boolean
      {
         return false;
      }
      
      public function isInSky() : Boolean
      {
         return !this.standInObj;
      }
      
      public function isUnderAttack() : Boolean
      {
         return this.currentLabel == "被攻击";
      }
      
      public function attack() : void
      {
      }
      
      protected function jumpDown() : void
      {
         if(Boolean(this.standInObj) && Boolean(this.standInObj.getChildByName("isThroughWall")))
         {
            this.y += 10;
            this._currentAction = "jump";
            ThreeKingdoms._instance._protectedProperty.setProperty(this,"_jumpCount",1);
         }
      }
      
      public function getSpecificAttackPowerByType(param1:String) : int
      {
         return 0;
      }
      
      public function decreaseHealthPoint(param1:int) : void
      {
      }
      
      public function isMoveOrRun() : void
      {
         if(!this._isRunning)
         {
            this._currentAction = "右移动";
         }
         else
         {
            this._currentAction = "跑";
         }
      }
      
      public function jump() : void
      {
      }
      
      protected function moveLeft() : void
      {
         if(!this.isAttacking())
         {
            this._isLeft = true;
            this._isRight = false;
            GameUtility.flipHorizontal(this,1);
            if(!this.isInSky() && !this.isAttacking() && !this.isUnderAttack())
            {
               this.gotoAndStop("行走");
            }
         }
      }
      
      protected function addBeAttackEffect(param1:GameObject) : void
      {
      }
      
      protected function moveRight() : void
      {
         if(!this.isAttacking())
         {
            this._isLeft = false;
            this._isRight = true;
            GameUtility.flipHorizontal(this,-1);
            if(!this.isInSky() && !this.isAttacking() && !this.isUnderAttack())
            {
               this.gotoAndStop("行走");
            }
         }
      }
      
      public function fallOffScreen() : void
      {
         if(this.y > 750)
         {
            if(this is Hero || this is Enemy)
            {
               if(this.currentLabel != "死亡")
               {
                  this.y = 0;
               }
            }
            else
            {
               this.y = 0;
            }
         }
      }
      
      protected function beenAttackBackLeft(param1:Number, param2:Number) : void
      {
         this._vx = -param1;
         this._vy = param2;
      }
      
      protected function beenAttackBackRight(param1:Number, param2:Number) : void
      {
         this._vx = param1;
         this._vy = param2;
      }
      
      public function beenAttackBack(param1:GameObject, param2:Number, param3:Number) : void
      {
         if(param1.x > this.x)
         {
            this.beenAttackBackLeft(param2,param3);
         }
         else
         {
            this.beenAttackBackRight(param2,param3);
         }
      }
      
      protected function addHealingAnimation(param1:int, param2:String = "") : void
      {
         var _loc3_:FloatingNumber = new FloatingNumber();
         ThreeKingdoms._instance._game.addChild(_loc3_);
         _loc3_.addFloatingNumberBitmap(param2,param1,this.x - 20,this.y - 60,20);
      }
      
      public function setStandStill() : void
      {
         this._direction = 0;
         this._vx = 0;
         this._vy = 0;
      }
      
      public function setYourDaddysTime(param1:Number) : void
      {
         ThreeKingdoms._instance._protectedProperty.setProperty(this,"_whosYourDaddy",true);
         this._yourDaddysTime = param1;
      }
      
      public function destroy() : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onAddToStageHandler);
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         this._underAttackIDVector = new Vector.<Object>();
         this._magicBulletArray = [];
         this._yourDaddysTime = 0;
         this._count = 0;
      }
   }
}

