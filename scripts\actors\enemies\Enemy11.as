package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy11 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy11(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 600;
         this._walkSpeed = 5;
         this._object._alertRange = 600;
         this._object._attackRange = 400;
         this._currentHealthPoint = 18000;
         this._totalHealthPoint = 18000;
         this._attackProbablity = 65;
         this._resistance = 45;
         this._experience = 692;
         this._goldPrice = 100 + Math.round(Math.random() * 50);
         this.isBoss = true;
         this.enemyName = "王允";
         this._probability = 0.3;
         this._fallEquipmentsList = [{
            "id":3,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":64,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":17,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":18,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":15,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":16,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":78,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":79,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":80,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":81,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":29,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":41,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":42,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":43,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":44,
            "qualityID":[1],
            "type":"equipment"
         }];
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":120 + int(Math.random() * 60),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":120 + int(Math.random() * 60),
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-1.5],
            "attackInterval":4,
            "attackPower":120 + int(Math.random() * 60),
            "attackType":"physical"
         };
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(this._curAttackTarget)
         {
            if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) >= 300 && GameUtility.getDistance(this,this._curAttackTarget) <= 500)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 210;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) > 100 && GameUtility.getDistance(this,this._curAttackTarget) < 300)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 210;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 120;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) <= 100)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 210;
                  }
                  else if(_skill2CoolDown == 0)
                  {
                     this.realseSkill2();
                     _skill2CoolDown = 120;
                  }
                  else if(_skill3CoolDown == 0)
                  {
                     this.realseSkill3();
                     _skill3CoolDown = 90;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else
               {
                  moveTowardHero();
               }
            }
            else
            {
               moveTowardHero();
            }
         }
      }
      
      override public function isAttacking() : Boolean
      {
         return super.isAttacking();
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(22);
         newAttackID();
      }
      
      override public function realseSkill2() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         setYourDaddysTime(31);
         newAttackID();
      }
      
      override public function realseSkill3() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击3";
         this.gotoAndStop("攻击3");
         setYourDaddysTime(26);
         newAttackID();
      }
   }
}

