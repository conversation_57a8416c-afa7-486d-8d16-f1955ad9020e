package actors.enemies
{
   import actors.Enemy;
   import actors.Hero;
   import util.GameUtility;
   
   public class Enemy32 extends Enemy
   {
      
      private var count:int = 300;
      
      public function Enemy32(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 1900;
         this._object._alertRange = 1900;
         this._object._attackRange = 900;
         this._currentHealthPoint = 130000;
         this._totalHealthPoint = 130000;
         this._resistance = 130;
         this._experience = 5000;
         this._attackProbablity = 100;
         this._goldPrice = 5000;
         this._walkSpeed = 7;
         this.isBoss = true;
         this._probability = 0.2;
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[5,5],
            "attackInterval":4,
            "attackPower":(432 + Math.round(Math.random() * 34)) * 1.5,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[5,5],
            "attackInterval":4,
            "attackPower":(432 + Math.round(Math.random() * 34)) * 1.5,
            "attackType":"physical"
         };
         this.enemyName = "吕布";
         this._fallEquipmentsList = [{
            "id":23,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":24,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":25,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":26,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":86,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":87,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":88,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":89,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":49,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":50,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":51,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":52,
            "qualityID":[1],
            "type":"equipment"
         }];
      }
      
      override public function update() : void
      {
         var _loc1_:Enemy31 = null;
         if(this.count > 0)
         {
            --this.count;
         }
         if(this.count == 0)
         {
            _loc1_ = _game.addEnemy(31,this.x,this.y) as Enemy31;
            _loc1_.setCurrentHealthPoint(_currentHealthPoint);
            this.removeThis();
            return;
         }
         super.update();
      }
      
      public function setCurrentHealthPoint(param1:int) : void
      {
         this._currentHealthPoint = param1;
      }
      
      override public function startAttacking() : void
      {
         if(this._curAttackTarget)
         {
            if(GameUtility.getRandomNumber(5))
            {
               wait();
            }
            else if(GameUtility.getRandomNumber(_attackProbablity))
            {
               if(GameUtility.getDistance(this,this._curAttackTarget) > 270 && GameUtility.getDistance(this,this._curAttackTarget) < 950)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 100;
                  }
                  else
                  {
                     moveTowardHero();
                  }
               }
               else if(GameUtility.getDistance(this,this._curAttackTarget) <= 270)
               {
                  if(_skill1CoolDown == 0)
                  {
                     this.realseSkill1();
                     _skill1CoolDown = 100;
                  }
                  else
                  {
                     this.attack();
                  }
               }
            }
            else
            {
               moveTowardHero();
            }
         }
      }
      
      override public function realseSkill1() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击2";
         this.gotoAndStop("攻击2");
         setYourDaddysTime(77);
         newAttackID();
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(16);
         newAttackID();
      }
      
      override public function afterAttack(param1:Hero = null, param2:Object = null) : void
      {
         super.afterAttack(param1,param2);
      }
      
      override public function isUnderAttack() : Boolean
      {
         return this.currentLabel == "被攻击";
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2";
      }
      
      override public function destroy() : void
      {
         super.destroy();
      }
   }
}

