package actors.equipments.pharmaceutical
{
   import actors.Hero;
   import base.GameObject;
   import com.greensock.TweenMax;
   import util.HitTest;
   
   public class LesserHealingSalve extends GameObject
   {
      
      protected var _isUsed:Boolean = false;
      
      protected var _name:String = "";
      
      protected var _isRemoved:<PERSON><PERSON><PERSON> = false;
      
      protected var _cureNumber:uint = 0;
      
      protected var _flag:<PERSON><PERSON><PERSON> = true;
      
      protected var _owner:Hero;
      
      public function LesserHealingSalve(param1:<PERSON>olean = false)
      {
         super();
         this._isUsed = param1;
         this._name = "LesserHealingSalve";
         this._cureNumber = 150;
      }
      
      override public function update() : void
      {
         var _loc2_:Hero = null;
         super.update();
         var _loc1_:uint = ThreeKingdoms._gameWorld._heroes.length;
         while(_loc1_-- > 0)
         {
            _loc2_ = ThreeKingdoms._gameWorld._heroes[_loc1_] as Hero;
            if(!(Math.abs(this.x - _loc2_.x) > 700 || _loc2_.isDead()))
            {
               if(Math.abs(this.y - _loc2_.y) < 200)
               {
                  this.belongsToWhom(_loc2_);
               }
            }
         }
         if(this._count++ >= 200)
         {
            _count = 0;
            this.remove();
         }
      }
      
      public function remove() : void
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         var _loc1_:int = int(ThreeKingdoms._gameWorld._otherList.indexOf(this));
         if(_loc1_ != -1)
         {
            ThreeKingdoms._gameWorld._otherList.splice(_loc1_,1);
            delete global[this];
         }
      }
      
      private function belongsToWhom(param1:Hero) : void
      {
         if(this._flag)
         {
            if(param1.body)
            {
               if(!this.collipse.hitTestObject(param1.collipse))
               {
                  return;
               }
               if(HitTest.complexHitTestObject(this.collipse,param1.collipse))
               {
                  this._owner = param1;
                  this.cureHero();
                  this._flag = false;
                  TweenMax.to(this,0.8,{
                     "y":this.y - 100,
                     "alpha":0,
                     "onComplete":this.remove()
                  });
               }
            }
         }
      }
      
      protected function cureHero() : void
      {
         this._owner.healing(this._cureNumber);
         if(Boolean(this._owner.pet) && !this._owner.pet.isDead())
         {
            this._owner.pet.healing(this._cureNumber);
         }
      }
      
      override public function fallOffScreen() : void
      {
         if(this.y >= 1500)
         {
            this.remove();
         }
      }
   }
}

