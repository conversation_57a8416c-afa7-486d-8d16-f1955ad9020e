package actors.pets
{
   import actors.Properties;
   import base.BuffEffect;
   import flash.events.Event;
   import util.GameUtility;
   
   public class Pet5 extends Pet
   {
      
      public function Pet5()
      {
         super();
         this.gotoAndStop("休息");
         this._walkSpeed = 5;
         this._runSpeed = 10;
         this._properties = new Properties(this);
         this._properties.setMoveSpeed(this._walkSpeed);
         this._buffEffect = new BuffEffect(this);
         this._object._maxPatrolView = 500;
         this._object._alertRange = 500;
         this._attackProbablity = 60;
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddToStageHandler);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.onRemovedFromStageHandler);
         this._roleName = "大地之神";
         this._roleType = "懒惰";
         this._roleFrameID = 5;
         this._description = "    土灵的成熟形态，相传神农所养灵兽。用于寻找天下灵药之用。";
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0,-2],
            "attackInterval":4,
            "attackType":"physical"
         };
      }
      
      override public function onAddToStageHandler(param1:Event) : void
      {
         super.onAddToStageHandler(param1);
      }
      
      override public function initAR() : void
      {
         this._properties.setAttackPower(this._initAttackPower + 61 + (this._properties.getLevel() - 15) * 6);
         this._properties.setResistance(this._initResistance + 49 + (this._properties.getLevel() - 15) * 9);
      }
      
      override public function onRemovedFromStageHandler(param1:Event) : void
      {
         super.onRemovedFromStageHandler(param1);
      }
      
      override public function levelUP(param1:int = 29) : void
      {
         if(!this._isFirstTimeToInit)
         {
            this._properties.destroy();
         }
         this._isFirstTimeToInit = false;
         this._properties.setTotalHealthPoint(200 + (this._properties.getLevel() - 1) * 60);
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(60 + (this._properties.getLevel() - 1) * 12);
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
         this._properties.setAttackPower(this._initAttackPower + 61 + (this._properties.getLevel() - 15) * 6);
         this._properties.setResistance(this._initResistance + 49 + (this._properties.getLevel() - 15) * 9);
         var _loc2_:uint = this._properties.getLevel() / 10;
         this._properties.setHealthRegeneration(_loc2_);
         this._properties.setManaRegeneration(_loc2_);
         this._properties.setTotalExperience((this._properties.getLevel() - 1) * (this._properties.getLevel() - 1) * 120 + 50 * this._properties.getLevel());
         this._properties.initAll();
         this._properties.setTotalHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(this._properties.getTotalManaPoint());
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
      }
      
      override public function getSpecificAttackPowerByType(param1:String) : int
      {
         switch(param1)
         {
            case "攻击1":
               return this._properties.getTotalAttackPower();
            case "攻击2":
               return this._properties.getTotalAttackPower() * 1.5;
            default:
               return 0;
         }
      }
      
      override public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(_attackProbablity))
         {
            if(_skill1CoolDown == 0)
            {
               if(GameUtility.getDistance(this,this._currentAttakTarget) <= 480)
               {
                  this.realseSkill1();
                  _skill1CoolDown = 90;
               }
               else
               {
                  followTarget();
               }
            }
            else if(GameUtility.getDistance(this,this._currentAttakTarget) <= 120)
            {
               this.attack();
            }
            else
            {
               followTarget();
            }
         }
         else
         {
            followTarget();
         }
      }
      
      override public function realseSkill1() : void
      {
         if(this._properties.getCurrentManaPoint() > 25)
         {
            steer();
            _vx = 0;
            this._lastHit = "攻击2";
            this.gotoAndStop("攻击2");
            setYourDaddysTime(35);
            newAttackID();
            this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 25);
         }
      }
      
      override public function attack() : void
      {
         steer();
         _vx = 0;
         this._lastHit = "攻击1";
         this.gotoAndStop("攻击1");
         setYourDaddysTime(20);
         newAttackID();
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2";
      }
   }
}

