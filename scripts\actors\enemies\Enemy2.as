package actors.enemies
{
   import actors.Enemy;
   import util.UString;
   
   public class Enemy2 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy2(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 400;
         this._walkSpeed = 3;
         this._probability = 0.3;
         this._object._jumpCount = 0;
         this._object._alertRange = 400;
         this._object._attackRange = 120;
         if(ThreeKingdoms._instance._currentLevel == 1)
         {
            this._currentHealthPoint = 130;
            this._totalHealthPoint = 130;
            this._experience = 10;
            this._resistance = 3;
            this._attackProbablity = 35;
            this._goldPrice = 3 + Math.round(Math.random() * 5);
            this._attackBackInfomationDictionary["攻击1"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[2,-1.5],
               "attackInterval":4,
               "attackPower":22,
               "attackType":"physical"
            };
         }
         else if(ThreeKingdoms._instance._currentLevel == 2)
         {
            this._currentHealthPoint = 150;
            this._totalHealthPoint = 150;
            this._experience = 25;
            this._resistance = 3;
            this._attackProbablity = 38;
            this._goldPrice = 3 + Math.round(Math.random() * 5);
            this._attackBackInfomationDictionary["攻击1"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[2,-1.5],
               "attackInterval":4,
               "attackPower":22,
               "attackType":"physical"
            };
         }
         this._fallEquipmentsList = [{
            "id":1,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":9,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":10,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":35,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":36,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":27,
            "qualityID":[1],
            "type":"equipment"
         },{
            "id":72,
            "qualityID":[0],
            "type":"equipment"
         },{
            "id":73,
            "qualityID":[0,1,2],
            "type":"equipment"
         },{
            "id":62,
            "qualityID":[0,1],
            "type":"equipment"
         }];
      }
      
      override public function update() : void
      {
         super.update();
      }
   }
}

