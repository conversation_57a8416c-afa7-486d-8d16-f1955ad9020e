package actors.memory
{
   import actors.Hero;
   import actors.equipments.Equipment;
   import actors.user.User;
   import calista.utils.Base64;
   import flash.net.SharedObject;
   import flash.net.registerClassAlias;
   import flash.utils.ByteArray;
   import util.GameUtility;
   import util.KeyList;
   
   public class Memory
   {
      
      public var _neeUI:Boolean = false;
      
      private var tk:ThreeKingdoms;
      
      private var so:SharedObject;
      
      private var _memory0:Object = {};
      
      private var _memory1:Object;
      
      private var _memory2:Object;
      
      private var title:String;
      
      private var heroName1:Hero;
      
      private var heroName2:Hero;
      
      public var _saveIndex:uint = 7;
      
      public function Memory()
      {
         super();
         this.tk = ThreeKingdoms._instance;
         registerClassAlias("actors.equipments.Equipment",Equipment);
         registerClassAlias("actors.user.User",User);
         this.so = SharedObject.getLocal("SF_ThreeKingdoms");
      }
      
      public function getMemory() : void
      {
         ThreeKingdoms._instance.isFirstInit = true;
         var _loc1_:* = this._memory0;
         if(_loc1_)
         {
            this.tk._user1.setSavingObject(_loc1_._user1Obj);
            this.tk._user2.setSavingObject(_loc1_._user2Obj);
            this.tk._currentMaxLevel = _loc1_._currentMaxLevel;
            if(_loc1_.hideSuit == undefined && _loc1_.hideSuit == null)
            {
               this.tk._HideSuit = false;
            }
            else
            {
               this.tk._HideSuit = _loc1_.hideSuit;
            }
            if(this.tk._gameMode == 1)
            {
               this.newHero(this.tk._user1._controlPlayer + 1,this.tk._user1._roleID);
            }
            if(this.tk._gameMode == 2)
            {
               this.newHero(this.tk._user1._controlPlayer + 1,this.tk._user1._roleID);
               this.newHero(this.tk._user2._controlPlayer + 1,this.tk._user2._roleID);
            }
         }
      }
      
      public function memoryValue(param1:Object) : *
      {
         this._memory0 = param1;
      }
      
      private function newHero(param1:int, param2:int) : void
      {
         this.tk["_role" + param2] = GameUtility.getObject("actors.roles.Role" + param2);
         this.tk["_role" + param2].x = 300;
         this.tk["_role" + param2].y = 100;
         this.tk["_role" + param2].name = "";
         this.tk["_role" + param2].setPlayer(this.tk["_user" + param1]);
         this.tk["_role" + param2].setKeyList(KeyList["_keyList" + param1]);
         this["heroName" + param1] = this.tk["_role" + param2];
         this.tk["_role" + param2].setPets();
         this.tk["_role" + param2]._properties.setLevel(User(this.tk["_user" + param1])._protectedObject.currentLevel - 1);
         this.tk["_role" + param2]._properties.setGold(int(User(this.tk["_user" + param1])._protectedObject.gold));
         this.tk["_role" + param2]._properties._protectData.currentExperience = User(this.tk["_user" + param1])._protectedObject.currentExperience;
         this.tk["_role" + param2]._properties._protectData.foreverCrit = User(this.tk["_user" + param1])._protectedObject.foreverCrit;
         this.tk["_role" + param2]._properties._protectData.foreverMiss = User(this.tk["_user" + param1])._protectedObject.foreverMiss;
         this.tk["_role" + param2]._properties._protectData.foreverResistance = User(this.tk["_user" + param1])._protectedObject.foreverResistance;
         this.tk["_role" + param2]._properties._protectData.foreverAttackPower = User(this.tk["_user" + param1])._protectedObject.foreverAttackPower;
      }
      
      public function setMemory() : void
      {
         var _loc1_:String = null;
         var _loc2_:String = null;
         if(this.heroName1 == null)
         {
            this.heroName1 = Hero(ThreeKingdoms._gameWorld._heroes[0]);
         }
         if(this.tk._gameMode == 2)
         {
            if(this.heroName2 == null)
            {
               this.heroName2 = Hero(ThreeKingdoms._gameWorld._heroes[1]);
            }
         }
         if(this.tk._role1)
         {
            this.tk._role1.getPetObject();
         }
         if(this.tk._role2)
         {
            this.tk._role2.getPetObject();
         }
         if(this.tk._role3)
         {
            this.tk._role3.getPetObject();
         }
         if(this.heroName1._roleName == "LiuBei")
         {
            _loc1_ = "刘备";
         }
         else if(this.heroName1._roleName == "GuanYu")
         {
            _loc1_ = "关羽";
         }
         else
         {
            _loc1_ = "张飞";
         }
         if(this.tk._gameMode == 1)
         {
            this.title = _loc1_ + ": LV" + this.heroName1.getPlayer()._protectedObject.currentLevel + "";
         }
         else
         {
            if(this.heroName2._roleName == "LiuBei")
            {
               _loc2_ = "刘备";
            }
            else if(this.heroName2._roleName == "GuanYu")
            {
               _loc2_ = "关羽";
            }
            else
            {
               _loc2_ = "张飞";
            }
            this.title = _loc1_ + ": LV" + this.heroName1.getPlayer()._protectedObject.currentLevel + "、" + _loc2_ + ": LV" + this.heroName2.getPlayer()._protectedObject.currentLevel + "";
         }
         this._memory0._user1Obj = this.tk._user1.getSavingObject();
         this._memory0._user2Obj = this.tk._user2.getSavingObject();
         this._memory0._currentMaxLevel = this.tk._currentMaxLevel;
         this._memory0.hideSuit = this.tk._HideSuit;
         var _loc3_:ByteArray = new ByteArray();
         _loc3_.writeObject(this._memory0);
         _loc3_.compress();
         var _loc4_:String = Base64.encodeByteArray(_loc3_);
         ThreeKingdoms.serviceHold.saveData(this.title,_loc4_,this._neeUI,this._saveIndex);
      }
   }
}

