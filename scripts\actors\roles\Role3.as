package actors.roles
{
   import actors.Hero;
   import actors.SoundManager;
   import actors.info.RoleInfo;
   import event.GameEvent;
   import flash.events.Event;
   import flash.utils.getTimer;
   
   public class Role3 extends Hero
   {
      
      public static var _isBloodThirsty:Boolean = false;
      
      public function Role3()
      {
         super();
         _roleName = "ZhangFei";
         _roleType = "Warrior";
         this._attackBackInfomationDictionary["攻击1"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0,-3],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击2"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-3],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击3"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[1,-3],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击4"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[5,-3],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["跑攻"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["跳攻"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,-4],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击5"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[2,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击6"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[5,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["噬血"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击9"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[0.5,-2],
            "attackInterval":999,
            "attackType":"physical"
         };
         this._attackBackInfomationDictionary["攻击8"] = {
            "hitMaxCount":1,
            "attackBackVelocity":[3,-2],
            "attackInterval":1,
            "attackType":"physical"
         };
      }
      
      override public function onAddToStageHandler(param1:Event) : void
      {
         this.initProperties();
         _world._eventManager.addEventListener(GameEvent.ENEMY_IS_UNDER_ATTACK,this.onRole3IsBlood);
      }
      
      override public function removeToStageHandler(param1:Event) : void
      {
         _world._eventManager.removeEventListener(GameEvent.ENEMY_IS_UNDER_ATTACK,this.onRole3IsBlood);
      }
      
      private function onRole3IsBlood(param1:GameEvent) : void
      {
         if(Role3._isBloodThirsty && this.isNormalHit())
         {
            this.healing(Math.round(Number(param1._data[0]) * (_player.getAttackValueByActionType("噬血7") * 0.01 + _properties.getBloodPoint())));
         }
      }
      
      override public function getSpecificAttackPowerByType(param1:String) : int
      {
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc7_:Number = NaN;
         var _loc8_:Number = NaN;
         var _loc2_:Number = 0;
         switch(param1)
         {
            case "攻击1":
               return Math.round(this._properties.getTotalAttackPower() + Math.random() * 10);
            case "攻击2":
               return Math.round(this._properties.getTotalAttackPower() + Math.random() * 10);
            case "攻击3":
               return Math.round(this._properties.getTotalAttackPower() * 1.2 + Math.random() * 10);
            case "攻击4":
               return Math.round(this._properties.getTotalAttackPower() * 1.3 + Math.random() * 10);
            case "跑攻":
               return Math.round(this._properties.getTotalAttackPower() * 1.4 + Math.random() * 10);
            case "跳攻":
               return Math.round(this._properties.getTotalAttackPower() * 1.5 + Math.random() * 10);
            case "攻击5":
               _loc2_ = _player.getAttackValueByActionType(param1);
               return (_loc2_ * 0.01 + 1) * _properties.getTotalAttackPower();
            case "攻击6":
               _loc2_ = _player.getAttackValueByActionType(param1);
               return (_loc2_ * 0.01 + 1) * _properties.getTotalAttackPower();
            case "噬血":
               break;
            case "攻击8":
               _loc2_ = _player.getAttackValueByActionType(param1);
               return (_loc2_ * 0.01 + 1) * _properties.getTotalAttackPower();
            case "攻击9":
               _loc2_ = _player.getAttackValueByActionType(param1);
               return (_loc2_ * 0.01 + 1) * _properties.getTotalAttackPower();
         }
         return 0;
      }
      
      override public function runAttack() : void
      {
         if(!this.isInSky())
         {
            if(this._properties.getCurrentManaPoint() > 0)
            {
               this._hitTimes = 1;
               this._lastHit = "跑攻";
               this.gotoAndStop("跑攻");
               SoundManager.play("Role3_hit1");
               this.newAttackID();
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - 0);
            }
            else
            {
               this.gotoAndStop("攻击1");
               SoundManager.play("Role3_hit1");
               this._lastHit = "攻击1";
               this.newAttackID();
            }
         }
      }
      
      override public function levelUP(param1:int = 1) : void
      {
         if(!this._isFirstTimeToInit)
         {
            this._properties.destroy();
         }
         this._isFirstTimeToInit = false;
         this._properties.setTotalHealthPoint(110 + 110 * (this._properties.getLevel() - 1));
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(50 + 15 * (this._properties.getLevel() - 1));
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
         this._properties.setAttackPower(10 + Math.round(Math.random() * 4) + 6 * (_properties.getLevel() - 1));
         this._properties.setTotalExperience(((_properties.getLevel() - 1) * (_properties.getLevel() - 1) * 100 + 50 * _properties.getLevel()) * 1.5);
         this._properties.setCrit(0.01 + 0.001 * this._properties.getLevel());
         _properties.initAll();
         this._properties.setTotalHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setCurrentHealthPoint(this._properties.getTotalHealthPoint());
         this._properties.setTotalManaPoint(this._properties.getTotalManaPoint());
         this._properties.setCurrentManaPoint(this._properties.getTotalManaPoint());
      }
      
      override public function realseSkill1() : void
      {
         if(this.getPlayer()._learnSkill[0] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= _player._learnSkill[0] * 5 + 5)
         {
            if(this.currentLabel != "裂地斩")
            {
               this.gotoAndStop("裂地斩");
               this._lastHit = "攻击5";
               this._hitTimes = 0;
               this._times = 12;
               this.newAttackID();
               setYourDaddysTime(35);
               SoundManager.play("Role3_hit6");
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - (_player._learnSkill[0] * 5 + 5));
            }
         }
      }
      
      override public function realseSkill2() : void
      {
         if(this.getPlayer()._learnSkill[1] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= _player._learnSkill[1] * 4 + 4)
         {
            if(this.currentLabel != "穿心刺")
            {
               this.gotoAndStop("穿心刺");
               this._lastHit = "攻击6";
               this._hitTimes = 0;
               this._times = 10;
               this.newAttackID();
               SoundManager.play("Role3_hit6");
               setYourDaddysTime(20);
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - (_player._learnSkill[1] * 4 + 4));
            }
         }
      }
      
      override public function realseSkill3() : void
      {
         if(this.getPlayer()._learnSkill[2] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack() || _isBloodThirsty)
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= _player._learnSkill[2] * 20)
         {
            if(this.currentLabel != "噬血")
            {
               _vx = 0;
               this.gotoAndStop("噬血");
               this._hitTimes = 0;
               this._times = 8;
               this._lastHit = "噬血";
               this.newAttackID();
               this._buffEffect.add([{
                  "name":"blood",
                  "time":30 * 10
               }]);
               SoundManager.play("Role3_hit7");
               Role3._isBloodThirsty = true;
               setYourDaddysTime(58);
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - _player._learnSkill[2] * 20);
            }
         }
      }
      
      override public function realseSkill4() : void
      {
         if(this.getPlayer()._learnSkill[3] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         if(this._properties.getCurrentManaPoint() >= this.getPlayer()._learnSkill[3] * 10 + 30)
         {
            if(this.currentLabel != "技能4")
            {
               _vx = 0;
               this.gotoAndStop("技能4");
               this._lastHit = "攻击8";
               this._hitTimes = 0;
               this._times = 12;
               this.newAttackID();
               SoundManager.play("Role3_hit8");
               setYourDaddysTime(33);
               this._properties.setCurrentManaPoint(this._properties.getCurrentManaPoint() - (this.getPlayer()._learnSkill[3] * 10 + 30));
            }
         }
      }
      
      override public function realseSkill5() : void
      {
         var _loc1_:RoleInfo = null;
         if(this.getPlayer()._learnSkill[4] == 0)
         {
            return;
         }
         if(this.isAttacking() || this.isUnderAttack())
         {
            return;
         }
         _loc1_ = ThreeKingdoms._instance._gameInfo.getRoleInfoByPlayer(this._player) as RoleInfo;
         if(_loc1_.isUniqueReady())
         {
            if(this.currentLabel != "无双")
            {
               _vx = 0;
               _vy = 0;
               this.gotoAndStop("无双");
               this._lastHit = "攻击9";
               this._hitTimes = 0;
               this._times = 12;
               setYourDaddysTime(119);
               SoundManager.play("Role3_hit9");
               _loc1_._uniquePointObject.uniquePoint = 0;
            }
         }
      }
      
      override public function attack() : void
      {
         this._timer = getTimer();
         if(_times <= 0)
         {
            if(!this.isInSky())
            {
               if(!this.isRunning() && (!this.isAttacking() || this.isNormalHit()))
               {
                  if(this._hitTimes == 4)
                  {
                     this._times = 18;
                  }
                  else if(_hitTimes == 3)
                  {
                     this._times = 15;
                  }
                  else
                  {
                     this._times = 8;
                  }
                  if(this._timer - _lastTime > 1000)
                  {
                     this._hitTimes = 1;
                  }
                  else if(++_hitTimes > 4)
                  {
                     this._hitTimes = 1;
                  }
                  if(this._hitTimes <= 2)
                  {
                     SoundManager.play("Role3_hit1");
                  }
                  else if(this._hitTimes <= 4 && this._hitTimes >= 2)
                  {
                     SoundManager.play("Role3_hit3");
                  }
                  _vx = 0;
                  this.gotoAndStop("攻击" + this._hitTimes);
                  this._lastHit = "攻击" + this._hitTimes;
                  if(this._lastHit == "攻击4")
                  {
                     SoundManager.play("Role3_hit4");
                     setYourDaddysTime(29);
                  }
                  this.newAttackID();
               }
               else if(this.isRunning() && !this.isAttacking())
               {
                  this.gotoAndStop("跑攻");
                  this._lastHit = "跑攻";
                  SoundManager.play("Role3_hit1");
                  this.newAttackID();
                  this._hitTimes = 0;
               }
            }
            else
            {
               this._times = 15;
               this._lastHit = "跳攻";
               this.gotoAndStop("跳攻");
               SoundManager.play("Role3_hit3");
               this.newAttackID();
               this._hitTimes = 0;
            }
         }
         this._lastTime = this._timer;
      }
      
      override public function update() : void
      {
         super.update();
         if(!this.isDead() && this.currentLabel == "跳攻" && !isInSky())
         {
            this._vx = 0;
         }
      }
      
      override public function isRunning() : Boolean
      {
         return this.currentLabel == "跑";
      }
      
      override public function isAttacking() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4" || this.currentLabel == "裂地斩" || this.currentLabel == "跳攻" || this.currentLabel == "技能4" || this.currentLabel == "噬血" || this.currentLabel == "穿心刺" || this.currentLabel == "无双" || this.currentLabel == "跑攻";
      }
      
      override public function isNormalHit() : Boolean
      {
         return this.currentLabel == "攻击1" || this.currentLabel == "攻击2" || this.currentLabel == "攻击3" || this.currentLabel == "攻击4" || this.currentLabel == "跑攻" || this.currentLabel == "跳攻";
      }
      
      override public function isJumping() : Boolean
      {
         return this.currentLabel == "跳" || this.currentLabel == "二级跳" || this.currentLabel == "落地";
      }
   }
}

