package com.adobe.serialization.json
{
   public final class AJ<PERSON>NToken
   {
      
      internal static const token:AJ<PERSON>NToken = new AJSONToken();
      
      public var type:int;
      
      public var value:Object;
      
      public function AJSONToken(param1:int = -1, param2:Object = null)
      {
         super();
         this.type = param1;
         this.value = param2;
      }
      
      internal static function create(param1:int = -1, param2:Object = null) : AJSONToken
      {
         token.type = param1;
         token.value = param2;
         return token;
      }
   }
}

