package actors.holidays
{
   public final class HolidayManager
   {
      
      private static var _instance:Holiday<PERSON>anager;
      
      private var icon:ChildrenGiftIcon;
      
      private var _wechaticon:WeChatGiftIcon;
      
      public var is20130601Over:Boolean = false;
      
      public var is20130627Over:Boolean = false;
      
      public function HolidayManager()
      {
         super();
         if(_instance)
         {
            throw new Error("节假日活动管理类，为单例类，请使用getInstance()");
         }
         _instance = this;
      }
      
      public static function getInstance() : HolidayManager
      {
         if(!_instance)
         {
            new HolidayManager();
         }
         return _instance;
      }
      
      public function Init() : void
      {
         if(!ThreeKingdoms._instance._user1._isGet2013ChildrenGift)
         {
            if(!this.is20130601Over)
            {
               this.icon = new ChildrenGiftIcon();
               this.icon.name = "childrengifticon";
               this.icon.x = 370 + 38.4 + 15;
               this.icon.y = 200;
               this.icon.buttonMode = true;
               ThreeKingdoms._instance._town.addChild(this.icon);
            }
         }
         if(!ThreeKingdoms._instance._user1._isGet2013WeChatGift)
         {
            if(!this.is20130627Over)
            {
               this._wechaticon = new WeChatGiftIcon();
               this._wechaticon.name = "wechatgifticon";
               this._wechaticon.x = 370 + 38.4 + 15 + 65;
               this._wechaticon.y = 200;
               this._wechaticon.buttonMode = true;
               ThreeKingdoms._instance._town.addChild(this._wechaticon);
            }
         }
      }
      
      public function destory() : void
      {
         if(ThreeKingdoms._instance._user1._isGet2013ChildrenGift || this.is20130601Over)
         {
            if(ThreeKingdoms._instance._town.contains(this.icon))
            {
               ThreeKingdoms._instance._town.removeChild(this.icon);
               this.icon = null;
            }
         }
         if(ThreeKingdoms._instance._user1._isGet2013WeChatGift || this.is20130627Over)
         {
            if(ThreeKingdoms._instance._town.contains(this._wechaticon))
            {
               ThreeKingdoms._instance._town.removeChild(this._wechaticon);
               this._wechaticon = null;
            }
         }
      }
   }
}

