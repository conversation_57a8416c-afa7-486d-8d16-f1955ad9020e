package actors.enemies
{
   import actors.Enemy;
   import util.GameUtility;
   import util.UString;
   
   public class Enemy10 extends Enemy
   {
      
      private const _0:String = UString.ddb416368e17ec1e([52,51,57,57,46,99,111,109]);
      
      private const _1:String = UString.ddb416368e17ec1e([109,121,52,51,57,57,46,99,111,109]);
      
      private const _2:String = UString.ddb416368e17ec1e([102,102,49,51,48,46,99,111,109]);
      
      private const _3:String = UString.ddb416368e17ec1e([52,51,57,57,112,107,46,99,111,109]);
      
      private const _4:String = UString.ddb416368e17ec1e([47]);
      
      private const _5:String = UString.ddb416368e17ec1e([46]);
      
      private const _6:String = UString.ddb416368e17ec1e([58]);
      
      private const _URL_LIST:Array = new Array(this._0,this._1,this._2,this._3);
      
      public function Enemy10(param1:Number = 0, param2:Number = 0)
      {
         super(param1,param2);
         this._object._maxPatrolView = 400;
         this._walkSpeed = 3;
         this._object._alertRange = 400;
         this._object._attackRange = 150;
         this._currentHealthPoint = 2500;
         this._totalHealthPoint = 2500;
         this._attackProbablity = 60;
         this._resistance = 50;
         this._experience = 250;
         this._probability = 0.3;
         this._goldPrice = 40 + Math.round(Math.random() * 15);
         if(ThreeKingdoms._instance._currentLevel == 4)
         {
            this._walkSpeed = 1.8;
            this._attackProbablity = 38;
            this._currentHealthPoint = 8000;
            this._totalHealthPoint = 8000;
            this._resistance = 40;
            this._experience = 300;
            this._probability = 0.5;
            this._goldPrice = 50 + Math.round(Math.random() * 100);
            this._fallEquipmentsList = [{
               "id":1,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":2,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":64,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":11,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":12,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":13,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":14,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":74,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":75,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":76,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":77,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":28,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":37,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":38,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":39,
               "qualityID":[1],
               "type":"equipment"
            },{
               "id":40,
               "qualityID":[1],
               "type":"equipment"
            }];
            this._attackBackInfomationDictionary["攻击1"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[0.5,-1.5],
               "attackInterval":4,
               "attackPower":80 + Math.round(Math.random() * 35),
               "attackType":"physical"
            };
            this._attackBackInfomationDictionary["攻击2"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[3,-1.5],
               "attackInterval":4,
               "attackPower":80 + Math.round(Math.random() * 35),
               "attackType":"physical"
            };
         }
         else if(ThreeKingdoms._instance._currentLevel == 5 || ThreeKingdoms._instance._currentLevel == 10)
         {
            this._fallEquipmentsList = [{
               "id":78,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":79,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":80,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":81,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":15,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":16,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":17,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":18,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":41,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":42,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":43,
               "qualityID":[0],
               "type":"equipment"
            },{
               "id":44,
               "qualityID":[0],
               "type":"equipment"
            }];
            this._attackBackInfomationDictionary["攻击1"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[0.5,-1.5],
               "attackInterval":4,
               "attackPower":98 + Math.round(Math.random() * 30),
               "attackType":"physical"
            };
            this._attackBackInfomationDictionary["攻击2"] = {
               "hitMaxCount":1,
               "attackBackVelocity":[0.5,-1.5],
               "attackInterval":4,
               "attackPower":98 + Math.round(Math.random() * 30),
               "attackType":"physical"
            };
         }
      }
      
      override public function update() : void
      {
         super.update();
      }
      
      override public function startAttacking() : void
      {
         if(GameUtility.getRandomNumber(20))
         {
            wait();
         }
         else if(GameUtility.getRandomNumber(_attackProbablity))
         {
            if(GameUtility.getRandomNumber(50))
            {
               steer();
               _vx = 0;
               setYourDaddysTime(17);
               this._lastHit = "攻击1";
               this.gotoAndStop("攻击1");
               newAttackID();
            }
            else
            {
               steer();
               _vx = 0;
               setYourDaddysTime(33);
               this._lastHit = "攻击2";
               this.gotoAndStop("攻击2");
               newAttackID();
            }
         }
         else
         {
            moveTowardHero();
         }
      }
   }
}

