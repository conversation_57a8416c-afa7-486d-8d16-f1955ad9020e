package actors
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import util.GameUtility;
   
   public class <PERSON><PERSON> extends MovieClip
   {
      
      protected var _hero:Hero;
      
      protected var _enemy:Enemy;
      
      protected var _velocity:int;
      
      protected var _counter:int = 0;
      
      protected var _omega:int = 8;
      
      public function Missle(param1:Enemy = null, param2:Hero = null)
      {
         super();
         this._enemy = param1;
         this._hero = param2;
         this._velocity = 12 + int(Math.random() * 4);
         this.addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
      }
      
      private function onAddedToStageHandler(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onAddedToStageHandler);
      }
      
      public function update() : void
      {
         var _loc1_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         if(this._hero)
         {
            _loc1_ = this.x - this._hero.x;
            _loc2_ = this.y - this._hero.y;
            _loc3_ = (270 + Math.atan2(_loc2_,_loc1_) * 180 / Math.PI) % 360;
            _loc4_ = (_loc3_ - this.rotation + 360) % 360;
            _loc5_ = _loc4_ <= 180 ? 1 : -1;
            this.rotation = _loc4_ < 180 && _loc4_ > this._omega || _loc4_ > 180 && 360 - _loc4_ > this._omega ? this.rotation + this._omega * _loc5_ : _loc3_;
            this.x += this._velocity * Math.sin(this.rotation * Math.PI / 180);
            this.y -= this._velocity * Math.cos(this.rotation * Math.PI / 180);
         }
         else
         {
            this.destroy();
         }
         if(this._hero)
         {
            this.detectCollision();
         }
         ++this._counter;
         if(this._counter > 86)
         {
            this.destroy();
            this._counter = 0;
         }
      }
      
      public function detectCollision() : void
      {
      }
      
      public function destroy() : void
      {
         if(this)
         {
            GameUtility.clearDisplayList(this);
         }
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         var _loc1_:int = int(ThreeKingdoms._gameWorld._missles.indexOf(this));
         if(_loc1_ != -1)
         {
            ThreeKingdoms._gameWorld._missles.splice(_loc1_,1);
         }
      }
   }
}

